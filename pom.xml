<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>me.supernova</groupId>
    <artifactId>NovaFramework</artifactId>
    <version>${revision}</version>
    <name>NovaFramework</name>
    <description>NovaFramework</description>

    <packaging>pom</packaging>
    <modules>
        <module>nova-common</module>
        <module>nova-dashboard</module>
    </modules>

    <properties>
        <revision>0.0.1-SNAPSHOT</revision>

        <spring-boot.version>3.5.0</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>21</java.version>

        <springdoc.version>2.7.0</springdoc.version>
        <satoken.version>1.39.0</satoken.version>
        <mybatis-plus.version>3.5.10</mybatis-plus.version>
        <hutool.version>5.8.31</hutool.version>
        <redisson.version>3.37.0</redisson.version>
        <snailjob.version>1.1.2</snailjob.version>
        <lombok.version>1.18.34</lombok.version>

        <!-- OSS 配置 -->
        <aws.sdk.version>2.28.22</aws.sdk.version>
        <aws.crt.version>0.31.3</aws.crt.version>
        <!-- 阿里的 transmittable-thread-local 用来做上下文线程传递的 -->
        <ttl.version>2.14.5</ttl.version>
        <!--  mybatis plus 代码生成  -->
        <velocity.version>2.4.1</velocity.version>
        <!-- 前身是阿里的 easyExcel -->
        <fastexcel.version>1.2.0</fastexcel.version>
        <!--ip 定位离线库-->
        <ip2region.version>2.7.0</ip2region.version>

        <!-- 插件版本 -->
        <maven-compiler-plugin.verison>3.11.0</maven-compiler-plugin.verison>
        <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fastexcel.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-core</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!--  AWS SDK for Java 2.x  -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <!-- 使用AWS基于 CRT 的 S3 客户端 -->
            <dependency>
                <groupId>software.amazon.awssdk.crt</groupId>
                <artifactId>aws-crt</artifactId>
                <version>${aws.crt.version}</version>
            </dependency>
            <!-- 基于 AWS CRT 的 S3 客户端的性能增强的 S3 传输管理器 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3-transfer-manager</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- SnailJob Client -->
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-starter</artifactId>
                <version>${snailjob.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-job-core</artifactId>
                <version>${snailjob.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.16.1</version>
            </dependency>

            <!-- TTL（线程间传递 ThreadLocal，异步执行时上下文传递的解决方案） -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${ttl.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- 离线IP地址定位库 ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>3.5.10</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.verison}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- 统一版本号管理 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>banner*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>
