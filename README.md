# Nova Framework

Nova Framework 是一个基于 Spring Boot 3.4.1 的现代化微服务框架，提供了丰富的功能模块和工具集。

## 技术栈

- JDK 21
- Spring Boot 3.4.1
- Sa-Token 1.39.0
- MyBatis-Plus 3.5.10
- Redisson 3.37.0
- Hutool 5.8.31
- AWS SDK 2.28.22
- SpringDoc OpenAPI 2.7.0

## 项目模块

### nova-common

通用模块集合，包含以下子模块：

- nova-common-core: 核心功能模块
- nova-common-web: Web 相关功能
- nova-common-redis: Redis 工具和配置
- nova-common-mybatis: MyBatis-Plus 配置和工具
- nova-common-satoken: 认证授权模块
- nova-common-excel: Excel 处理工具
- nova-common-oss: 对象存储服务
- nova-common-translation: 多语言翻译支持
- nova-common-encrypt: 加密解密工具
- nova-common-ip: IP 地址工具

### nova-dashboard

系统控制台模块

## 特性

- 基于 Spring Boot 3.x 的现代化架构
- 完善的权限认证系统
- 分布式锁支持
- 分布式任务调度
- 对象存储服务集成
- 多语言支持
- 加密解密工具
- Excel 导入导出
- 统一异常处理
- 统一日志处理

## 环境要求

- JDK 21+
- Maven 3.8+
- Redis 6.0+

## 快速开始

1. 克隆项目
```bash
git clone https://github.com/yourusername/Nova-Framework.git
```

2. 编译项目
```bash
mvn clean install
```

3. 运行项目
```bash
cd nova-dashboard
mvn spring-boot:run
```

## 文档

详细文档请参考各个模块下的 README.md 文件。

## 许可证

[Apache License 2.0](https://www.apache.org/licenses/LICENSE-2.0) 