---
description: 
globs: 
alwaysApply: false
---
# Nova Framework 开发指南

## 项目结构
- `nova-dashboard/`: 主要的 Web 应用模块
- `nova-common/`: 公共工具和组件模块
- `sql/`: 数据库相关 SQL 文件
- `script/`: 项目相关脚本文件

## 技术栈
- 基于 Spring Boot 3.4
- 使用 Lombok 简化代码
- 使用 Swagger 进行 API 文档管理

## 开发规范

### API 开发规范
1. 所有 REST API 接口必须使用 Swagger 注解进行文档化
2. Controller 类应放在对应模块的 `controller` 包下
3. REST API 的 Controller 类应以 `Controller` 结尾

### 代码规范
1. 充分利用 Lombok 注解简化代码
   - 使用 `@Data` 生成 getter/setter
   - 使用 `@Builder` 实现建造者模式
   - 使用 `@Slf4j` 添加日志支持
2. 所有的数据库变更都应该在 `sql` 目录下有对应的 SQL 文件
3. 所有模型应该根据模型分类放在 `model` 文件下,接口的入参类型为 bo,出参类型为 vo

### 数据库规范
1. 所有数据库变更脚本都必须放在 `sql` 目录下
2. SQL 文件命名格式：`YYYYMMDD_description.sql`

## 常用命令
```bash
# 构建项目
mvn clean install

# 运行 dashboard 模块
cd nova-dashboard
mvn spring-boot:run
```

## 注意事项
1. 提交代码前请确保通过所有测试
2. 新功能开发请创建新的 feature 分支
3. 重要的配置变更需要在 README.md 中更新
