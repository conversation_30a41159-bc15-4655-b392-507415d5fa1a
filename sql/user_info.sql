-- auto-generated definition
create table user_info
(
    user_id              bigserial
        primary key,
    uuid                 varchar(32),
    game_name            varchar(100)                             not null,
    game_version         varchar(20)                              not null,
    country_code         varchar(2),
    language_code        varchar(5)                               not null,
    timezone_utc         integer        default 0                 not null,
    device_model         varchar(100),
    manufacturer         varchar(50)                              not null,
    os_version           varchar(255)                             not null,
    platform             varchar(10)                              not null,
    resolution           varchar(20),
    dpr                  numeric(3, 2),
    telecom_operators    varchar(100),
    package_name         varchar(100)                             not null,
    store                varchar(50)                              not null,
    idfv                 varchar(100),
    idfa                 varchar(100),
    gps_adid             varchar(100),
    adjust_adid          varchar(100),
    no_ads               boolean        default false             not null,
    total_payment_amount numeric(10, 2) default 0,
    payment_count        integer        default 0,
    create_by            bigint,
    create_time          timestamp      default CURRENT_TIMESTAMP not null,
    update_by            bigint,
    update_time          timestamp      default CURRENT_TIMESTAMP not null,
    last_login_time      timestamp      default CURRENT_TIMESTAMP not null,
    ip                   varchar(64),
    last_ip              varchar(64),
    last_version         varchar(50),
    ip_country_code      varchar(20),
    device_country_code  varchar(20)
);

comment on table user_info is '设备信息表';

comment on column user_info.user_id is '主键ID';

comment on column user_info.uuid is '客户端生成的唯一标识符';

comment on column user_info.game_name is '游戏名称';

comment on column user_info.game_version is '游戏版本';

comment on column user_info.country_code is '国家码(ISO 3166-1)';

comment on column user_info.language_code is '语言码';

comment on column user_info.timezone_utc is '时区';

comment on column user_info.device_model is '设备型号';

comment on column user_info.manufacturer is '制造商';

comment on column user_info.os_version is '系统版本';

comment on column user_info.platform is '设备类型(Android/iOS)';

comment on column user_info.resolution is '分辨率宽度';

comment on column user_info.dpr is '设备像素比';

comment on column user_info.telecom_operators is '运营商';

comment on column user_info.package_name is '包名';

comment on column user_info.store is '下载渠道';

comment on column user_info.idfv is 'iOS设备标识符';

comment on column user_info.idfa is 'iOS广告标识符';

comment on column user_info.gps_adid is 'Google Play Services广告ID';

comment on column user_info.adjust_adid is 'adjustAdid';

comment on column user_info.no_ads is '是否拥有免广告权益';

comment on column user_info.total_payment_amount is '付费总金额';

comment on column user_info.payment_count is '付费次数';

comment on column user_info.create_by is '创建者';

comment on column user_info.create_time is '创建时间';

comment on column user_info.update_by is '更新者';

comment on column user_info.update_time is '更新时间';

comment on column user_info.last_login_time is '最后一次登录时间';

comment on column user_info.last_version is '最近版本';

comment on column user_info.ip_country_code is 'ip对应的国家码';

comment on column user_info.device_country_code is '设备对应的国家码';

alter table user_info
    owner to pixelab;

