-- auto-generated definition
create table event_log
(
    id                  bigserial primary key,
    event_id            varchar(100) not null,
    event_type          varchar(50)  not null,
    event_name          varchar(64)  not null,
    event_time          timestamp    not null,
    event_value         numeric(50, 10),
    event_params        jsonb,
    user_id             bigint,
    uuid                varchar(32),
    idfv                varchar(100),
    idfa                varchar(100),
    adjust_adid         varchar(100),
    game_name           varchar(100) not null,
    game_version        varchar(20)  not null,
    platform            varchar(10)  not null,
    ip_country_code     varchar(20)  not null,
    device_country_code varchar(20)  not null,
    language_code       varchar(5)   not null,
    os_version          varchar(255),
    ip                  varchar(64),
    create_time         timestamp default CURRENT_TIMESTAMP,
    update_time         timestamp default CURRENT_TIMESTAMP
);

comment on table event_log is '游戏事件表';

comment on column event_log.id is '主键ID';

comment on column event_log.event_id is '事件ID';

comment on column event_log.event_type is '事件类型(generalEvent/adEvent/purchaseEvent/sessionEvent/resourceEvent)';

comment on column event_log.event_time is '事件发生时间';

comment on column event_log.event_value is '事件数值(如付费金额)';

comment on column event_log.user_id is '用户ID';

comment on column event_log.uuid is '客户端生成的唯一标识符';

comment on column event_log.idfv is 'iOS设备标识符';

comment on column event_log.idfa is 'iOS广告标识符';

comment on column event_log.adjust_adid is 'adjust广告ID';

comment on column event_log.game_name is '游戏名称';

comment on column event_log.game_version is '游戏版本';

comment on column event_log.platform is '平台(Android/iOS)';

comment on column event_log.ip_country_code is 'ip国家码';

comment on column event_log.device_country_code is '设备国家码';

comment on column event_log.language_code is '语言码';

comment on column event_log.os_version is '操作系统版本';

comment on column event_log.ip is 'IP地址';

comment on column event_log.create_time is '创建时间';

comment on column event_log.update_time is '更新时间';

create index idx_game_event_user_id
    on event_log (user_id);

create index idx_event_record_event_params
    on event_log using gin (event_params);

create index idx_game_compound_index
    on event_log (event_time, game_name, event_type, event_name);

