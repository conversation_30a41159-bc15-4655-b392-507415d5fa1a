-- auto-generated definition
create table sys_user
(
    user_id      bigserial
        constraint sys_user_pk
            primary key,
    dept_id      bigint,
    user_name    varchar(30)                                not null,
    nick_name    varchar(30)                                not null,
    user_type    varchar(10)  default 'sys_user'::character varying,
    email        varchar(50)  default ''::character varying,
    phone_number varchar(21)  default ''::character varying,
    sex          char         default '0'::bpchar,
    avatar       varchar(1024),
    password     varchar(100) default ''::character varying,
    status       char         default '0'::bpchar,
    del_flag     char         default '0'::bpchar,
    login_ip     varchar(128) default ''::character varying,
    login_date   timestamp,
    create_dept  bigint,
    create_by    bigint,
    create_time  timestamp,
    update_by    bigint,
    update_time  timestamp,
    remark       varchar(500) default NULL::character varying,
    lark_open_id varchar(64)  default ''::character varying not null
);

comment on table sys_user is '用户信息表';

comment on column sys_user.user_id is '用户ID';

comment on column sys_user.dept_id is '部门ID';

comment on column sys_user.user_name is '用户账号';

comment on column sys_user.nick_name is '用户昵称';

comment on column sys_user.user_type is '用户类型（sys_user系统用户）';

comment on column sys_user.email is '用户邮箱';

comment on column sys_user.phone_number is '手机号码';

comment on column sys_user.sex is '用户性别（0男 1女 2未知）';

comment on column sys_user.avatar is '头像地址';

comment on column sys_user.password is '密码';

comment on column sys_user.status is '帐号状态（0正常 1停用）';

comment on column sys_user.del_flag is '删除标志（0代表存在 1代表删除）';

comment on column sys_user.login_ip is '最后登陆IP';

comment on column sys_user.login_date is '最后登陆时间';

comment on column sys_user.create_dept is '创建部门';

comment on column sys_user.create_by is '创建者';

comment on column sys_user.create_time is '创建时间';

comment on column sys_user.update_by is '更新者';

comment on column sys_user.update_time is '更新时间';

comment on column sys_user.remark is '备注';

comment on column sys_user.lark_open_id is '飞书 用户openId';

alter table sys_user
    owner to postgres;

INSERT INTO public.sys_user (dept_id, user_name, nick_name, user_type,
                             password, create_dept, create_by, update_by, remark)
VALUES (1, 'admin', '超级管理员', 'sys_user',
        '$2a$10$1KtJLu7fKQvVtYLEJqjOauV6VmQsgXwZLaeRB0uqjcAcyx5R9Z98a', 103, 1, 1, '管理员');
