create table sys_dict_type
(
    dict_id     serial
        constraint sys_dict_type_pk
            primary key,
    dict_name   varchar(100) default ''::character varying,
    dict_type   varchar(100) default ''::character varying,
    create_dept bigint,
    create_by   bigint,
    create_time timestamp,
    update_by   bigint,
    update_time timestamp,
    remark      varchar(500) default NULL::character varying
);

comment on table sys_dict_type is '字典类型表';

comment on column sys_dict_type.dict_id is '字典主键';

comment on column sys_dict_type.dict_name is '字典名称';

comment on column sys_dict_type.dict_type is '字典类型';

comment on column sys_dict_type.create_dept is '创建部门';

comment on column sys_dict_type.create_by is '创建者';

comment on column sys_dict_type.create_time is '创建时间';

comment on column sys_dict_type.update_by is '更新者';

comment on column sys_dict_type.update_time is '更新时间';

comment on column sys_dict_type.remark is '备注';

alter table sys_dict_type
    owner to postgres;

