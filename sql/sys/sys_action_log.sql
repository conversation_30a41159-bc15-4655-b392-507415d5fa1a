-- auto-generated definition
create table sys_action_log
(
    id               bigserial,
    trace_id         varchar(255) default NULL::character varying,
    description      varchar(255)           not null,
    module           varchar(50)            not null,
    request_host     varchar(255)           not null,
    request_path     varchar(512)           not null,
    request_method   varchar(10)            not null,
    request_headers  jsonb,
    request_param    jsonb,
    request_body     jsonb,
    status_code      integer                not null,
    response_headers jsonb,
    response_body    jsonb,
    time_taken       bigint                 not null,
    ip               varchar(100) default NULL::character varying,
    address          varchar(255) default NULL::character varying,
    browser          varchar(100) default NULL::character varying,
    os               varchar(100) default NULL::character varying,
    status           smallint     default 1 not null,
    error_msg        text,
    create_dept      bigint,
    create_by        bigint,
    create_time      timestamp,
    update_by        bigint,
    update_time      timestamp
);

comment on table sys_action_log is '系统日志表';

comment on column sys_action_log.id is 'ID';

comment on column sys_action_log.trace_id is '链路ID';

comment on column sys_action_log.description is '日志描述';

comment on column sys_action_log.module is '所属模块';

comment on column sys_action_log.request_host is '请求HOST';

comment on column sys_action_log.request_path is '请求路径';

comment on column sys_action_log.request_method is '请求方式';

comment on column sys_action_log.request_headers is '请求头';

comment on column sys_action_log.request_param is '请求参数';

comment on column sys_action_log.request_body is '请求体';

comment on column sys_action_log.status_code is '状态码';

comment on column sys_action_log.response_headers is '响应头';

comment on column sys_action_log.response_body is '响应体';

comment on column sys_action_log.time_taken is '耗时（ms）';

comment on column sys_action_log.ip is 'IP';

comment on column sys_action_log.address is 'IP归属地';

comment on column sys_action_log.browser is '浏览器';

comment on column sys_action_log.os is '操作系统';

comment on column sys_action_log.status is '状态（1：成功；2：失败）';

comment on column sys_action_log.error_msg is '错误信息';

comment on column sys_action_log.create_by is '创建者';

comment on column sys_action_log.create_time is '创建时间';

comment on column sys_action_log.update_by is '更新者';

comment on column sys_action_log.update_time is '更新时间';

alter table sys_action_log
    owner to postgres;

create index idx_log_module
    on sys_action_log (module);

create index idx_log_ip
    on sys_action_log (ip);

create index idx_log_address
    on sys_action_log (address);

create index idx_log_create_time
    on sys_action_log (create_time);

