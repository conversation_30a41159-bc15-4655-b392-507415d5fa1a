create table sys_dept
(
    dept_id     serial
        constraint sys_dept_pk
            primary key,
    parent_id   bigint       default 0,
    ancestors   varchar(500) default ''::character varying,
    dept_name   varchar(30)  default ''::character varying,
    order_num   integer      default 0,
    leader      bigint,
    phone       varchar(11)  default NULL::character varying,
    email       varchar(50)  default NULL::character varying,
    status      char         default '0'::bpchar,
    del_flag    char         default '0'::bpchar,
    create_dept bigint,
    create_by   bigint,
    create_time timestamp,
    update_by   bigint,
    update_time timestamp
);

comment on table sys_dept is '部门表';

comment on column sys_dept.dept_id is '部门ID';

comment on column sys_dept.parent_id is '父部门ID';

comment on column sys_dept.ancestors is '祖级列表';

comment on column sys_dept.dept_name is '部门名称';

comment on column sys_dept.order_num is '显示顺序';

comment on column sys_dept.leader is '负责人';

comment on column sys_dept.phone is '联系电话';

comment on column sys_dept.email is '邮箱';

comment on column sys_dept.status is '部门状态（0正常 1停用）';

comment on column sys_dept.del_flag is '删除标志（0代表存在 1代表删除）';

comment on column sys_dept.create_dept is '创建部门';

comment on column sys_dept.create_by is '创建者';

comment on column sys_dept.create_time is '创建时间';

comment on column sys_dept.update_by is '更新者';

comment on column sys_dept.update_time is '更新时间';

alter table sys_dept
    owner to postgres;

INSERT INTO public.sys_dept (parent_id, ancestors, dept_name, order_num, leader, phone, email, status, del_flag,
                             create_dept, create_by, create_time, update_by, update_time)
VALUES (0, '0', 'WeNova', 0, null, '15888888888', '<EMAIL>', '0', '0', 103, 1, '2025-01-16 05:48:18.615948', null,
        null);

