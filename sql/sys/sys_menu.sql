create table sys_menu
(
    menu_id     serial
        constraint sys_menu_pk
            primary key,
    menu_name   varchar(50) not null,
    parent_id   bigint       default 0,
    order_num   integer      default 0,
    path        varchar(200) default ''::character varying,
    component   varchar(255) default NULL::character varying,
    query_param varchar(255) default NULL::character varying,
    is_frame    char         default '1'::bpchar,
    is_cache    char         default '0'::bpchar,
    menu_type   char         default ''::bpchar,
    visible     char         default '0'::bpchar,
    status      char         default '0'::bpchar,
    perms       varchar(100) default NULL::character varying,
    icon        varchar(100) default '#'::character varying,
    create_dept bigint,
    create_by   bigint,
    create_time timestamp,
    update_by   bigint,
    update_time timestamp,
    remark      varchar(500) default ''::character varying
);

comment on table sys_menu is '菜单权限表';

comment on column sys_menu.menu_id is '菜单ID';

comment on column sys_menu.menu_name is '菜单名称';

comment on column sys_menu.parent_id is '父菜单ID';

comment on column sys_menu.order_num is '显示顺序';

comment on column sys_menu.path is '路由地址';

comment on column sys_menu.component is '组件路径';

comment on column sys_menu.query_param is '路由参数';

comment on column sys_menu.is_frame is '是否为外链（0是 1否）';

comment on column sys_menu.is_cache is '是否缓存（0缓存 1不缓存）';

comment on column sys_menu.menu_type is '菜单类型（M目录 C菜单 F按钮）';

comment on column sys_menu.visible is '显示状态（0显示 1隐藏）';

comment on column sys_menu.status is '菜单状态（0正常 1停用）';

comment on column sys_menu.perms is '权限标识';

comment on column sys_menu.icon is '菜单图标';

comment on column sys_menu.create_dept is '创建部门';

comment on column sys_menu.create_by is '创建者';

comment on column sys_menu.create_time is '创建时间';

comment on column sys_menu.update_by is '更新者';

comment on column sys_menu.update_time is '更新时间';

comment on column sys_menu.remark is '备注';

alter table sys_menu
    owner to postgres;

INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (1, '系统管理', 0, 1, 'system', '', '', '1', '0', 'M', '0', '0', '', 'icon-park-outline:system', 103, 1,
        '2025-01-16 05:48:19.505412', 1, '2025-01-21 10:18:38.129218', '系统管理目录');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (2, '用户管理', 1, 1, 'user', 'system/user/index', '', '1', '0', 'C', '0', '0', 'system:user:list',
        'icon-park-outline:user', 103, 1, '2025-01-16 05:48:19.559211', 1, '2025-01-21 10:19:01.089082',
        '用户管理菜单');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (3, '角色管理', 1, 2, 'role', 'system/role/index', '', '1', '0', 'C', '0', '0', 'system:role:list',
        'icon-park-outline:id-card-h', 103, 1, '2025-01-16 05:48:19.571094', 1, '2025-01-21 10:22:08.095797',
        '角色管理菜单');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (4, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '1', '0', 'C', '0', '0', 'system:menu:list',
        'icon-park-outline:freeze-column', 103, 1, '2025-01-16 05:48:19.580945', 1, '2025-01-21 10:21:10.478735',
        '菜单管理菜单');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (5, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '1', '0', 'C', '0', '0', 'system:dept:list',
        'icon-park-outline:broadcast-one', 103, 1, '2025-01-16 05:48:19.591169', 1, '2025-01-21 10:29:43.091314',
        '部门管理菜单');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (6, '岗位管理', 1, 5, 'post', 'system/post/index', '', '1', '0', 'C', '0', '0', 'system:post:list',
        'icon-park-outline:user-business', 103, 1, '2025-01-16 05:48:19.599422', 1, '2025-01-21 10:25:06.207562',
        '岗位管理菜单');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (7, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '1', '0', 'C', '0', '0', 'system:dict:list',
        'icon-park-outline:transform', 103, 1, '2025-01-16 05:48:19.609559', 1, '2025-01-21 10:35:13.842177',
        '字典管理菜单');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (8, '行为日志', 1, 7, 'log', 'system/log/index', null, '1', '0', 'C', '0', '0', 'system:log:list',
        'icon-park-outline:turn-on', 103, 1, '2025-01-21 10:37:34.048318', 1, '2025-01-21 10:41:10.906396', '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (9, '重置密码', 2, 7, '', '', '', '1', '0', 'F', '0', '0', 'system:user:resetPwd', '#', 103, 1,
        '2025-01-16 05:48:19.841385', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (10, '用户新增', 2, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:user:add', '#', 103, 1,
        '2025-01-16 05:48:19.798182', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (11, '用户修改', 2, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:user:edit', '#', 103, 1,
        '2025-01-16 05:48:19.805398', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (12, '用户查询', 2, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:user:query', '#', 103, 1,
        '2025-01-16 05:48:19.790411', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (13, '用户删除', 2, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:user:remove', '#', 103, 1,
        '2025-01-16 05:48:19.814545', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (14, '用户导出', 2, 5, '', '', '', '1', '0', 'F', '0', '0', 'system:user:export', '#', 103, 1,
        '2025-01-16 05:48:19.824857', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (15, '用户导入', 2, 6, '', '', '', '1', '0', 'F', '0', '0', 'system:user:import', '#', 103, 1,
        '2025-01-16 05:48:19.834109', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (16, '角色新增', 3, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:role:add', '#', 103, 1,
        '2025-01-16 05:48:19.857131', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (17, '角色查询', 3, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:role:query', '#', 103, 1,
        '2025-01-16 05:48:19.848468', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (18, '角色修改', 3, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:role:edit', '#', 103, 1,
        '2025-01-16 05:48:19.867421', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (19, '角色删除', 3, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:role:remove', '#', 103, 1,
        '2025-01-16 05:48:19.875207', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (20, '角色导出', 3, 5, '', '', '', '1', '0', 'F', '0', '0', 'system:role:export', '#', 103, 1,
        '2025-01-16 05:48:19.882048', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (21, '菜单修改', 4, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:menu:edit', '#', 103, 1,
        '2025-01-16 05:48:19.907493', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (22, '菜单新增', 4, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:menu:add', '#', 103, 1,
        '2025-01-16 05:48:19.897098', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (23, '菜单删除', 4, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:menu:remove', '#', 103, 1,
        '2025-01-16 05:48:19.915478', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (24, '菜单查询', 4, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:menu:query', '#', 103, 1,
        '2025-01-16 05:48:19.889528', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (25, '部门新增', 5, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:dept:add', '#', 103, 1,
        '2025-01-16 05:48:19.932180', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (26, '部门修改', 5, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:dept:edit', '#', 103, 1,
        '2025-01-16 05:48:19.940034', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (27, '部门删除', 5, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:dept:remove', '#', 103, 1,
        '2025-01-16 05:48:19.950167', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (28, '部门查询', 5, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:dept:query', '#', 103, 1,
        '2025-01-16 05:48:19.923667', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (29, '岗位修改', 6, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:post:edit', '#', 103, 1,
        '2025-01-16 05:48:19.976123', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (30, '岗位删除', 6, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:post:remove', '#', 103, 1,
        '2025-01-16 05:48:19.983320', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (31, '岗位导出', 6, 5, '', '', '', '1', '0', 'F', '0', '0', 'system:post:export', '#', 103, 1,
        '2025-01-16 05:48:19.992881', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (32, '岗位查询', 6, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:post:query', '#', 103, 1,
        '2025-01-16 05:48:19.960257', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (33, '岗位新增', 6, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:post:add', '#', 103, 1,
        '2025-01-16 05:48:19.969324', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (34, '字典修改', 7, 3, '#', '', '', '1', '0', 'F', '0', '0', 'system:dict:edit', '#', 103, 1,
        '2025-01-16 05:48:20.026758', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (35, '字典查询', 7, 1, '#', '', '', '1', '0', 'F', '0', '0', 'system:dict:query', '#', 103, 1,
        '2025-01-16 05:48:20.003363', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (36, '字典新增', 7, 2, '#', '', '', '1', '0', 'F', '0', '0', 'system:dict:add', '#', 103, 1,
        '2025-01-16 05:48:20.014962', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (37, '字典删除', 7, 4, '#', '', '', '1', '0', 'F', '0', '0', 'system:dict:remove', '#', 103, 1,
        '2025-01-16 05:48:20.036193', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (38, '字典导出', 7, 5, '#', '', '', '1', '0', 'F', '0', '0', 'system:dict:export', '#', 103, 1,
        '2025-01-16 05:48:20.077956', null, null, '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (39, '日志查询', 8, 1, '', '', null, '1', '0', 'F', '0', '0', 'system:log:query', '#', 103, 1,
        '2025-01-21 10:40:53.379213', 1, '2025-01-21 10:40:53.379213', '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (40, '日志删除', 8, 2, '', '', null, '1', '0', 'F', '0', '0', 'system:log:remove', '#', 103, 1,
        '2025-01-21 10:41:59.948826', 1, '2025-01-21 10:41:59.948826', '');
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache,
                             menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by,
                             update_time, remark)
VALUES (41, '日志导出', 8, 3, '', '', null, '1', '0', 'F', '0', '0', 'system:log:export', '#', 103, 1,
        '2025-01-21 10:42:41.319977', 1, '2025-01-21 10:42:41.319977', '');

SELECT setval('sys_menu_menu_id_seq', 100);