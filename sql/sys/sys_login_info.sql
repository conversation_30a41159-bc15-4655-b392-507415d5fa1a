create table sys_login_info
(
    info_id        serial
        constraint sys_login_info_pk
            primary key,
    user_name      varchar(50)  default ''::character varying,
    device_type    varchar(32)  default ''::character varying,
    ipaddr         varchar(128) default ''::character varying,
    login_location varchar(255) default ''::character varying,
    browser        varchar(50)  default ''::character varying,
    os             varchar(50)  default ''::character varying,
    status         char         default '0'::bpchar,
    msg            varchar(255) default ''::character varying,
    login_time     timestamp
);

comment on table sys_login_info is '系统访问记录';

comment on column sys_login_info.info_id is '访问ID';

comment on column sys_login_info.user_name is '用户账号';

comment on column sys_login_info.device_type is '设备类型';

comment on column sys_login_info.ipaddr is '登录IP地址';

comment on column sys_login_info.login_location is '登录地点';

comment on column sys_login_info.browser is '浏览器类型';

comment on column sys_login_info.os is '操作系统';

comment on column sys_login_info.status is '登录状态（0成功 1失败）';

comment on column sys_login_info.msg is '提示消息';

comment on column sys_login_info.login_time is '访问时间';

alter table sys_login_info
    owner to postgres;

create index idx_sys_login_info_s
    on sys_login_info (status);

create index idx_sys_login_info_lt
    on sys_login_info (login_time);

