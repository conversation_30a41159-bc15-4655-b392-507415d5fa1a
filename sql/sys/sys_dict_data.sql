create table sys_dict_data
(
    dict_code   serial
        constraint sys_dict_data_pk
            primary key,
    dict_sort   integer      default 0,
    dict_label  varchar(100) default ''::character varying,
    dict_value  varchar(100) default ''::character varying,
    dict_type   varchar(100) default ''::character varying,
    css_class   varchar(100) default NULL::character varying,
    list_class  varchar(100) default NULL::character varying,
    is_default  char         default 'N'::bpchar,
    create_dept bigint,
    create_by   bigint,
    create_time timestamp,
    update_by   bigint,
    update_time timestamp,
    remark      varchar(500) default NULL::character varying
);

comment on table sys_dict_data is '字典数据表';

comment on column sys_dict_data.dict_code is '字典编码';

comment on column sys_dict_data.dict_sort is '字典排序';

comment on column sys_dict_data.dict_label is '字典标签';

comment on column sys_dict_data.dict_value is '字典键值';

comment on column sys_dict_data.dict_type is '字典类型';

comment on column sys_dict_data.css_class is '样式属性（其他样式扩展）';

comment on column sys_dict_data.list_class is '表格回显样式';

comment on column sys_dict_data.is_default is '是否默认（Y是 N否）';

comment on column sys_dict_data.create_dept is '创建部门';

comment on column sys_dict_data.create_by is '创建者';

comment on column sys_dict_data.create_time is '创建时间';

comment on column sys_dict_data.update_by is '更新者';

comment on column sys_dict_data.update_time is '更新时间';

comment on column sys_dict_data.remark is '备注';

alter table sys_dict_data
    owner to postgres;

