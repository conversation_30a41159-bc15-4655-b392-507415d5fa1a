-- auto-generated definition
create table orders
(
    id                  serial
        primary key,
    order_no            varchar(50)             not null
        unique,
    user_id             bigint,
    game_name           varchar(100)            not null,
    product_id          varchar(100)            not null,
    order_amount        numeric(10, 2)          not null,
    order_status        varchar(20)             not null,
    paid                boolean   default false not null,
    verified            boolean,
    payment_method      varchar(20),
    payment_time        timestamp,
    transaction_id      varchar(100),
    payment_environment smallint  default 0,
    refund_time         timestamp,
    purchase_token_raw  text,
    purchase_token      jsonb,
    create_time         timestamp default CURRENT_TIMESTAMP,
    update_time         timestamp default CURRENT_TIMESTAMP,
    create_by           bigint,
    update_by           bigint,
    ip_country_code     varchar(20),
    device_country_code varchar(20)
);

comment on table orders is '订单表';

comment on column orders.id is '主键ID';

comment on column orders.order_no is '订单编号';

comment on column orders.user_id is '用户ID';

comment on column orders.game_name is '游戏名称';

comment on column orders.product_id is '产品ID';

comment on column orders.order_amount is '订单金额';

comment on column orders.order_status is '订单状态(created/verifying/paid/failed)';

comment on column orders.paid is '支付状态，用户是否已经支付（其实就是客户端是否请求过校验接口）';

comment on column orders.verified is '服务端校验状态';

comment on column orders.payment_method is '支付方式';

comment on column orders.payment_time is '支付时间';

comment on column orders.transaction_id is '交易ID';

comment on column orders.payment_environment is '支付环境(0:校验中,1:沙盒支付,2:生产环境支付)';

comment on column orders.refund_time is '退款时间';

comment on column orders.purchase_token_raw is '未解析的purchase_token';

comment on column orders.purchase_token is '解析后的purchase_token';

comment on column orders.create_time is '创建时间';

comment on column orders.update_time is '更新时间';

comment on column orders.create_by is '创建者';

comment on column orders.update_by is '更新者';

comment on column orders.ip_country_code is 'ip 对应的国家代码';

comment on column orders.device_country_code is '设备国家代码';

alter table orders
    owner to pixelab;

create index idx_orders_order_no
    on orders (order_no);

create index idx_orders_user_id
    on orders (user_id);

create index idx_orders_game_name
    on orders (game_name);

create index idx_orders_order_status
    on orders (order_status);

create index idx_orders_create_time
    on orders (create_time);

create index idx_orders_product_id
    on orders (product_id);

create index idx_orders_payment_environment
    on orders (payment_environment);

