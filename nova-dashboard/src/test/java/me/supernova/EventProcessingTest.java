package me.supernova;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
class EventProcessingTest {


    @Test
    void testProcessEventParams() {
        List<String[]> allNodes = new ArrayList<>();
        Random random = new Random();

        // 定义不同层级的字符串池
        String[] level0Pool = {"event_typeA", "event_typeB"}; // 根节点池，较小，重复率高
        String[] level1Pool = {"event_name1", "event_name2", "event_name3"}; // 第二层节点池
        String[] level2Pool = {"paramX1", "paramX2", "paramX3", "paramX4"}; // 第三层节点池
        String[] level3Pool = {"paramY1", "paramY2", "paramY3", "paramY4", "paramY5"}; // 第四层节点池
        String[] level4Pool = {"paramZ1", "paramZ2", "paramZ3", "paramZ4", "paramZ5", "paramZ6"}; // 第五层节点池，较大，重复率低

        int numberOfArrays = 15; // 生成15个数组
        int arrayLength = 5;     // 每个数组的长度

        for (int i = 0; i < numberOfArrays; i++) {
            String[] nodes = new String[arrayLength];
            nodes[0] = level0Pool[random.nextInt(level0Pool.length)];
            nodes[1] = level1Pool[random.nextInt(level1Pool.length)];
            nodes[2] = level2Pool[random.nextInt(level2Pool.length)];
            nodes[3] = level3Pool[random.nextInt(level3Pool.length)];
            nodes[4] = level4Pool[random.nextInt(level4Pool.length)];
            allNodes.add(nodes);
        }

        for (String[] nodes : allNodes) {
            log.info("\n");
            log.info(String.join(":", nodes));
            processEventParams(nodes);
        }
        log.info("处理完成，共生成 {} 个事件参数组合", numberOfArrays);
    }

    private void processEventParams(String[] nodes) {
        if (nodes == null || nodes.length == 0) {
            log.warn("跳过处理：事件对象为空");
            return;
        }

        Map<String, Set<String>> batchOperations = new HashMap<>();

        for (int i = 1; i < nodes.length; i++) {
            String value = nodes[i];
            if (StrUtil.isBlank(value)) {
                continue;
            }

            List<String> prefixElements = Arrays.stream(nodes, 0, i)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());

            if (prefixElements.isEmpty()) {
                continue;
            }

            String redisKey = String.join(":", prefixElements);
            batchOperations.computeIfAbsent(redisKey, k -> new HashSet<>()).add(value);

            if (log.isDebugEnabled()) {
                log.debug("{} -> {}", redisKey, value);
            }
        }

        if (!batchOperations.isEmpty()) {
            executeBatchRedisOperations(batchOperations);
        }

    }

    private void executeBatchRedisOperations(Map<String, Set<String>> batchOperations) {
        try {

            for (Map.Entry<String, Set<String>> entry : batchOperations.entrySet()) {
                log.info("{} -> {}", entry.getKey(), entry.getValue());
            }
        } catch (Exception e) {
            log.error("执行Redis批量操作时发生错误", e);
            throw e;
        }
    }

}
