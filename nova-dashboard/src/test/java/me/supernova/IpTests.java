package me.supernova;

import me.supernova.common.ip.util.AddressUtils;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class IpTests {

    private static final double GROUP_A_PROBABILITY = 0.4;
    private static final double GROUP_B_PROBABILITY = 0.4;
    private static final double GROUP_C_PROBABILITY = 0.2;

    private String assignGroup() {
        double random = new Random().nextDouble();
        if (random < GROUP_A_PROBABILITY) {
            return "A";
        } else if (random < GROUP_A_PROBABILITY + GROUP_B_PROBABILITY) {
            return "B";
        } else {
            return "C";
        }
    }

    private Map<String, Integer> runDistributionTest(int sampleSize) {
        Map<String, Integer> distribution = new HashMap<>();
        distribution.put("A", 0);
        distribution.put("B", 0);
        distribution.put("C", 0);

        for (int i = 0; i < sampleSize; i++) {
            String group = assignGroup();
            distribution.put(group, distribution.get(group) + 1);
        }

        return distribution;
    }

    @Test
    public void test1() {
        int[] sampleSizes = {10, 100, 1000, 10000, 100000};

        for (int size : sampleSizes) {
            Map<String, Integer> distribution = runDistributionTest(size);
            System.out.println("\n样本量: " + size);
            System.out.println("A组: " + distribution.get("A") + " (" + String.format("%.2f", (double) distribution.get("A") / size * 100) + "%)");
            System.out.println("B组: " + distribution.get("B") + " (" + String.format("%.2f", (double) distribution.get("B") / size * 100) + "%)");
            System.out.println("C组: " + distribution.get("C") + " (" + String.format("%.2f", (double) distribution.get("C") / size * 100) + "%)");
        }
    }

    @Test
    public void test() {

        List<String> ipList = List.of("***************",
                "************",
                "**************",
                "**************",
                "*************",
                "**************",
                "*************",
                "*************",
                "***************",
                "*************",
                "**************",
                "**************",
                "**************",
                "**************",
                "************",
                "************",
                "**************",
                "**************",
                "**************",
                "*************",
                "***************",
                "***************",
                "************",
                "*************",
                "**************",
                "*************",
                "**********",
                "**************",
                "************",
                "*************5",
                "177.228.81.122",
                "187.249.9.99",
                "200.68.152.21",
                "201.163.122.214",
                "38.58.169.52",
                "170.233.79.220",
                "189.217.199.71",
                "201.141.24.39",
                "170.238.50.125",
                "177.232.89.120",
                "38.49.155.101",
                "131.108.155.184",
                "187.184.7.19",
                "177.200.73.84",
                "201.131.201.138",
                "201.141.98.63",
                "177.236.15.71",
                "202.5.97.106",
                "170.244.29.189",
                "149.19.169.243",
                "187.110.96.1",
                "189.219.54.149",
                "70.132.241.62",
                "201.166.190.83",
                "186.235.191.14",
                "187.249.43.178",
                "38.43.96.74",
                "189.196.18.163",
                "189.215.167.141",
                "189.84.34.61",
                "192.24.68.104",
                "187.161.132.62",
                "177.36.170.6",
                "177.239.37.95",
                "68.115.78.54",
                "201.140.253.102",
                "189.217.199.179",
                "189.179.128.171",
                "45.188.166.2",
                "189.149.1.27",
                "38.194.247.234",
                "189.150.92.23",
                "164.163.150.236",
                "47.33.17.95",
                "189.215.9.61",
                "177.248.17.227",
                "45.180.5.61",
                "131.108.154.224",
                "45.172.89.43",
                "177.229.212.186",
                "187.189.211.224",
                "187.62.238.45",
                "45.7.64.105",
                "177.230.65.204",
                "201.140.111.154",
                "189.222.191.79",
                "181.232.209.141",
                "187.189.171.97",
                "38.49.144.62",
                "45.168.238.190",
                "187.190.115.130",
                "189.217.107.65",
                "187.122.59.89",
                "200.108.187.142",
                "201.141.30.77",
                "189.226.103.214",
                "**************",
                "*************",
                "*************",
                "*************",
                "**************",
                "**************",
                "**************",
                "*************",
                "************",
                "***************",
                "**************",
                "************",
                "***************",
                "***********",
                "***************");


        for (String ip : ipList) {
            System.out.println(AddressUtils.getAddressByIP(ip));
        }
    }
}
