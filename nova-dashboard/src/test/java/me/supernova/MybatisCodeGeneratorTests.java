package me.supernova;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import org.junit.jupiter.api.Test;

import java.util.Collections;

public class MybatisCodeGeneratorTests {

    @Test
    public void generate() {
        // 使用 FastAutoGenerator 快速配置代码生成器
        FastAutoGenerator.create(
                        "*************************************************************************************",
                        "postgres",
                        "asdfasdf"
                )
                .globalConfig(builder -> builder
                        .author("Litian") // 设置作者
                        .outputDir("src/main/java") // 输出目录
                        .disableOpenDir()
                        .enableSpringdoc()
                        .dateType(DateType.TIME_PACK)
                )
                .packageConfig(builder -> builder
                        .parent("me.supernova.dashboard") // 设置父包名
                        .entity("model.entity") // 设置实体类包名
                        .mapper("mapper") // 设置 Mapper 接口包名
                        .controller("controller") // 设置 Controller 类包名
                        .service("service") // 设置 Service 接口包名
                        .serviceImpl("service.impl") // 设置 Service 实现类包名
                        .pathInfo(Collections.singletonMap(OutputFile.xml, "src/main/resources/mapper")) // 设置路径配置信息
                )
                .strategyConfig(builder -> builder
                                .addInclude("funnels") // 设置需要生成的表名
                        .controllerBuilder()
                        // 启用 REST 风格
                        .enableRestStyle()
                        .serviceBuilder()
                        // 设置 Service 接口命名格式 不设置的话接口都会用 I 开头, 如 IUserService
                        .formatServiceFileName("%sService")
                        .entityBuilder()
                        .enableFileOverride()
                        .enableLombok()
//                        .superClass(BaseEntity.class)
//                        .addIgnoreColumns(List.of("create_dept", "create_by", "create_time", "update_by", "update_time"))
                        .enableFileOverride()
                )
                .templateEngine(new VelocityTemplateEngine()) // 使用 Freemarker 模板引擎
                .execute(); // 执行生成
    }
}
