spring:
  data:
    redis:
      cluster:
        nodes:
          - pixelab-redis-ororyx.serverless.use1.cache.amazonaws.com:6379
      database: 0
      timeout: 10s
      ssl.enabled: true
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ********************************************************************************************************************************************************************************************************************************************************************************
    username: postgres
    password: Oohn7RGiGz68vVCeraXW
  config:
    import:
      - classpath:/config/application-satoken.yml
      - classpath:/config/application-servlet.yml
      - classpath:/config/application-actuator.yml
      - classpath:/config/application-mybatis.yml
      - classpath:/config/application-redis.yml


# 飞书配置
lark:
  # 飞书应用ID，需要替换为实际的应用ID
  app-id: cli_a75115e9a41e100e
  # 飞书应用密钥，需要替换为实际的应用密钥
  app-secret: iy6CG80LnRl9H94zRqufrDTasIKdhP6D

api-decrypt:
  enabled: true
  password: abcksibeksjxurlf

feishu:
  webhook: https://open.feishu.cn/open-apis/bot/v2/hook/3ae286e5-e269-4535-b517-ac37661e5c2b