<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.supernova.dashboard.mapper.UserRankMapper">
    <!-- 获取指定用户的排名 -->
    <select id="selectRankByUserId" resultType="java.lang.Integer">
        SELECT rank
        FROM (SELECT user_id,
                     row_number() OVER (ORDER BY score DESC, update_time) AS rank
              FROM user_rank) ranked
        WHERE user_id = #{userId};
    </select>

    <select id="selectTopNRankVos" resultType="me.supernova.dashboard.model.vo.RankVo">
        SELECT
            user_id AS userId,
            score,
            nickname,
            avatar,
            country_code AS countryCode,
            robot,
            ROW_NUMBER() OVER (ORDER BY score DESC, update_time) as rank
        FROM
            user_rank
        WHERE
            score >= #{minScore}
        ORDER BY
            score DESC, update_time
        LIMIT #{n}
    </select>

    <select id="selectTopNUserRankVos" resultType="me.supernova.dashboard.model.vo.RankVo">
        SELECT user_id                                              AS userId,
               score,
               nickname,
               avatar,
               country_code                                         AS countryCode,
               robot,
               ROW_NUMBER() OVER (ORDER BY score DESC, update_time) as rank
        FROM user_rank
        WHERE robot = false
          and score >= #{minScore}
        ORDER BY score DESC, update_time
        LIMIT #{n}
    </select>
</mapper>
