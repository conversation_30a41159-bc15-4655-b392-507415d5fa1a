<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.supernova.dashboard.mapper.ProductSalesAnalysisMapper">

    <!-- 查询商品基础销售数据 -->
    <!--$var timeConfig.timeColumn=event_time-->
    <select id="selectProductBasicSales" resultType="me.supernova.dashboard.model.dto.ProductBasicSalesDTO">
        SELECT
            o.product_id,
            AVG(o.order_amount) as unit_price,
            COUNT(DISTINCT o.user_id) as buyer_count,
            COUNT(o.id) as purchase_count,
            SUM(o.order_amount) as total_amount
        FROM orders o
        JOIN user_info u ON o.user_id = u.user_id
        WHERE o.order_status = 'paid'
        AND o.verified = true
        AND o.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
        AND o.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version = #{qryBo.firstVersion}
        </if>
        <if test="qryBo.store != null and qryBo.store != ''">
            AND u.store = #{qryBo.store}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version = #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group->>'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
        <if test="qryBo.productId != null and qryBo.productId != ''">
            AND o.product_id = #{qryBo.productId}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="country" open="(" close=")" separator=",">
                #{country}
            </foreach>
        </if>
        GROUP BY o.product_id
    </select>

    <!-- 查询商品新用户销售数据 -->
    <!--$var timeConfig.timeColumn=event_time-->
    <select id="selectProductNewUserSales" resultType="me.supernova.dashboard.model.dto.ProductNewUserSalesDTO">
        SELECT
            o.product_id,
            COUNT(DISTINCT o.user_id) as new_buyer_count,
            COUNT(o.id) as new_user_purchase_count
        FROM orders o
        JOIN user_info u ON o.user_id = u.user_id
        WHERE o.order_status = 'paid'
        AND o.verified = true
        AND o.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
        AND o.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version = #{qryBo.firstVersion}
        </if>
        <if test="qryBo.store != null and qryBo.store != ''">
            AND u.store = #{qryBo.store}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version = #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group->>'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
        <if test="qryBo.productId != null and qryBo.productId != ''">
            AND o.product_id = #{qryBo.productId}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="country" open="(" close=")" separator=",">
                #{country}
            </foreach>
        </if>
        AND o.product_id IN
        <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
            #{productId}
        </foreach>
        AND o.user_id IN (
            SELECT DISTINCT first_order.user_id
            FROM (
                SELECT user_id, MIN(create_time) as first_purchase_time
                FROM orders
                WHERE order_status = 'paid' AND verified = true
                GROUP BY user_id
            ) first_order
            WHERE first_order.first_purchase_time <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
              AND first_order.first_purchase_time <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        )
        GROUP BY o.product_id
    </select>

    <!-- 查询总销售额 -->
    <!--$var timeConfig.timeColumn=event_time-->
    <select id="selectTotalSalesAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(o.order_amount), 0) as total_amount
        FROM orders o
        JOIN user_info u ON o.user_id = u.user_id
        WHERE o.order_status = 'paid'
        AND o.verified = true
        AND o.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
        AND o.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version = #{qryBo.firstVersion}
        </if>
        <if test="qryBo.store != null and qryBo.store != ''">
            AND u.store = #{qryBo.store}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version = #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group->>'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
        <if test="qryBo.productId != null and qryBo.productId != ''">
            AND o.product_id = #{qryBo.productId}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="country" open="(" close=")" separator=",">
                #{country}
            </foreach>
        </if>
    </select>

</mapper>
