<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.supernova.dashboard.mapper.UserRetentionMapper">
    <resultMap id="userRetentionMetricsMap" type="me.supernova.dashboard.model.dto.UserRetentionMetricsDTO">
        <result property="activeUsers" column="active_users"/>
        <result property="day1RetainedUsers" column="day1_retained_users"/>
        <result property="day2RetainedUsers" column="day2_retained_users"/>
        <result property="day3RetainedUsers" column="day3_retained_users"/>
        <result property="day4RetainedUsers" column="day4_retained_users"/>
        <result property="day5RetainedUsers" column="day5_retained_users"/>
        <result property="day6RetainedUsers" column="day6_retained_users"/>
        <result property="day7RetainedUsers" column="day7_retained_users"/>
        <result property="day8RetainedUsers" column="day8_retained_users"/>
        <result property="day9RetainedUsers" column="day9_retained_users"/>
    </resultMap>

    <select id="batchGetUserRetentionMetrics" resultMap="userRetentionMetricsMap">
        WITH date_range AS (SELECT generate_series(
                                           #{startTime}::timestamp,
                                           #{endTime}::timestamp,
                                           '1 day'::interval
                                   )::date as date),
        filtered_users AS (
        SELECT ur.*
        FROM user_retention ur
            INNER JOIN user_info ui
        ON ur.user_id = ui.user_id
        <if test="ipCountryCode != null">
            AND ui.ip_country_code IN
            <foreach collection="ipCountryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platform != null and platform != ''">
            AND ui.platform = #{platform}
        </if>
        <if test="gameName != null and gameName != ''">
            AND ui.game_name = #{gameName}
        </if>
        <if test="store != null and store != ''">
            AND ui.store = #{store}
        </if>
        <if test="firstVersion != null and firstVersion != ''">
            AND ui.game_version = #{firstVersion}
        </if>
        <if test="lastVersion != null and lastVersion != ''">
            AND ui.last_version = #{lastVersion}
        </if>
        WHERE ur.record_type = #{recordType}
          AND ur.timezone = #{timezone}
        )
        SELECT dr.date                                                        as date,
               COUNT(DISTINCT fu.user_id)                                     as active_users,
               COUNT(DISTINCT CASE WHEN fu.day1_retained THEN fu.user_id END) as day1_retained_users,
               COUNT(DISTINCT CASE WHEN fu.day2_retained THEN fu.user_id END) as day2_retained_users,
               COUNT(DISTINCT CASE WHEN fu.day3_retained THEN fu.user_id END) as day3_retained_users,
               COUNT(DISTINCT CASE WHEN fu.day4_retained THEN fu.user_id END) as day4_retained_users,
               COUNT(DISTINCT CASE WHEN fu.day5_retained THEN fu.user_id END) as day5_retained_users,
               COUNT(DISTINCT CASE WHEN fu.day6_retained THEN fu.user_id END) as day6_retained_users,
               COUNT(DISTINCT CASE WHEN fu.day7_retained THEN fu.user_id END) as day7_retained_users,
               COUNT(DISTINCT CASE WHEN fu.day8_retained THEN fu.user_id END) as day8_retained_users,
               COUNT(DISTINCT CASE WHEN fu.day9_retained THEN fu.user_id END) as day9_retained_users
        FROM date_range dr
                 LEFT JOIN filtered_users fu
                           ON fu.start_date = dr.date
        GROUP BY dr.date
        ORDER BY dr.date
    </select>
</mapper>
