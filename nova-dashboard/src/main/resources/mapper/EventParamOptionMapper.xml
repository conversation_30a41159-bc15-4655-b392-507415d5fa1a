<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.supernova.dashboard.mapper.EventParamOptionMapper">

    <insert id="insertBatchIgnore">
        INSERT INTO event_param_option (key, value, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.key}, #{item.value}, #{item.createTime})
        </foreach>
        ON CONFLICT (key, value) DO NOTHING
    </insert>

</mapper>
