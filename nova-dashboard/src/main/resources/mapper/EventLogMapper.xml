<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.supernova.dashboard.mapper.EventLogMapper">
    <!--$var timeConfig.timeColumn=event_time-->
    <select id="getPaymentMetrics" resultType="me.supernova.dashboard.model.dto.PaymentMetricsDTO">
        WITH user_registrations AS (SELECT DISTINCT e.user_id
                                    FROM event_log e
                                             JOIN user_info u ON e.user_id = u.user_id
                                    WHERE e.event_type = 'SERVER_EVENT'
                                      AND e.event_name = 'register'
                                      AND e.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime} :: timestamp
                                      AND e.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime} :: timestamp
                                    <if test="qryBo.gameName != null and qryBo.gameName != ''">
                                        AND u.game_name = #{qryBo.gameName}
                                    </if>
                                    <if test="qryBo.platform != null and qryBo.platform != ''">
                                        AND u.platform = #{qryBo.platform}
                                    </if>
                                    <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
                                        AND u.ip_country_code IN
                                        <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                    </if>
                                    <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
                                        AND u.game_version <![CDATA[=]]> #{qryBo.firstVersion}
                                    </if>
                                    <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
                                        AND u.last_version <![CDATA[=]]> #{qryBo.latestVersion}
                                    </if>
                                    <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
                                        AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
                                    </if>),
        valid_payments AS (
        SELECT e.user_id,
               COUNT(*) as payment_count
        FROM event_log e
                 JOIN user_info u ON e.user_id = u.user_id
        WHERE e.event_type = 'SERVER_EVENT'
          AND e.event_name = 'paid'
          AND e.${timeConfig.timeColumn} >= #{timeConfig.startTime} :: timestamp
          AND e.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime} :: timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version <![CDATA[=]]> #{qryBo.firstVersion}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version <![CDATA[=]]> #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
        GROUP BY e.user_id
        )
        SELECT COALESCE(SUM(vp.payment_count), 0) as total_payment_count,
               COALESCE(SUM(CASE WHEN ur.user_id IS NOT NULL THEN vp.payment_count ELSE 0 END),
                        0)                        as new_user_payment_count
        FROM valid_payments vp
                 LEFT JOIN user_registrations ur ON vp.user_id = ur.user_id
    </select>

    <!--$var timeConfig.timeColumn=event_time-->
    <select id="getAdShowMetrics" resultType="me.supernova.dashboard.model.dto.AdShowMetricsDTO">
        SELECT COUNT(CASE WHEN e.param3 = 'rewarded_video' THEN 1 END) as rewarded_video_count,
               COUNT(CASE WHEN e.param3 = 'interstitial' THEN 1 END)   as interstitial_count
        FROM event_log e
                 JOIN user_info u ON e.user_id = u.user_id
        WHERE e.event_type = 'AD_EVENT'
          AND e.param4 = 'show'
          AND e.param3 IN ('rewarded_video', 'interstitial')
          AND e.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
          AND e.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version <![CDATA[=]]> #{qryBo.firstVersion}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version <![CDATA[=]]> #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
    </select>

    <!--$var timeConfig.timeColumn=event_time-->
    <select id="calculateUserRetention" resultType="me.supernova.dashboard.model.dto.UserRetentionDTO">
        WITH base_users AS (SELECT e.user_id,
                                   DATE(e.${timeConfig.timeColumn}) as base_date,
                                   MIN(e.${timeConfig.timeColumn})  as base_time
                            FROM event_log e
                                     JOIN user_info u ON e.user_id = u.user_id
                            WHERE e.event_type = 'SERVER_EVENT'
                              AND e.event_name = #{eventName}
                              AND e.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
                              AND e.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp
                            GROUP BY e.user_id, DATE(e.${timeConfig.timeColumn})),
             user_logins AS (SELECT el.user_id,
                                    bu.base_time,
                                    el.${timeConfig.timeColumn} as login_time,
                                    FLOOR(EXTRACT(EPOCH FROM (el.${timeConfig.timeColumn} - bu.base_time)) /
                                          86400)                as day_number
                             FROM event_log el
                                      JOIN user_info u ON el.user_id = u.user_id
                                      JOIN base_users bu ON el.user_id = bu.user_id
                                 AND el.${timeConfig.timeColumn} <![CDATA[>=]]> bu.base_time
                                 -- 计算9天留存需要10天数据窗口（第0天到第9天）
                                 AND el.${timeConfig.timeColumn} <![CDATA[<]]> bu.base_time + INTERVAL '10 days'
                             WHERE el.event_type = 'SERVER_EVENT'
                               AND el.event_name = 'login')
        SELECT bu.user_id                                         as user_id,
               bu.base_time::date                                 as start_date,
               MAX(CASE WHEN ul.day_number = 1 THEN 1 ELSE 0 END) as day1_retained,
               MAX(CASE WHEN ul.day_number = 2 THEN 1 ELSE 0 END) as day2_retained,
               MAX(CASE WHEN ul.day_number = 3 THEN 1 ELSE 0 END) as day3_retained,
               MAX(CASE WHEN ul.day_number = 4 THEN 1 ELSE 0 END) as day4_retained,
               MAX(CASE WHEN ul.day_number = 5 THEN 1 ELSE 0 END) as day5_retained,
               MAX(CASE WHEN ul.day_number = 6 THEN 1 ELSE 0 END) as day6_retained,
               MAX(CASE WHEN ul.day_number = 7 THEN 1 ELSE 0 END) as day7_retained,
               MAX(CASE WHEN ul.day_number = 8 THEN 1 ELSE 0 END) as day8_retained,
               MAX(CASE WHEN ul.day_number = 9 THEN 1 ELSE 0 END) as day9_retained
        FROM base_users bu
                 LEFT JOIN user_logins ul ON bu.user_id = ul.user_id AND bu.base_time = ul.base_time
        GROUP BY bu.user_id, bu.base_time
        ORDER BY bu.user_id, bu.base_time
    </select>

    <!--$var timeConfig.timeColumn=event_time-->
    <select id="getUserMetrics" resultType="me.supernova.dashboard.model.dto.UserMetricsDTO">
        WITH
        filtered_users AS (
        SELECT u.user_id
        FROM user_info u
        WHERE 1 = 1
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version = #{qryBo.firstVersion}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version = #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
        ),
            -- 获取指定时间范围内的注册和登录事件（为计算次日留存，需要扩展1天获取次日登录数据）
            relevant_events AS (SELECT e.user_id, e.event_time, e.event_name
                                FROM event_log e
                                         JOIN filtered_users fu ON e.user_id = fu.user_id
                                WHERE e.event_type = 'SERVER_EVENT'
                                  AND e.event_name IN ('register', 'login')
                                  AND e.event_time >= #{timeConfig.startTime}::timestamp
                                  AND e.event_time <![CDATA[<]]> (#{timeConfig.endTime}::timestamp + INTERVAL '1 day')),
            -- 统计指定时间范围内的新注册用户
            register_times AS (SELECT user_id, MIN(event_time) AS register_time
                               FROM relevant_events
                               WHERE event_name = 'register'
                                 AND event_time >= #{timeConfig.startTime}::timestamp
                                 AND event_time <![CDATA[<]]> #{timeConfig.endTime}::timestamp
                               GROUP BY user_id),
            new_users AS (SELECT COUNT(*) AS new_users
                          FROM register_times),
            -- 统计指定时间范围内的活跃用户（DAU）
            dau AS (SELECT COUNT(DISTINCT user_id) AS dau
                    FROM relevant_events
                    WHERE event_name = 'login'
                      AND event_time >= #{timeConfig.startTime}::timestamp
                      AND event_time <![CDATA[<]]> #{timeConfig.endTime}::timestamp),
            -- 计算次日留存：在指定时间范围内注册的用户，在注册次日有登录行为的用户数
            retention AS (SELECT COUNT(DISTINCT re.user_id) AS retention
                          FROM relevant_events re
                                   JOIN register_times rt ON re.user_id = rt.user_id
                          WHERE re.event_name = 'login'
                            AND re.event_time >= rt.register_time + INTERVAL '1 day'
                            AND re.event_time <![CDATA[<]]> rt.register_time + INTERVAL '2 day')
        SELECT n.new_users, d.dau, r.retention
        FROM new_users n
                 CROSS JOIN dau d
                 CROSS JOIN retention r
    </select>

    <!--$var timeConfig.timeColumn=event_time-->
    <select id="countDistinctUsersByEventCriteria" resultType="Integer">
        SELECT COUNT(DISTINCT e.user_id) AS userCount
        FROM event_log e
                 JOIN user_info u ON e.user_id = u.user_id
        WHERE e.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
          AND e.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp

        <if test="paramElementDto.eventType != null and paramElementDto.eventType != ''">
            AND e.event_type = #{paramElementDto.eventType}
        </if>
        <if test="paramElementDto.eventName != null and paramElementDto.eventName != ''">
            AND e.event_name = #{paramElementDto.eventName}
        </if>
        <if test="paramElementDto.param1 != null and paramElementDto.param1.size() > 0">
            AND e.param1 IN
            <foreach collection="paramElementDto.param1" item="value" open="(" separator="," close=")">
                #{value}
            </foreach>
        </if>
        <if test="paramElementDto.param2 != null and paramElementDto.param2.size() > 0">
            AND e.param2 IN
            <foreach collection="paramElementDto.param2" item="value" open="(" separator="," close=")">
                #{value}
            </foreach>
        </if>
        <if test="paramElementDto.param3 != null and paramElementDto.param3.size() > 0">
            AND e.param3 IN
            <foreach collection="paramElementDto.param3" item="value" open="(" separator="," close=")">
                #{value}
            </foreach>
        </if>
        <if test="paramElementDto.param4 != null and paramElementDto.param4.size() > 0">
            AND e.param4 IN
            <foreach collection="paramElementDto.param4" item="value" open="(" separator="," close=")">
                #{value}
            </foreach>
        </if>
        <if test="paramElementDto.param5 != null and paramElementDto.param5.size() > 0">
            AND e.param5 IN
            <foreach collection="paramElementDto.param5" item="value" open="(" separator="," close=")">
                #{value}
            </foreach>
        </if>
        <if test="paramElementDto.param6 != null and paramElementDto.param6.size() > 0">
            AND e.param6 IN
            <foreach collection="paramElementDto.param6" item="value" open="(" separator="," close=")">
                #{value}
            </foreach>
        </if>
        <if test="paramElementDto.param7 != null and paramElementDto.param7.size() > 0">
            AND e.param7 IN
            <foreach collection="paramElementDto.param7" item="value" open="(" separator="," close=")">
                #{value}
            </foreach>
        </if>
        <if test="paramElementDto.param8 != null and paramElementDto.param8.size() > 0">
            AND e.param8 IN
            <foreach collection="paramElementDto.param8" item="value" open="(" separator="," close=")">
                #{value}
            </foreach>
        </if>
        <if test="paramElementDto.param9 != null and paramElementDto.param9.size() > 0">
            AND e.param9 IN
            <foreach collection="paramElementDto.param9" item="value" open="(" separator="," close=")">
                #{value}
            </foreach>
        </if>
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version <![CDATA[=]]> #{qryBo.firstVersion}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version <![CDATA[=]]> #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
        GROUP BY e.event_name
    </select>

    <!--$var timeConfig.timeColumn=event_time-->
    <select id="countTotalAppTime" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(e.event_value), 0) as total_app_time
        FROM event_log e
                 JOIN user_info u ON e.user_id = u.user_id
        WHERE e.event_type = 'USAGE_DURATION_EVENT'
          AND e.event_name = 'usage_duration'
          AND e.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
          AND e.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version <![CDATA[=]]> #{qryBo.firstVersion}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version <![CDATA[=]]> #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
    </select>

    <!--$var timeConfig.timeColumn=event_time-->
    <select id="sumIapRevenue" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(e.event_value), 0) as total_revenue
        FROM event_log e
                 JOIN user_info u ON e.user_id = u.user_id
        WHERE e.event_type = 'SERVER_EVENT'
          AND e.event_name = 'paid'
          AND e.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
          AND e.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version <![CDATA[=]]> #{qryBo.firstVersion}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version <![CDATA[=]]> #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
    </select>

    <!--$var timeConfig.timeColumn=event_time-->
    <select id="sumAdRevenue" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(e.event_value), 0) as total_revenue
        FROM event_log e
                 JOIN user_info u ON e.user_id = u.user_id
        WHERE e.event_type = 'AD_EVENT'
          AND e.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
          AND e.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version <![CDATA[=]]> #{qryBo.firstVersion}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version <![CDATA[=]]> #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
    </select>

    <!--$var timeConfig.timeColumn=event_time-->
    <select id="countNewPayingUsers" resultType="java.lang.Integer">
        -- 统计真正的新付费用户：首次付费发生在指定时间范围内的用户
        SELECT COUNT(DISTINCT e.user_id)
        FROM event_log e
                 JOIN user_info u ON e.user_id = u.user_id
                 JOIN (
            -- 子查询：找出每个用户的首次付费时间
            SELECT user_id, MIN(${timeConfig.timeColumn}) as first_payment_time
            FROM event_log
            WHERE event_type = 'SERVER_EVENT' AND event_name = 'paid'
            GROUP BY user_id
        ) fp ON e.user_id = fp.user_id
        WHERE e.event_type = 'SERVER_EVENT'
          AND e.event_name = 'paid'
          -- 首次付费时间必须在查询时间范围内
          AND fp.first_payment_time <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
          AND fp.first_payment_time <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version <![CDATA[=]]> #{qryBo.firstVersion}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version <![CDATA[=]]> #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
    </select>

    <!--$var timeConfig.timeColumn=event_time-->
    <select id="countPayingUsers" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT e.user_id)
        FROM event_log e
                 JOIN user_info u ON e.user_id = u.user_id
        WHERE e.event_type = 'SERVER_EVENT'
          AND e.event_name = 'paid'
          AND e.${timeConfig.timeColumn} <![CDATA[>=]]> #{timeConfig.startTime}::timestamp
          AND e.${timeConfig.timeColumn} <![CDATA[<]]> #{timeConfig.endTime}::timestamp
        <if test="qryBo.gameName != null and qryBo.gameName != ''">
            AND u.game_name = #{qryBo.gameName}
        </if>
        <if test="qryBo.platform != null and qryBo.platform != ''">
            AND u.platform = #{qryBo.platform}
        </if>
        <if test="qryBo.countryCode != null and qryBo.countryCode.size() > 0">
            AND u.ip_country_code IN
            <foreach collection="qryBo.countryCode" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="qryBo.firstVersion != null and qryBo.firstVersion != ''">
            AND u.game_version <![CDATA[=]]> #{qryBo.firstVersion}
        </if>
        <if test="qryBo.latestVersion != null and qryBo.latestVersion != ''">
            AND u.last_version <![CDATA[=]]> #{qryBo.latestVersion}
        </if>
        <if test="qryBo.abTestGameConfigGroup != null and qryBo.abTestGameConfigGroup != ''">
            AND u.ab_test_group ->> 'levelSetting' = #{qryBo.abTestGameConfigGroup}
        </if>
    </select>
</mapper>
