# redisson 配置
redisson:
  # redis key前缀
  keyPrefix: redisson
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: nova-dashboard
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
  clusterServersConfig:
    # 客户端名称
    clientName: nova-dashboard
    # master最小空闲连接数
    masterConnectionMinimumIdleSize: 32
    # master连接池大小
    masterConnectionPoolSize: 64
    # slave最小空闲连接数
    slaveConnectionMinimumIdleSize: 32
    # slave连接池大小
    slaveConnectionPoolSize: 64
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
    # 读取模式
    readMode: "SLAVE"
    # 订阅模式
    subscriptionMode: "MASTER"
