# Spring配置
spring:
  application:
    name: Nova-dashboard
  profiles:
    active: dev
  threads:
    virtual:
      enabled: false
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# 日志配置
logging:
  level:
    me.supernova: info
    org.springframework: warn
    org.mybatis.spring.mapper: error
  config: classpath:logback-spring.xml
  structured:
    format:
      console: ecs

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接
  excludeUrls:

# 全局线程池相关配置
# 如使用JDK21请直接使用虚拟线程 不要开启此配置
thread-pool:
  # 是否开启线程池
  enabled: false
  # 队列最大长度
  queueCapacity: 128
  # 线程池维护线程所允许的空闲时间
  keepAliveSeconds: 300

action-log:
  enabled: true
  isPrint: false
  enable-curl: true