spring:
  data:
    redis:
      host: *************
      port: 6379
      database: 0
      timeout: 10s
      ssl.enabled: false
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *********************************************************************************************************************************************************************************************************************************************
    username: pixelab
    password: lkasjdfjknwunf283748
  config:
    import:
      - classpath:/config/application-satoken.yml
      - classpath:/config/application-servlet.yml
      - classpath:/config/application-actuator.yml
      - classpath:/config/application-mybatis.yml
      - classpath:/config/application-redis.yml


# 飞书配置
lark:
  # 飞书应用ID，需要替换为实际的应用ID
  app-id: cli_a751126368e25013
  # 飞书应用密钥，需要替换为实际的应用密钥
  app-secret: nrGrmgFvXEmKI02JDCtJkhikezhrtElW

api-decrypt:
  enabled: true
  password: abcksibeksjxurlf


feishu:
  webhook: https://open.feishu.cn/open-apis/bot/v2/hook/e98b012b-2ba9-4139-bb8e-d33e81654f3f