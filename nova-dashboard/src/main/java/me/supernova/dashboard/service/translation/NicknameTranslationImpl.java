package me.supernova.dashboard.service.translation;

import lombok.AllArgsConstructor;
import me.supernova.common.translation.core.TranslationInterface;
import me.supernova.dashboard.model.constant.DictConstant;
import me.supernova.dashboard.service.UserService;
import org.springframework.stereotype.Component;

@Component(DictConstant.USER_ID_TO_NICKNAME)
@AllArgsConstructor
public class NicknameTranslationImpl implements TranslationInterface<String> {

    private final UserService userService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof Long id) {
            return userService.selectNicknameByIds(id.toString());
        } else if (key instanceof String ids) {
            return userService.selectNicknameByIds(ids);
        }
        return null;
    }
}
