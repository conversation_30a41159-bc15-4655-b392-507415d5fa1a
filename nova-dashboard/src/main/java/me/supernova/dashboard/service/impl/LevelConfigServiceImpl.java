package me.supernova.dashboard.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.FastExcel;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.json.utils.JsonUtils;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.dashboard.constant.AbTestGroupConstant;
import me.supernova.dashboard.mapper.LevelConfigMapper;
import me.supernova.dashboard.model.entity.LevelConfig;
import me.supernova.dashboard.model.entity.UserInfo;
import me.supernova.dashboard.model.vo.*;
import me.supernova.dashboard.service.LevelConfigService;
import me.supernova.dashboard.service.UserInfoService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 关卡信息表，存储游戏中各个关卡的详细信息 服务实现类
 * </p>
 * <p>
 * 此服务负责处理游戏关卡配置的获取逻辑，包括从数据库获取基础关卡列表，
 * 以及从 Redis 获取或设置远程游戏配置（如广告配置、评分配置）。
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LevelConfigServiceImpl extends ServiceImpl<LevelConfigMapper, LevelConfig> implements LevelConfigService {

    private final UserInfoService userInfoService;

    // Redis 键前缀
    private static final String REDIS_KEY_PREFIX = "game_config:v2:";
    // 存储合并后的远程游戏配置的 Redis Key
    private static final String KEY_REMOTE_GAME_CONFIG = REDIS_KEY_PREFIX + "remote_settings";
    // 存储XLSX配置文件的 Redis Key 前缀
    private static final String KEY_XLSX_CONFIG_PREFIX = REDIS_KEY_PREFIX + "xlsx:";
    // 存储版本列表的 Redis Key
    private static final String KEY_VERSIONS = REDIS_KEY_PREFIX + "versions";

    // IntervalAdConfig 默认值
    private static final Long DEFAULT_INTERVAL_AD_PLAY_INTERVAL = 120L;
    private static final Integer DEFAULT_INTERVAL_AD_FIRST_NO_INTERS_COUNT = 6;
    private static final Integer DEFAULT_INTERVAL_AD_PLAY_INTERVAL_LEVEL = 6;

    // RateConfig 默认值
    private static final Integer DEFAULT_RATE_CONFIG_FIRST_TRIGGER_LEVEL_COUNT = 5;
    private static final Integer DEFAULT_RATE_CONFIG_INTERVAL_LEVEL_COUNT = 10;
    private static final Integer DEFAULT_RATE_CONFIG_MAX_TRIGGER_COUNT = 3;

    /**
     * 内部类，用于封装存储在 Redis 中的远程配置项
     */
    @Data
    public static class RemoteConfigData {
        private GameConfigV2.IntervalAdConfig intervalAdConfig;
        private GameConfigV2.RateConfig rateConfig;
    }

    @Override
    public GameConfigV2 getConfig(BaseVo vo) {
        // 1. 获取或创建用户信息，并获取AB测试分组信息
        UserInfo userInfo = userInfoService.updateOrCreateDeviceInfo(vo.getClientInfo());
        Dict abTestGroup = userInfo.getAbTestGroup();
        String gameConfigGroup = abTestGroup.getStr(AbTestGroupConstant.LEVEL_SETTING);
        log.debug("用户信息: {}, AB测试分组: {}, 游戏配置分组: {}", userInfo, abTestGroup, gameConfigGroup);

        // 2. 从数据库获取关卡配置列表
        List<LevelConfig> list = list(
                Wrappers.lambdaQuery(LevelConfig.class)
                        .eq(LevelConfig::getAbGroup, gameConfigGroup)
                        .eq(LevelConfig::getEnabled, true)
                        .orderByAsc(LevelConfig::getLevelPriority)
        );

        List<LevelConfigVo> levelConfigVos = list.stream().map(s -> {
            LevelConfigVo levelConfigVo = BeanUtil.copyProperties(s, LevelConfigVo.class);
            String unlockConditions = levelConfigVo.getUnlockConditions();
            if (StrUtil.isBlank(unlockConditions)) {
                return levelConfigVo;
            }

            List<Integer> split = StrUtil.split(unlockConditions, "#")
                    .stream().map(Integer::valueOf).toList();
            levelConfigVo.setUnlockConditionsArray(split);
            return levelConfigVo;
        }).toList();

        log.debug("从数据库获取关卡配置列表，数量: {}", levelConfigVos.size());

        GameConfigV2 gameConfigV2 = new GameConfigV2();
        gameConfigV2.setLevelConfigs(levelConfigVos);
        gameConfigV2.setAbGroup(gameConfigGroup);

        // 3. 从 Redis 获取或设置远程游戏配置 (IntervalAdConfig 和 RateConfig)
        RemoteConfigData remoteConfigData = getOrInitRemoteConfigData(gameConfigGroup);

        // 将获取到或默认的配置设置到 gameConfigV2
        gameConfigV2.setIntervalAdConfig(remoteConfigData.getIntervalAdConfig());
        gameConfigV2.setRateConfig(remoteConfigData.getRateConfig());

        log.debug("最终返回的游戏配置 GameConfigV2: {}", gameConfigV2);
        return gameConfigV2;
    }

    private RemoteConfigData getOrInitRemoteConfigData(String gameConfigGroup) {
        String dynamicKeyRemoteGameConfig = KEY_REMOTE_GAME_CONFIG + ":" + gameConfigGroup;
        RemoteConfigData remoteConfigData = RedisUtils.getCacheObject(dynamicKeyRemoteGameConfig);

        log.debug("从 Redis 获取远程配置, key: {}, value: {}", dynamicKeyRemoteGameConfig, remoteConfigData);
        boolean needsUpdateInRedis;
        if (remoteConfigData == null || remoteConfigData.getIntervalAdConfig() == null || remoteConfigData.getRateConfig() == null) {
            log.debug("Redis中不存在有效配置或配置不完整，初始化默认远程配置数据");
            remoteConfigData = initDefaultRemoteConfigData();
            needsUpdateInRedis = true; // 新创建的，需要存入 Redis
        } else {
            log.debug("Redis中存在配置，检查并补全默认值, remoteConfigData: {}", remoteConfigData);
            // 数据存在，检查并补全默认值
            needsUpdateInRedis = ensureRemoteConfigDataDefaults(remoteConfigData);
        }

        if (needsUpdateInRedis) {
            try {
                RedisUtils.setCacheObject(dynamicKeyRemoteGameConfig, remoteConfigData);
                log.debug("远程配置已更新到 Redis, key: {}, value: {}", dynamicKeyRemoteGameConfig, JsonUtils.toJsonString(remoteConfigData));
            } catch (Exception e) {
                log.error("更新远程游戏配置到 Redis 失败, key: {}", dynamicKeyRemoteGameConfig, e);
            }
        }
        return remoteConfigData;
    }

    private RemoteConfigData initDefaultRemoteConfigData() {
        RemoteConfigData newConfigData = new RemoteConfigData();

        GameConfigV2.IntervalAdConfig defaultIntervalAdConfig = new GameConfigV2.IntervalAdConfig();
        defaultIntervalAdConfig.setPlayIntersInterval(DEFAULT_INTERVAL_AD_PLAY_INTERVAL);
        defaultIntervalAdConfig.setFirstNoIntersCount(DEFAULT_INTERVAL_AD_FIRST_NO_INTERS_COUNT);
        defaultIntervalAdConfig.setPlayIntersIntervalLevel(DEFAULT_INTERVAL_AD_PLAY_INTERVAL_LEVEL);
        newConfigData.setIntervalAdConfig(defaultIntervalAdConfig);

        GameConfigV2.RateConfig defaultRateConfig = new GameConfigV2.RateConfig();
        defaultRateConfig.setFirstTriggerLevelCount(DEFAULT_RATE_CONFIG_FIRST_TRIGGER_LEVEL_COUNT);
        defaultRateConfig.setIntervalLevelCount(DEFAULT_RATE_CONFIG_INTERVAL_LEVEL_COUNT);
        defaultRateConfig.setMaxTriggerCount(DEFAULT_RATE_CONFIG_MAX_TRIGGER_COUNT);
        newConfigData.setRateConfig(defaultRateConfig);

        log.debug("初始化默认远程配置数据完成: {}", newConfigData);
        return newConfigData;
    }

    private boolean ensureRemoteConfigDataDefaults(RemoteConfigData remoteConfigData) {
        boolean needsUpdate = false;
        log.debug("开始检查并补全远程配置的默认值: {}", remoteConfigData);

        GameConfigV2.IntervalAdConfig intervalAdConfig = remoteConfigData.getIntervalAdConfig();
        if (intervalAdConfig == null) { // 如果整个 intervalAdConfig 为 null，则初始化
            log.debug("IntervalAdConfig 为 null，进行初始化");
            intervalAdConfig = new GameConfigV2.IntervalAdConfig();
            remoteConfigData.setIntervalAdConfig(intervalAdConfig);
            needsUpdate = true; // 标记需要更新，因为我们创建了新的 IntervalAdConfig 对象
        }
        // 补全 IntervalAdConfig 字段
        if (intervalAdConfig.getPlayIntersInterval() == null) {
            log.debug("补全 IntervalAdConfig.playIntersInterval 为默认值: {}", DEFAULT_INTERVAL_AD_PLAY_INTERVAL);
            intervalAdConfig.setPlayIntersInterval(DEFAULT_INTERVAL_AD_PLAY_INTERVAL);
            needsUpdate = true;
        }
        if (intervalAdConfig.getFirstNoIntersCount() == null) {
            log.debug("补全 IntervalAdConfig.firstNoIntersCount 为默认值: {}", DEFAULT_INTERVAL_AD_FIRST_NO_INTERS_COUNT);
            intervalAdConfig.setFirstNoIntersCount(DEFAULT_INTERVAL_AD_FIRST_NO_INTERS_COUNT);
            needsUpdate = true;
        }
        if (intervalAdConfig.getPlayIntersIntervalLevel() == null) {
            log.debug("补全 IntervalAdConfig.playIntersIntervalLevel 为默认值: {}", DEFAULT_INTERVAL_AD_PLAY_INTERVAL_LEVEL);
            intervalAdConfig.setPlayIntersIntervalLevel(DEFAULT_INTERVAL_AD_PLAY_INTERVAL_LEVEL);
            needsUpdate = true;
        }

        GameConfigV2.RateConfig rateConfig = remoteConfigData.getRateConfig();
        if (rateConfig == null) { // 如果整个 rateConfig 为 null，则初始化
            log.debug("RateConfig 为 null，进行初始化");
            rateConfig = new GameConfigV2.RateConfig();
            remoteConfigData.setRateConfig(rateConfig);
            needsUpdate = true; // 标记需要更新，因为我们创建了新的 RateConfig 对象
        }
        // 补全 RateConfig 字段
        if (rateConfig.getFirstTriggerLevelCount() == null) {
            log.debug("补全 RateConfig.firstTriggerLevelCount 为默认值: {}", DEFAULT_RATE_CONFIG_FIRST_TRIGGER_LEVEL_COUNT);
            rateConfig.setFirstTriggerLevelCount(DEFAULT_RATE_CONFIG_FIRST_TRIGGER_LEVEL_COUNT);
            needsUpdate = true;
        }
        if (rateConfig.getIntervalLevelCount() == null) {
            log.debug("补全 RateConfig.intervalLevelCount 为默认值: {}", DEFAULT_RATE_CONFIG_INTERVAL_LEVEL_COUNT);
            rateConfig.setIntervalLevelCount(DEFAULT_RATE_CONFIG_INTERVAL_LEVEL_COUNT);
            needsUpdate = true;
        }
        if (rateConfig.getMaxTriggerCount() == null) {
            log.debug("补全 RateConfig.maxTriggerCount 为默认值: {}", DEFAULT_RATE_CONFIG_MAX_TRIGGER_COUNT);
            rateConfig.setMaxTriggerCount(DEFAULT_RATE_CONFIG_MAX_TRIGGER_COUNT);
            needsUpdate = true;
        }

        log.debug("检查并补全远程配置的默认值完成，是否需要更新到Redis: {}", needsUpdate);
        return needsUpdate;
    }

    @Override
    public AdminGameConfigV2 getAdminConfig(String abGroup) {
        // 如果abGroup为空，使用默认组
        if (StrUtil.isBlank(abGroup)) {
            abGroup = "default";
        }

        log.debug("获取后台管理系统游戏配置, abGroup: {}", abGroup);

        // 1. 从数据库获取关卡配置列表
        List<LevelConfig> list = list(
                Wrappers.lambdaQuery(LevelConfig.class)
                        .eq(LevelConfig::getAbGroup, abGroup)
                        .orderByAsc(LevelConfig::getLevelPriority)
        );

        List<AdminLevelConfigVo> levelConfigVos = list.stream().map(s -> {
            AdminLevelConfigVo levelConfigVo = BeanUtil.copyProperties(s, AdminLevelConfigVo.class);
            String unlockConditions = levelConfigVo.getUnlockConditions();
            if (StrUtil.isNotBlank(unlockConditions)) {
                List<String> split = StrUtil.split(unlockConditions, CharUtil.AMP);
                levelConfigVo.setUnlockConditionsArray(split);
            }
            return levelConfigVo;
        }).toList();

        log.debug("从数据库获取关卡配置列表，数量: {}", levelConfigVos.size());

        // 2. 从Redis获取远程配置
        RemoteConfigData remoteConfigData = getOrInitRemoteConfigData(abGroup);

        // 3. 转换为AdminGameConfigV2
        AdminGameConfigV2 adminGameConfigV2 = new AdminGameConfigV2();
        adminGameConfigV2.setAbGroup(abGroup);
        adminGameConfigV2.setLevelConfigs(levelConfigVos);

        // 转换IntervalAdConfig
        AdminGameConfigV2.IntervalAdConfig adminIntervalAdConfig = new AdminGameConfigV2.IntervalAdConfig();
        BeanUtil.copyProperties(remoteConfigData.getIntervalAdConfig(), adminIntervalAdConfig);
        adminGameConfigV2.setIntervalAdConfig(adminIntervalAdConfig);

        // 转换RateConfig
        AdminGameConfigV2.RateConfig adminRateConfig = new AdminGameConfigV2.RateConfig();
        BeanUtil.copyProperties(remoteConfigData.getRateConfig(), adminRateConfig);
        adminGameConfigV2.setRateConfig(adminRateConfig);

        log.debug("后台管理系统游戏配置获取完成: {}", adminGameConfigV2);
        return adminGameConfigV2;
    }

    @Override
    public boolean updateAdminConfig(AdminGameConfigV2 config) {
        if (config == null || StrUtil.isBlank(config.getAbGroup())) {
            log.error("更新游戏配置失败，配置为空或AB测试组为空");
            return false;
        }

        String abGroup = config.getAbGroup();
        log.debug("更新后台管理系统游戏配置, abGroup: {}", abGroup);

        try {
            // 1. 更新远程配置到Redis
            RemoteConfigData remoteConfigData = new RemoteConfigData();

            // 转换IntervalAdConfig
            GameConfigV2.IntervalAdConfig intervalAdConfig = new GameConfigV2.IntervalAdConfig();
            BeanUtil.copyProperties(config.getIntervalAdConfig(), intervalAdConfig);
            remoteConfigData.setIntervalAdConfig(intervalAdConfig);

            // 转换RateConfig
            GameConfigV2.RateConfig rateConfig = new GameConfigV2.RateConfig();
            BeanUtil.copyProperties(config.getRateConfig(), rateConfig);
            remoteConfigData.setRateConfig(rateConfig);

            // 保存到Redis
            String dynamicKeyRemoteGameConfig = KEY_REMOTE_GAME_CONFIG + ":" + abGroup;
            RedisUtils.setCacheObject(dynamicKeyRemoteGameConfig, remoteConfigData);
            log.debug("远程配置已更新到Redis, key: {}, value: {}", dynamicKeyRemoteGameConfig, JsonUtils.toJsonString(remoteConfigData));

            // 2. 更新关卡配置到数据库
            if (config.getLevelConfigs() != null && !config.getLevelConfigs().isEmpty()) {
                for (AdminLevelConfigVo levelConfigVo : config.getLevelConfigs()) {
                    // 将unlockConditionsArray转换为unlockConditions字符串
                    if (levelConfigVo.getUnlockConditionsArray() != null && !levelConfigVo.getUnlockConditionsArray().isEmpty()) {
                        levelConfigVo.setUnlockConditions(String.join("&", levelConfigVo.getUnlockConditionsArray()));
                    }

                    // 检查关卡是否存在
                    LevelConfig existingLevel = getOne(Wrappers.lambdaQuery(LevelConfig.class)
                            .eq(LevelConfig::getAbGroup, levelConfigVo.getAbGroup())
                            .eq(LevelConfig::getLevelId, levelConfigVo.getLevelId()));
                    LevelConfig levelConfig = BeanUtil.copyProperties(levelConfigVo, LevelConfig.class);
                    if (existingLevel != null) {
                        // 更新现有
                        levelConfig.setId(existingLevel.getId());
                        updateById(levelConfig);
                        log.debug("更新关卡配置: {}", levelConfig);
                    } else {
                        // 新增关卡
                        save(levelConfig);
                        log.debug("新增关卡配置: {}", levelConfig);
                    }
                }
            }

            return true;
        } catch (Exception e) {
            log.error("更新游戏配置失败", e);
            return false;
        }
    }


    @Override
    public List<VersionInfo> getAllVersions() {
        try {
            // 从Redis Hash中获取所有版本信息
            Map<String, VersionInfo> versionMap = RedisUtils.getCacheMap(KEY_VERSIONS);

            if (versionMap == null || versionMap.isEmpty()) {
                log.debug("没有找到任何游戏配置版本");
                return new ArrayList<>();
            }

            // 转换为List并按上传时间倒序排列
            List<VersionInfo> versionList = new ArrayList<>(versionMap.values());
            versionList.sort((v1, v2) -> {
                if (v1.getUploadTime() == null && v2.getUploadTime() == null) {
                    return 0;
                }
                if (v1.getUploadTime() == null) {
                    return 1;
                }
                if (v2.getUploadTime() == null) {
                    return -1;
                }
                return v2.getUploadTime().compareTo(v1.getUploadTime());
            });

            log.debug("成功获取游戏配置版本列表，数量: {}", versionList.size());
            return versionList;

        } catch (Exception e) {
            log.error("获取游戏配置版本列表时发生异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public String deleteGameConfigVersion(String version) {
        try {
            if (StrUtil.isBlank(version)) {
                return "版本号不能为空";
            }

            // 构建Redis key
            String redisKey = KEY_XLSX_CONFIG_PREFIX + version;

            // 检查版本是否存在
            if (!RedisUtils.isExistsObject(redisKey)) {
                RedisUtils.delCacheMapValue(KEY_VERSIONS, version);
                return "指定版本不存在: " + version;
            }

            // 删除XLSX文件
            boolean xlsxDeleted = RedisUtils.deleteObject(redisKey);
            if (!xlsxDeleted) {
                RedisUtils.delCacheMapValue(KEY_VERSIONS, version);
                return "删除XLSX文件失败";
            }

            // 从版本列表中移除该版本记录
            RedisUtils.delCacheMapValue(KEY_VERSIONS, version);

            log.info("成功删除游戏配置版本: {}", version);
            return null; // 成功返回null

        } catch (Exception e) {
            log.error("删除游戏配置版本时发生异常，版本: {}", version, e);
            return "删除版本时发生异常：" + e.getMessage();
        }
    }

    @Override
    public String uploadGameConfigXlsx(MultipartFile file, String version) {
        try {
            if (file.isEmpty()) {
                return "文件不能为空";
            }

            if (StrUtil.isBlank(version)) {
                return "版本号不能为空";
            }

            // 验证文件格式
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".xlsx")) {
                return "文件格式错误，只支持XLSX格式";
            }

            // 解析Excel文件
            ExcelGameConfig config = parseExcelFile(file);

            // 构建Redis key
            String redisKey = KEY_XLSX_CONFIG_PREFIX + version;

            // 存储到Redis
            RedisUtils.setCacheObject(redisKey, config);

            // 记录版本信息到版本列表
            VersionInfo versionInfo = new VersionInfo(
                    version,
                    LocalDateTime.now(),
                    "active",
                    originalFilename
            );

            // 将版本信息存储到Redis Hash中
            RedisUtils.setCacheMapValue(KEY_VERSIONS, version, versionInfo);

            log.info("成功上传游戏配置XLSX文件到Redis，版本: {}", version);
            return null; // 成功返回null

        } catch (Exception e) {
            log.error("上传XLSX文件时发生异常，版本: {}", version, e);
            return "上传XLSX文件时发生异常：" + e.getMessage();
        }
    }

    @Override
    public ExcelGameConfig getGameConfigXlsxData(String version) {
        if (StrUtil.isBlank(version)) {
            log.warn("获取游戏配置XLSX数据失败，版本号为空");
            return null;
        }

        // 构建Redis key
        String redisKey = KEY_XLSX_CONFIG_PREFIX + version;

        // 从Redis获取JSON内容
        ExcelGameConfig jsonContent = RedisUtils.getCacheObject(redisKey);

        log.debug("成功从Redis获取游戏配置XLSX数据，版本: {},", version);
        return jsonContent;
    }

    @Override
    public ExcelGameConfig getGameConfigXlsxForWorker(BaseVo vo) {
        if (vo == null || vo.getClientInfo() == null) {
            log.warn("获取Worker游戏配置XLSX文件失败，客户端信息为空");
            return null;
        }

        // 从clientInfo中获取版本号
        String version = vo.getClientInfo().getGameVersion();
        if (StrUtil.isBlank(version)) {
            log.warn("获取Worker游戏配置XLSX文件失败，游戏版本为空");
            return null;
        }

        // 构建Redis key
        String redisKey = KEY_XLSX_CONFIG_PREFIX + version;

        // 从Redis获取JSON内容
        return RedisUtils.getCacheObject(redisKey);
    }

    /**
     * 解析Excel文件内容
     *
     * @param file Excel文件
     * @return 解析后的配置数据
     * @throws Exception 解析异常
     */
    private ExcelGameConfig parseExcelFile(MultipartFile file) throws Exception {
        ExcelGameConfig config = new ExcelGameConfig();

        try {
            // 解析levelConfig工作表
            List<Map<Integer, String>> levelData = readSheetData(file, "levelConfig");
            config.setLevelConfig(parseLevelConfigFromMap(levelData));

            // 解析itemConfig工作表
            List<Map<Integer, String>> itemData = readSheetData(file, "itemConfig");
            config.setItemConfig(parseItemConfigFromMap(itemData));

            // 解析commonConfig工作表
            List<Map<Integer, String>> commonData = readSheetData(file, "commonConfig");
            config.setCommonConfig(parseCommonConfigFromMap(commonData));

            // 解析collectiblesConfig工作表
            List<Map<Integer, String>> collectiblesData = readSheetData(file, "collectiblesConfig");
            config.setCollectiblesConfig(parseCollectiblesConfigFromMap(collectiblesData));

            // 解析collectiblesImageConfig工作表
            List<Map<Integer, String>> collectiblesImageData = readSheetData(file, "collectiblesImageConfig");
            config.setCollectiblesImageConfig(parseCollectiblesImageConfigFromMap(collectiblesImageData));

        } catch (Exception e) {
            log.error("解析Excel文件失败", e);
            throw new Exception("解析Excel文件失败: " + e.getMessage());
        }

        return config;
    }

    /**
     * 读取指定工作表的数据
     */
    private List<Map<Integer, String>> readSheetData(MultipartFile file, String sheetName) throws IOException {
        List<Map<Integer, String>> dataList = new ArrayList<>();

        try {
            FastExcel.read(file.getInputStream())
                    .sheet(sheetName)
                    .registerReadListener(new AnalysisEventListener<Map<Integer, String>>() {
                        @Override
                        public void invoke(Map<Integer, String> data, AnalysisContext context) {
                            dataList.add(data);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                            // 读取完成
                            log.debug("成功读取工作表 {} 的数据，共 {} 行", sheetName, dataList.size());
                        }
                    })
                    .doRead();
        } catch (Exception e) {
            log.error("读取工作表 {} 时发生异常: {}", sheetName, e.getMessage(), e);

            // 对于关键工作表，记录更详细的错误信息
            if ("commonConfig".equals(sheetName)) {
                log.error("❌ commonConfig工作表读取失败！这将导致通用配置解析为空！");
                log.error("异常详情: ", e);
                log.error("请检查Excel文件中是否存在名为'commonConfig'的工作表");
            }

            // 如果工作表不存在或读取失败，返回空列表而不是抛出异常
            // 这样可以让其他工作表继续正常解析
        }

        log.info("工作表 {} 读取完成，共读取 {} 行数据", sheetName, dataList.size());

        return dataList;
    }

    /**
     * 解析levelConfig数据
     */
    private List<ExcelGameConfig.LevelConfigItem> parseLevelConfigFromMap(List<Map<Integer, String>> dataList) {
        List<ExcelGameConfig.LevelConfigItem> items = new ArrayList<>();

        // levelConfig工作表前3行是标题（cs、数据类型、英文字段名），第4行开始是数据，所以跳过前3行（索引0-2）
        // 注意：FastExcel会跳过第一行中文标题，所以实际结构是：索引0=cs行，索引1=数据类型行，索引2=字段名行，索引3=第一行数据
        for (int i = 3; i < dataList.size(); i++) {
            Map<Integer, String> row = dataList.get(i);

            if (row == null || row.isEmpty()) {
                continue;
            }

            ExcelGameConfig.LevelConfigItem item = new ExcelGameConfig.LevelConfigItem();

            // 解析基本字段
            Integer id = parseInteger(getValueFromMap(row, 0));
            if (id == null) {
                continue; // 跳过ID解析失败的行
            }

            item.setId(id);
            item.setValid(parseInteger(getValueFromMap(row, 1)));
            item.setNode(parseInteger(getValueFromMap(row, 2)));
            item.setIshard(getValueFromMap(row, 3));
            item.setChapter(parseInteger(getValueFromMap(row, 4)));
            item.setChapter_node(parseInteger(getValueFromMap(row, 5)));
            item.setPic(getValueFromMap(row, 6));

            // 解析数组字段
            item.setColor(parseStringArray(getValueFromMap(row, 7)));
            item.setSlot_state(parseIntegerArray(getValueFromMap(row, 8)));
            item.setSlot(parseIntegerArray(getValueFromMap(row, 9)));
            item.setSlot_cut(parseIntegerArrayArray(getValueFromMap(row, 10)));

            // 解析card相关字段
            item.setCard_sort_001(parseIntegerArray(getValueFromMap(row, 11)));
            item.setCard_num_001(parseIntegerArray(getValueFromMap(row, 12)));
            item.setCard_extra_001(parseIntegerArray(getValueFromMap(row, 13)));

            item.setCard_sort_002(parseIntegerArray(getValueFromMap(row, 14)));
            item.setCard_num_002(parseIntegerArray(getValueFromMap(row, 15)));
            item.setCard_extra_002(parseIntegerArray(getValueFromMap(row, 16)));

            item.setCard_sort_003(parseIntegerArray(getValueFromMap(row, 17)));
            item.setCard_num_003(parseIntegerArray(getValueFromMap(row, 18)));
            item.setCard_extra_003(parseIntegerArray(getValueFromMap(row, 19)));

            item.setCard_sort_004(parseIntegerArray(getValueFromMap(row, 20)));
            item.setCard_num_004(parseIntegerArray(getValueFromMap(row, 21)));
            item.setCard_extra_004(parseIntegerArray(getValueFromMap(row, 22)));

            // 解析奖励字段
            item.setCost(getValueFromMap(row, 23));
            item.setReward(getValueFromMap(row, 24));
            // ads_reward列在Excel文件中不存在，设置为null

            items.add(item);
        }

        return items;
    }

    /**
     * 解析itemConfig数据
     */
    private List<ExcelGameConfig.ItemConfigItem> parseItemConfigFromMap(List<Map<Integer, String>> dataList) {
        List<ExcelGameConfig.ItemConfigItem> items = new ArrayList<>();

        // itemConfig工作表前3行是标题，第4行开始是数据，所以跳过前3行（索引0-2）
        for (int i = 3; i < dataList.size(); i++) {
            Map<Integer, String> row = dataList.get(i);
            if (row == null || row.isEmpty()) continue;

            ExcelGameConfig.ItemConfigItem item = new ExcelGameConfig.ItemConfigItem();
            item.setId(parseInteger(getValueFromMap(row, 0)));
            item.setValid(parseInteger(getValueFromMap(row, 1)));
            item.setType(parseInteger(getValueFromMap(row, 2)));
            item.setSub(parseInteger(getValueFromMap(row, 3)));
            item.setReward_pool(parseInteger(getValueFromMap(row, 4)));
            item.setName(getValueFromMap(row, 5));
            item.setDes(getValueFromMap(row, 6));
            item.setIcon(parseStringArray(getValueFromMap(row, 7)));
            item.setJump(parseIntegerArray(getValueFromMap(row, 8)));

            items.add(item);
        }

        return items;
    }

    /**
     * 解析commonConfig数据
     * 根据第三列的字段数据结构标识来精确解析数据类型
     */
    private ExcelGameConfig.CommonConfigItem parseCommonConfigFromMap(List<Map<Integer, String>> dataList) {
        ExcelGameConfig.CommonConfigItem config = new ExcelGameConfig.CommonConfigItem();
        Map<String, Object> configMap = new HashMap<>();

        log.info("🔍 开始解析 commonConfig 数据，总行数: {}", dataList.size());

        if (dataList.isEmpty()) {
            log.error("❌ commonConfig 数据列表为空！这可能是因为工作表读取失败");
            log.error("请检查:");
            log.error("1. Excel文件中是否存在'commonConfig'工作表");
            log.error("2. 工作表名称是否完全匹配（区分大小写）");
            log.error("3. 工作表是否包含数据");
            return config;
        }

        // 打印前几行数据用于调试
        log.debug("commonConfig 前3行数据预览:");
        for (int i = 0; i < Math.min(3, dataList.size()); i++) {
            Map<Integer, String> row = dataList.get(i);
            log.debug("  第{}行: {}", i + 1, row);
        }

        // commonConfig工作表结构特殊：FastExcel跳过第一行中文标题后，第1行就是数据，所以从索引0开始解析
        int successCount = 0;
        int skipCount = 0;

        for (int i = 0; i < dataList.size(); i++) {
            Map<Integer, String> row = dataList.get(i);
            if (row == null || row.isEmpty()) {
                log.debug("第{}行数据为空，跳过", i + 1);
                skipCount++;
                continue;
            }

            String key = getValueFromMap(row, 0);           // 字段标识
            String value = getValueFromMap(row, 1);         // 字段值
            String dataType = getValueFromMap(row, 2);      // 字段数据结构

            log.debug("第{}行: key='{}', value='{}', dataType='{}'", i + 1, key, value, dataType);

            if (StrUtil.isNotBlank(key)) {
                try {
                    if (StrUtil.isNotBlank(value)) {
                        // 根据数据结构标识来解析值
                        Object convertedValue = convertValueByDataType(value, dataType);
                        configMap.put(key, convertedValue);
                        log.debug("✅ 解析配置项: {} = {} (数据结构: {}, 实际类型: {})",
                                key, convertedValue, dataType, convertedValue.getClass().getSimpleName());
                        successCount++;
                    } else {
                        configMap.put(key, "");
                        log.debug("⚠️  空值设置为空字符串: {}", key);
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("❌ 解析配置项 {} 时发生异常: {}", key, e.getMessage());
                    log.error("原始值: '{}', 数据类型: '{}'", value, dataType);
                    // 发生异常时设置为原始字符串值
                    configMap.put(key, value != null ? value : "");
                    successCount++;
                }
            } else {
                log.debug("第{}行key为空，跳过", i + 1);
                skipCount++;
            }
        }

        log.info("commonConfig 数据解析统计:");
        log.info("  总行数: {}", dataList.size());
        log.info("  成功解析: {} 个配置项", successCount);
        log.info("  跳过行数: {}", skipCount);
        log.info("  期望配置项数: 30");

        if (successCount == 0) {
            log.error("❌ 没有成功解析任何配置项！请检查数据格式");
            return config;
        }

        if (successCount < 30) {
            log.warn("⚠️  解析的配置项数量({})少于期望数量(30)", successCount);
        }

        // 将Map中的值设置到CommonConfigItem对象中
        try {
            setCommonConfigFields(config, configMap);
            log.info("✅ 成功解析 commonConfig，共 {} 个配置项", configMap.size());
        } catch (Exception e) {
            log.error("❌ 设置CommonConfigItem字段时发生异常: {}", e.getMessage(), e);
        }

        return config;
    }

    /**
     * 将Map中的配置值设置到CommonConfigItem对象中
     */
    private void setCommonConfigFields(ExcelGameConfig.CommonConfigItem config, Map<String, Object> configMap) {
        // 使用反射或者直接设置字段值
        try {
            if (configMap.containsKey("Initial_Money")) {
                config.setInitial_Money((Integer) configMap.get("Initial_Money"));
            }
            if (configMap.containsKey("Initial_Power")) {
                config.setInitial_Power((Integer) configMap.get("Initial_Power"));
            }
            if (configMap.containsKey("Power_Recovery_Time")) {
                config.setPower_Recovery_Time((Integer) configMap.get("Power_Recovery_Time"));
            }
            if (configMap.containsKey("Power_Recovery_Value")) {
                config.setPower_Recovery_Value((Integer) configMap.get("Power_Recovery_Value"));
            }
            if (configMap.containsKey("General_Slot_Grid")) {
                // General_Slot_Grid在Excel中是int[]类型，应该正确转换为List<Integer>
                config.setGeneral_Slot_Grid((List<Integer>) configMap.get("General_Slot_Grid"));
            }
            if (configMap.containsKey("General_Slot_Cost")) {
                // General_Slot_Cost在Excel中是int[]类型，应该正确转换为List<Integer>
                config.setGeneral_Slot_Cost((List<Integer>) configMap.get("General_Slot_Cost"));
            }
            if (configMap.containsKey("GoOnCost")) {
                config.setGoOnCost((Integer) configMap.get("GoOnCost"));
            }
            if (configMap.containsKey("Item_Value")) {
                config.setItem_Value((List<String>) configMap.get("Item_Value"));
            }
            if (configMap.containsKey("Main_Unlock")) {
                // Main_Unlock在Excel中是int类型，需要转换为List<String>
                Object value = configMap.get("Main_Unlock");
                if (value instanceof Integer) {
                    config.setMain_Unlock(List.of(value.toString()));
                } else if (value instanceof List) {
                    config.setMain_Unlock((List<String>) value);
                }
            }
            if (configMap.containsKey("Collect_Unlock")) {
                // Collect_Unlock在Excel中是int类型，需要转换为List<String>
                Object value = configMap.get("Collect_Unlock");
                if (value instanceof Integer) {
                    config.setCollect_Unlock(List.of(value.toString()));
                } else if (value instanceof List) {
                    config.setCollect_Unlock((List<String>) value);
                }
            }
            if (configMap.containsKey("Extra_Slot")) {
                // Extra_Slot在Excel中是int类型，需要转换为List<String>
                Object value = configMap.get("Extra_Slot");
                if (value instanceof Integer) {
                    config.setExtra_Slot(List.of(value.toString()));
                } else if (value instanceof List) {
                    config.setExtra_Slot((List<String>) value);
                }
            }
            if (configMap.containsKey("Hide_Card")) {
                // Hide_Card在Excel中是int类型，需要转换为List<String>
                Object value = configMap.get("Hide_Card");
                if (value instanceof Integer) {
                    config.setHide_Card(List.of(value.toString()));
                } else if (value instanceof List) {
                    config.setHide_Card((List<String>) value);
                }
            }
            if (configMap.containsKey("Extra_Rv")) {
                // Extra_Rv在Excel中是string类型，需要转换为List<String>
                Object value = configMap.get("Extra_Rv");
                if (value instanceof String) {
                    config.setExtra_Rv(List.of((String) value));
                } else if (value instanceof List) {
                    config.setExtra_Rv((List<String>) value);
                }
            }
            if (configMap.containsKey("Shuffle_Card")) {
                // Shuffle_Card在Excel中是string类型，需要转换为List<String>
                Object value = configMap.get("Shuffle_Card");
                if (value instanceof String) {
                    config.setShuffle_Card(List.of((String) value));
                } else if (value instanceof List) {
                    config.setShuffle_Card((List<String>) value);
                }
            }
            if (configMap.containsKey("Hammer")) {
                // Hammer在Excel中是string类型，需要转换为List<String>
                Object value = configMap.get("Hammer");
                if (value instanceof String) {
                    config.setHammer(List.of((String) value));
                } else if (value instanceof List) {
                    config.setHammer((List<String>) value);
                }
            }

            if (configMap.containsKey("Level_Finish_Rward_Rates")) {
                config.setLevel_Finish_Rward_Rates((List<Integer>) configMap.get("Level_Finish_Rward_Rates"));
            }
            if (configMap.containsKey("Color_001")) {
                config.setColor_001((String) configMap.get("Color_001"));
            }
            if (configMap.containsKey("Color_002")) {
                config.setColor_002((String) configMap.get("Color_002"));
            }
            if (configMap.containsKey("Color_003")) {
                config.setColor_003((String) configMap.get("Color_003"));
            }
            if (configMap.containsKey("Color_004")) {
                config.setColor_004((String) configMap.get("Color_004"));
            }
            if (configMap.containsKey("Color_005")) {
                config.setColor_005((String) configMap.get("Color_005"));
            }
            if (configMap.containsKey("Color_006")) {
                config.setColor_006((String) configMap.get("Color_006"));
            }
            if (configMap.containsKey("Color_007")) {
                config.setColor_007((String) configMap.get("Color_007"));
            }
            if (configMap.containsKey("Color_008")) {
                config.setColor_008((String) configMap.get("Color_008"));
            }
            if (configMap.containsKey("Giftbag_001")) {
                config.setGiftbag_001((List<String>) configMap.get("Giftbag_001"));
            }
            if (configMap.containsKey("ShopItem_001")) {
                config.setShopItem_001((Integer) configMap.get("ShopItem_001"));
            }
            if (configMap.containsKey("ShopItem_002")) {
                config.setShopItem_002((Integer) configMap.get("ShopItem_002"));
            }
            if (configMap.containsKey("ShopItem_003")) {
                config.setShopItem_003((Integer) configMap.get("ShopItem_003"));
            }
            if (configMap.containsKey("ShopItem_004")) {
                config.setShopItem_004((Integer) configMap.get("ShopItem_004"));
            }
            if (configMap.containsKey("ShopItem_005")) {
                config.setShopItem_005((Integer) configMap.get("ShopItem_005"));
            }
            if (configMap.containsKey("ShopItem_006")) {
                config.setShopItem_006((Integer) configMap.get("ShopItem_006"));
            }
        } catch (Exception e) {
            log.warn("设置CommonConfig字段时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 解析collectiblesConfig数据
     */
    private List<ExcelGameConfig.CollectiblesConfigItem> parseCollectiblesConfigFromMap(List<Map<Integer, String>> dataList) {
        List<ExcelGameConfig.CollectiblesConfigItem> items = new ArrayList<>();

        // collectiblesConfig工作表前3行是标题，第4行开始是数据，所以跳过前3行（索引0-2）
        for (int i = 3; i < dataList.size(); i++) {
            Map<Integer, String> row = dataList.get(i);
            if (row == null || row.isEmpty()) continue;

            ExcelGameConfig.CollectiblesConfigItem item = new ExcelGameConfig.CollectiblesConfigItem();
            item.setId(parseInteger(getValueFromMap(row, 0)));
            item.setTheme_name(getValueFromMap(row, 1));
            item.setPic_path(getValueFromMap(row, 2));
            item.setUnlock_lvl(parseInteger(getValueFromMap(row, 3)));
            item.setReward_id(parseStringArray(getValueFromMap(row, 4)));

            items.add(item);
        }

        return items;
    }

    /**
     * 解析collectiblesImageConfig数据
     */
    private List<ExcelGameConfig.CollectiblesImageConfigItem> parseCollectiblesImageConfigFromMap(List<Map<Integer, String>> dataList) {
        List<ExcelGameConfig.CollectiblesImageConfigItem> items = new ArrayList<>();

        // collectiblesImageConfig工作表前3行是标题，第4行开始是数据，所以跳过前3行（索引0-2）
        for (int i = 3; i < dataList.size(); i++) {
            Map<Integer, String> row = dataList.get(i);
            if (row == null || row.isEmpty()) continue;

            ExcelGameConfig.CollectiblesImageConfigItem item = new ExcelGameConfig.CollectiblesImageConfigItem();
            item.setId(parseInteger(getValueFromMap(row, 0)));
            item.setTheme(parseInteger(getValueFromMap(row, 1)));
            item.setOrder(parseInteger(getValueFromMap(row, 2)));
            item.setImage_id(getValueFromMap(row, 3));
            item.setUse_color(parseStringArray(getValueFromMap(row, 4)));
            item.setRequire_level_id(parseStringArray(getValueFromMap(row, 5)));

            items.add(item);
        }

        return items;
    }

    /**
     * 从Map中获取指定索引的值
     */
    private String getValueFromMap(Map<Integer, String> row, int index) {
        String value = row.get(index);
        return value != null ? value.trim() : "";
    }

    /**
     * 解析单个整数值
     */
    private Integer parseInteger(String value) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析整数: {}", value);
            return null;
        }
    }

    /**
     * 解析字符串数组（支持逗号和#分隔）
     */
    private List<String> parseStringArray(String value) {
        if (StrUtil.isBlank(value)) {
            return new ArrayList<>();
        }

        // 支持逗号和#分隔符
        String[] parts = value.contains("#") ? value.split("#") : value.split(",");
        List<String> result = new ArrayList<>();
        for (String part : parts) {
            result.add(part.trim());
        }
        return result;
    }

    /**
     * 解析整数数组（支持逗号和#分隔）
     */
    private List<Integer> parseIntegerArray(String value) {
        if (StrUtil.isBlank(value)) {
            return new ArrayList<>();
        }

        // 支持逗号和#分隔符
        String[] parts = value.contains("#") ? value.split("#") : value.split(",");
        List<Integer> result = new ArrayList<>();
        for (String part : parts) {
            try {
                result.add(Integer.parseInt(part.trim()));
            } catch (NumberFormatException e) {
                log.warn("无法解析整数: {}", part);
            }
        }
        return result;
    }

    /**
     * 解析二维整数数组（支持分号和|分隔一维数组，逗号和#分隔元素）
     */
    private List<List<Integer>> parseIntegerArrayArray(String value) {
        if (StrUtil.isBlank(value)) {
            return new ArrayList<>();
        }

        // 支持分号和|分隔一维数组
        String[] arrays = value.contains("|") ? value.split("\\|") : value.split(";");
        List<List<Integer>> result = new ArrayList<>();
        for (String array : arrays) {
            result.add(parseIntegerArray(array.trim()));
        }
        return result;
    }

    /**
     * 根据数据结构标识将字符串值转换为对应的类型
     *
     * @param value    字符串值
     * @param dataType 数据结构标识 (int, int[], string, string[] 等)
     * @return 转换后的对象
     */
    private Object convertValueByDataType(String value, String dataType) {
        if (StrUtil.isBlank(value)) {
            return "";
        }

        if (StrUtil.isBlank(dataType)) {
            return value; // 默认返回字符串
        }

        String trimmedValue = value.trim();
        String trimmedDataType = dataType.trim().toLowerCase();

        try {
            switch (trimmedDataType) {
                case "int":
                    return Integer.parseInt(trimmedValue);
                case "long":
                    return Long.parseLong(trimmedValue);
                case "double":
                case "float":
                    return Double.parseDouble(trimmedValue);
                case "boolean":
                case "bool":
                    return Boolean.parseBoolean(trimmedValue) || "1".equals(trimmedValue) || "true".equalsIgnoreCase(trimmedValue);
                case "int[]":
                    // 解析整数数组，支持#和,分隔符
                    if (trimmedValue.contains("#")) {
                        return Arrays.stream(trimmedValue.split("#"))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .map(Integer::parseInt)
                                .collect(Collectors.toList());
                    } else if (trimmedValue.contains(",")) {
                        return Arrays.stream(trimmedValue.split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .map(Integer::parseInt)
                                .collect(Collectors.toList());
                    } else {
                        return Collections.singletonList(Integer.parseInt(trimmedValue));
                    }
                case "string[]":
                    // 解析字符串数组，支持#和,分隔符
                    if (trimmedValue.contains("#")) {
                        return Arrays.stream(trimmedValue.split("#"))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .collect(Collectors.toList());
                    } else if (trimmedValue.contains(",")) {
                        return Arrays.stream(trimmedValue.split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .collect(Collectors.toList());
                    } else {
                        return Collections.singletonList(trimmedValue);
                    }
                default:
                    return trimmedValue; // 默认作为字符串处理
            }
        } catch (Exception e) {
            log.warn("⚠️  数据类型转换失败: 值='{}', 类型='{}', 错误: {}", trimmedValue, trimmedDataType, e.getMessage());
            return trimmedValue; // 转换失败时返回原始字符串
        }
    }

    /**
     * 将字符串值转换为合适的类型（保留原方法作为备用）
     *
     * @param value 字符串值
     * @return 转换后的对象
     */
    private Object convertValue(String value) {
        if (StrUtil.isBlank(value)) {
            return value;
        }

        value = value.trim();

        // 尝试转换为整数
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            // 不是整数，继续尝试其他类型
        }

        // 尝试转换为长整数
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            // 不是长整数，继续尝试其他类型
        }

        // 尝试转换为浮点数
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            // 不是浮点数，继续尝试其他类型
        }

        // 尝试转换为布尔值
        if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
            return Boolean.parseBoolean(value);
        }

        // 默认返回字符串
        return value;
    }
}
