package me.supernova.dashboard.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.dashboard.mapper.SysActionLogMapper;
import me.supernova.dashboard.model.bo.SysActionLogBo;
import me.supernova.dashboard.model.entity.SysActionLog;
import me.supernova.dashboard.model.vo.SysActionLogVo;
import me.supernova.dashboard.service.SysActionLogService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统日志 服务实现类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class SysActionLogServiceImpl extends ServiceImpl<SysActionLogMapper, SysActionLog> implements SysActionLogService {

    private final SysActionLogMapper baseMapper;

    /**
     * 查询系统日志列表
     */
    @Override
    public TableDataInfo<SysActionLogVo> selectPageActionLogList(SysActionLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysActionLog> lqw = buildQueryWrapper(bo);
        Page<SysActionLogVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 查询系统日志列表
     */
    @Override
    public List<SysActionLogVo> selectActionLogList(SysActionLogBo bo) {
        LambdaQueryWrapper<SysActionLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询系统日志详细信息
     */
    @Override
    public SysActionLogVo selectActionLogById(Long id) {
        return baseMapper.selectVoById(id);
    }

    private LambdaQueryWrapper<SysActionLog> buildQueryWrapper(SysActionLogBo bo) {
        return Wrappers.lambdaQuery(SysActionLog.class)
                .in(CollUtil.isNotEmpty(bo.getCreateByIds()), SysActionLog::getCreateBy, bo.getCreateByIds())
                .eq(StrUtil.isNotBlank(bo.getTraceId()), SysActionLog::getTraceId, bo.getTraceId())
                .eq(StrUtil.isNotBlank(bo.getModule()), SysActionLog::getModule, bo.getModule())
                .like(StrUtil.isNotBlank(bo.getDescription()), SysActionLog::getDescription, bo.getDescription())
                .eq(bo.getStatus() != null, SysActionLog::getStatus, bo.getStatus())
                .eq(StrUtil.isNotBlank(bo.getIp()), SysActionLog::getIp, bo.getIp())
                .ge(bo.getBeginTime() != null, SysActionLog::getCreateTime, bo.getBeginTime())
                .le(bo.getEndTime() != null, SysActionLog::getCreateTime, bo.getEndTime())
                .orderByDesc(SysActionLog::getCreateTime);
    }
}
