package me.supernova.dashboard.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.WeightRandom;
import lombok.extern.slf4j.Slf4j;
import me.supernova.dashboard.constant.AbTestGroupConstant;
import me.supernova.dashboard.service.ABTestService;
import org.springframework.stereotype.Service;

/**
 * AB测试服务实现类
 * 以后可能会出现多个维度的 测试组,所以用 json 来存储
 * <p>示例JSON：</p>
 * {@snippet :
 * """
 * {
 *   "gameConfig": "A",
 *   "adConfig": "B"
 * }
 * """
 *}
 */
@Slf4j
@Service
public class ABTestServiceImpl implements ABTestService {

    // AB测试分组配置
    private static final WeightRandom<String> AB_TEST_CONFIG = WeightRandom.<String>create()
            .add("A", 100)
            .add("B", 0);

    @Override
    public Dict getABTestGroup(Long userId) {
        // 使用 WeightRandom 进行加权随机选择
        String gameConfig = AB_TEST_CONFIG.next();
        return Dict.create().set(AbTestGroupConstant.LEVEL_SETTING, gameConfig);
    }
} 