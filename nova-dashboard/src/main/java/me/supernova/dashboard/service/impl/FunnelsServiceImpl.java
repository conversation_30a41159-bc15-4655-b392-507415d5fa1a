package me.supernova.dashboard.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.dashboard.mapper.EventLogMapper;
import me.supernova.dashboard.mapper.FunnelsMapper;
import me.supernova.dashboard.model.bo.FunnelAnalysisBO;
import me.supernova.dashboard.model.dto.TimeConfig;
import me.supernova.dashboard.model.dto.UserEventSequenceDTO;
import me.supernova.dashboard.model.entity.Funnels;
import me.supernova.dashboard.service.FunnelsService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 漏斗表，记录各类漏斗信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FunnelsServiceImpl extends ServiceImpl<FunnelsMapper, Funnels> implements FunnelsService {

    private final EventLogMapper eventLogMapper;

    @Override
    public List<UserEventSequenceDTO> analyzeFunnelEvents(FunnelAnalysisBO bo) {
        Funnels funnels = getById(bo.getFunnelId());
        if (funnels == null) {
            log.warn("[info]未找到指定的漏斗配置：funnelId={}", bo.getFunnelId());
            return Collections.emptyList();
        }

        log.debug("成功获取漏斗配置：name={}", funnels.getName());

        TimeConfig timeConfig = buildTimeConfig(bo);
        return funnels.getParam().getParamList().stream()
                .map(paramElement -> {
                    Integer count = eventLogMapper.countDistinctUsersByEventCriteria(
                            timeConfig,
                            bo.getQueryBo(),
                            paramElement
                    );
                    count = count == null ? 0 : count;
                    return UserEventSequenceDTO.builder()
                            .userCount(count)
                            .sort(paramElement.getSort())
                            .filterName(paramElement.getFilterName())
                            .build();
                })
                .toList();
    }

    /**
     * 构建时间配置
     *
     * @param bo 漏斗分析业务对象
     * @return 时间配置
     */
    private static TimeConfig buildTimeConfig(FunnelAnalysisBO bo) {
        Integer timezone = bo.getQueryBo().getTimezone();
        String timeColumn = timezone == 8 ? "event_time" : "event_time_utc";
        LocalDateTime startTime = bo.getQueryBo().getStartDate().atStartOfDay();
        LocalDateTime endTime = bo.getQueryBo().getEndDate().atTime(LocalTime.MAX);

        return new TimeConfig(startTime, endTime, timeColumn);
    }
}
