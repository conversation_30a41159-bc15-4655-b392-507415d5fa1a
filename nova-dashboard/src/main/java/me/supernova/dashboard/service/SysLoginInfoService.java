package me.supernova.dashboard.service;


import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.dashboard.model.bo.SysLoginInfoBo;
import me.supernova.dashboard.model.entity.SysLoginInfo;
import me.supernova.dashboard.model.event.LoginInfoEvent;
import me.supernova.dashboard.model.vo.SysLoginInfoVo;

import java.util.List;

/**
 * 系统访问日志情况信息 服务层
 *
 * <AUTHOR> Li
 */
public interface SysLoginInfoService extends IService<SysLoginInfo> {


    void recordLoginInfo(LoginInfoEvent loginInfoEvent);

    TableDataInfo<SysLoginInfoVo> selectPageloginInfoList(SysLoginInfoBo loginInfo, PageQuery pageQuery);

    /**
     * 新增系统登录日志
     *
     * @param bo 访问日志对象
     */
    void insertloginInfo(SysLoginInfoBo bo);

    /**
     * 查询系统登录日志集合
     *
     * @param loginInfo 访问日志对象
     * @return 登录记录集合
     */
    List<SysLoginInfoVo> selectLoginInfoList(SysLoginInfoBo loginInfo);

    /**
     * 批量删除系统登录日志
     *
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    int deleteLoginInfoByIds(Long[] infoIds);

    /**
     * 清空系统登录日志
     */
    void cleanLoginInfo();
}
