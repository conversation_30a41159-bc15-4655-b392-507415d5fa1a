package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.dashboard.model.dto.RankDto;
import me.supernova.dashboard.model.entity.UserRank;
import me.supernova.dashboard.model.vo.RankVo;

import java.util.List;

/**
 * <p>
 * 用户排名表，存储用户分数及更新时间 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
public interface UserRankService extends IService<UserRank> {

    /**
     * 更新用户分数
     *
     * @param userId 用户ID
     * @param score  分数
     */
    void updateScore(Long userId, RankDto score);

    Integer getRank(Long userId);

    UserRank getRecordByUserId(Long uid);

    List<RankVo> getTopNUserRankVos(int n, int minScore);

    /**
     * 获取TopN用户的排名信息，直接返回 RankVo 列表
     *
     * @param n        获取数量
     * @param minScore 最低分数
     * @return List<RankVo>
     */
    List<RankVo> getTopNRankVos(int n, int minScore);
}
