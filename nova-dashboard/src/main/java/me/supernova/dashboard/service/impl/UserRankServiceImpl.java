package me.supernova.dashboard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import me.supernova.dashboard.mapper.UserRankMapper;
import me.supernova.dashboard.model.dto.RankDto;
import me.supernova.dashboard.model.entity.UserRank;
import me.supernova.dashboard.model.vo.RankVo;
import me.supernova.dashboard.service.UserRankService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户排名表，存储用户分数及更新时间 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
@RequiredArgsConstructor
public class UserRankServiceImpl extends ServiceImpl<UserRankMapper, UserRank> implements UserRankService {

    private final UserRankMapper userRankMapper;

    @Override
    @Transactional
    public void updateScore(Long userId, RankDto dto) {
        // 使用upsert操作更新分数
        UserRank userRank = new UserRank();
        userRank.setUserId(userId);
        userRank.setScore(dto.getScore());
        userRank.setAvatar(dto.getAvatar());
        userRank.setNickname(dto.getNickname());
        userRank.setCountryCode(dto.getCountryCode());

        LambdaQueryWrapper<UserRank> wrapper = Wrappers.<UserRank>lambdaQuery()
                .eq(UserRank::getUserId, userId);

        if (this.count(wrapper) > 0) {
            this.update(userRank, wrapper);
        } else {
            this.save(userRank);
        }

    }

    @Override
    public Integer getRank(Long userId) {
        return userRankMapper.selectRankByUserId(userId);
    }

    @Override
    public UserRank getRecordByUserId(Long userId) {
        return userRankMapper.selectOne(Wrappers.<UserRank>lambdaQuery().eq(UserRank::getUserId, userId));
    }

    @Override
    public List<RankVo> getTopNUserRankVos(int n, int minScore) {
        return userRankMapper.selectTopNUserRankVos(n, minScore);
    }

    @Override
    public List<RankVo> getTopNRankVos(int n, int minScore) {
        return userRankMapper.selectTopNRankVos(n, minScore);
    }

}
