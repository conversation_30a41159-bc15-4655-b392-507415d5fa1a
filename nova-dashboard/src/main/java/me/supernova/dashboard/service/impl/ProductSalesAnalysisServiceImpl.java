package me.supernova.dashboard.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.dashboard.mapper.ProductSalesAnalysisMapper;
import me.supernova.dashboard.model.bo.ProductSalesAnalysisBo;
import me.supernova.dashboard.model.dto.ProductBasicSalesDTO;
import me.supernova.dashboard.model.dto.ProductNewUserSalesDTO;
import me.supernova.dashboard.model.dto.TimeConfig;
import me.supernova.dashboard.model.vo.ProductSalesAnalysisVo;
import me.supernova.dashboard.service.ProductSalesAnalysisService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品销售数据分析服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductSalesAnalysisServiceImpl implements ProductSalesAnalysisService {

    private final ProductSalesAnalysisMapper productSalesAnalysisMapper;

    @Override
    public List<ProductSalesAnalysisVo> queryProductSalesAnalysis(ProductSalesAnalysisBo qryBo) {
        // 1. 参数校验和默认值设置
        if (qryBo.getStartDate().isAfter(qryBo.getEndDate())) {
            throw new RuntimeException("查询开始日期不能大于结束日期");
        }

        // 2. 构建时间配置
        TimeConfig timeConfig = buildTimeConfig(qryBo);

        // 3. 查询商品基础销售数据
        List<ProductBasicSalesDTO> basicSalesList = productSalesAnalysisMapper.selectProductBasicSales(timeConfig, qryBo);

        // 4. 如果没有数据，直接返回空结果
        if (basicSalesList.isEmpty()) {
            return List.of();
        }

        // 5. 获取商品ID列表
        List<String> productIds = basicSalesList.stream()
                .map(ProductBasicSalesDTO::getProductId)
                .collect(Collectors.toList());

        // 6. 查询新用户销售数据
        List<ProductNewUserSalesDTO> newUserSalesList = productSalesAnalysisMapper.selectProductNewUserSales(productIds, timeConfig, qryBo);
        Map<String, ProductNewUserSalesDTO> newUserSalesMap = newUserSalesList.stream()
                .collect(Collectors.toMap(ProductNewUserSalesDTO::getProductId, dto -> dto));

        // 7. 查询总销售额
        BigDecimal totalSalesAmount = productSalesAnalysisMapper.selectTotalSalesAmount(timeConfig, qryBo);

        // 8. 组合数据并构建最终结果
        return basicSalesList.stream()
                .map(basicSales -> buildProductSalesAnalysisVo(basicSales, newUserSalesMap, totalSalesAmount))
                .collect(Collectors.toList());
    }

    /**
     * 构建商品销售分析VO对象
     */
    private ProductSalesAnalysisVo buildProductSalesAnalysisVo(ProductBasicSalesDTO basicSales,
                                                               Map<String, ProductNewUserSalesDTO> newUserSalesMap,
                                                               BigDecimal totalSalesAmount) {
        ProductNewUserSalesDTO newUserSales = newUserSalesMap.get(basicSales.getProductId());

        // 计算销量占比
        BigDecimal salesRatio = BigDecimal.ZERO;
        if (totalSalesAmount != null && totalSalesAmount.compareTo(BigDecimal.ZERO) > 0) {
            salesRatio = basicSales.getTotalAmount()
                    .divide(totalSalesAmount, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP);
        }

        return ProductSalesAnalysisVo.builder()
                .productName(basicSales.getProductId()) // 商品名称使用商品ID
                .productId(basicSales.getProductId())
                .sku(basicSales.getProductId()) // SKU使用商品ID
                .unitPrice(basicSales.getUnitPrice())
                .buyerCount(basicSales.getBuyerCount())
                .purchaseCount(basicSales.getPurchaseCount())
                .newBuyerCount(newUserSales != null ? newUserSales.getNewBuyerCount() : 0)
                .newUserPurchaseCount(newUserSales != null ? newUserSales.getNewUserPurchaseCount() : 0)
                .totalAmount(basicSales.getTotalAmount())
                .salesRatio(salesRatio)
                .build();
    }

    /**
     * 构建时间配置
     */
    private TimeConfig buildTimeConfig(ProductSalesAnalysisBo qryBo) {
        LocalDateTime startTime = qryBo.getStartDate().atStartOfDay();
        LocalDateTime endTime = qryBo.getEndDate().atTime(LocalTime.MAX);
        int timezone = qryBo.getTimezone() != null ? qryBo.getTimezone() : 8;
        String timeColumn = timezone == 8 ? "create_time" : "create_time"; // 订单表使用create_time
        return new TimeConfig(startTime, endTime, timeColumn);
    }
}
