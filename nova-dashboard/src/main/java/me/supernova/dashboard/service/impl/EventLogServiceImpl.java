package me.supernova.dashboard.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.dashboard.mapper.EventLogMapper;
import me.supernova.dashboard.model.entity.EventLog;
import me.supernova.dashboard.model.vo.EventLogVo;
import me.supernova.dashboard.service.EventLogService;
import me.supernova.dashboard.service.UserInfoService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 游戏事件服务实现类
 * 负责处理游戏事件的保存和处理逻辑
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventLogServiceImpl extends ServiceImpl<EventLogMapper, EventLog> implements EventLogService {

    private final UserInfoService userInfoService;

    @Override
    public void saveEvent(EventLogVo vo) {
        // 1. 构建事件实体
        EventLog eventLog = buildBaseEventLog(vo);

        // 2. 根据事件类型处理参数
        processEventParams(eventLog, vo);

        // 3. 保存事件
        save(eventLog);
        log.debug("事件保存成功:用户:{} 类型:{}", eventLog.getUserId(), eventLog.getEventType());
    }

    @Override
    public void saveServerEvent(EventLogVo vo) {
        EventLog entity = buildBaseEventLog(vo);
        processServerEvent(entity);
        save(entity);
    }

    @Override
    public List<EventLog> listByGreaterId(Long id, int limit) {
        // 1. 构建查询条件
        LambdaQueryWrapper<EventLog> wrapper = new LambdaQueryWrapper<>();

        // 2. ID大于指定值的条件
        wrapper.gt(EventLog::getId, id);

        // 3. 按ID升序排序
        wrapper.orderByAsc(EventLog::getId);

        // 4. 限制查询数量
        wrapper.last("LIMIT " + limit);

        // 5. 执行查询并返回结果
        return list(wrapper);
    }

    /**
     * 构建事件日志实体
     */
    private EventLog buildBaseEventLog(EventLogVo vo) {
        EventLog eventLog = new EventLog();
        // 1. 复制基础信息
        if (vo.getClientInfo() != null) {
            BeanUtil.copyProperties(vo.getClientInfo(), eventLog);
        }
        BeanUtil.copyProperties(vo, eventLog);

        // 2. 设置用户ID
        setUserId(vo, eventLog);

        // 3. 设置事件时间
        LocalDateTime now = LocalDateTime.now();
        eventLog.setEventTime(now);
        eventLog.setEventTimeUtc(now.minusHours(8));

        return eventLog;
    }

    /**
     * 设置用户ID
     */
    private void setUserId(EventLogVo vo, EventLog eventLog) {
        if (vo.getUserId() != null) {
            eventLog.setUserId(vo.getUserId());
            return;
        }

        if (vo.getClientInfo() != null) {
            if (vo.getClientInfo().getUserId() != null) {
                eventLog.setUserId(vo.getClientInfo().getUserId());
            } else {
                eventLog.setUserId(userInfoService.getUserId(vo.getClientInfo()));
            }
        }
    }

    /**
     * 处理不同类型事件的参数
     */
    private void processEventParams(EventLog eventLog, EventLogVo vo) {
        switch (eventLog.getEventType()) {
            case GENERAL_EVENT -> processGeneralEvent(eventLog, vo);
            case AD_EVENT -> processAdEvent(eventLog);
            //看enum 内部的注释
            case PURCHASE_EVENT -> processPurchaseEvent(eventLog);
            case UNIQUE_EVENT -> processUniqueEvent(eventLog);
            default -> {
            } // 其他类型事件暂不需要特殊处理
        }
    }

    private void processServerEvent(EventLog eventLog) {
        Dict eventParams = eventLog.getEventParams();
        if (eventParams == null) {
            return;
        }

        Map.Entry<String, Object> param1 = eventParams.pollFirstEntry();
        if (param1 != null) {
            eventLog.setParam1(param1.getValue().toString());
        }
        Map.Entry<String, Object> param2 = eventParams.pollFirstEntry();
        if (param2 != null) {
            eventLog.setParam2(param2.getValue().toString());
        }
        Map.Entry<String, Object> param3 = eventParams.pollFirstEntry();
        if (param3 != null) {
            eventLog.setParam3(param3.getValue().toString());
        }
        Map.Entry<String, Object> param4 = eventParams.pollFirstEntry();
        if (param4 != null) {
            eventLog.setParam4(param4.getValue().toString());
        }
        Map.Entry<String, Object> param5 = eventParams.pollFirstEntry();
        if (param5 != null) {
            eventLog.setParam5(param5.getValue().toString());
        }
        Map.Entry<String, Object> param6 = eventParams.pollFirstEntry();
        if (param6 != null) {
            eventLog.setParam6(param6.getValue().toString());
        }
        Map.Entry<String, Object> param7 = eventParams.pollFirstEntry();
        if (param7 != null) {
            eventLog.setParam7(param7.getValue().toString());
        }
        Map.Entry<String, Object> param8 = eventParams.pollFirstEntry();
        if (param8 != null) {
            eventLog.setParam8(param8.getValue().toString());
        }
        Map.Entry<String, Object> param9 = eventParams.pollFirstEntry();
        if (param9 != null) {
            eventLog.setParam9(param9.getValue().toString());
        }

    }

    /**
     * 处理通用事件参数
     */
    private void processGeneralEvent(EventLog eventLog, EventLogVo vo) {
        String eventName = vo.getEventName();
        if (StrUtil.isBlank(eventName)) {
            return;
        }

        String[] split = eventName.split(":");
        // 直接设置参数，避免使用反射
        int paramCount = Math.min(split.length, 10);
        for (int i = 0; i < paramCount; i++) {
            if (split[i] != null) {
                setEventParam(eventLog, i, split[i]);
            }
        }
    }

    /**
     * 处理广告事件参数
     */
    private void processAdEvent(EventLog eventLog) {
        Dict eventParams = eventLog.getEventParams();
        if (eventParams == null) {
            return;
        }

        eventLog.setParam1(eventParams.getStr("adSdkName"));
        eventLog.setParam2(eventParams.getStr("adPlacement"));
        eventLog.setParam3(eventParams.getStr("adType"));
        eventLog.setParam4(eventParams.getStr("adAction"));
        eventLog.setParam5(eventParams.getStr("adFailShowReason"));
        eventLog.setParam6(eventParams.getStr("adDuration"));
        String eventValue = eventParams.getStr("EventValue");
        eventLog.setParam7(eventValue);
        eventLog.setEventValue(new BigDecimal(eventValue));
    }

    /**
     * 处理购买事件参数
     */
    private void processPurchaseEvent(EventLog eventLog) {
        Dict eventParams = eventLog.getEventParams();
        if (eventParams == null) {
            return;
        }

        String clientTransactionId = eventParams.getStr("clientTransactionId");
        if (StrUtil.isBlank(clientTransactionId)) {
            log.error("clientTransactionId 是空的!, eventLog: {}", eventLog);
            throw new IllegalArgumentException("clientTransactionId 是空的!");
        }

        eventLog.setParam1(eventParams.getStr("cartType"));
        eventLog.setParam2(eventParams.getStr("itemType"));
        eventLog.setParam3(eventParams.getStr("itemId"));
        eventLog.setParam4(eventParams.getStr("amount"));
        eventLog.setParam5(eventParams.getStr("currency"));
        eventLog.setParam6(eventParams.getStr("state"));
        eventLog.setParam7(clientTransactionId);
    }

    /**
     * 设置事件参数
     */
    private void setEventParam(EventLog eventLog, int index, String value) {
        switch (index) {
            case 0 -> eventLog.setParam1(value);
            case 1 -> eventLog.setParam2(value);
            case 2 -> eventLog.setParam3(value);
            case 3 -> eventLog.setParam4(value);
            case 4 -> eventLog.setParam5(value);
            case 5 -> eventLog.setParam6(value);
            case 6 -> eventLog.setParam7(value);
            case 7 -> eventLog.setParam8(value);
            case 8 -> eventLog.setParam9(value);
            default -> log.warn("参数索引超出范围: {}", index);
        }
    }

    private void processUniqueEvent(EventLog eventLog) {
        remove(Wrappers.lambdaQuery(EventLog.class)
                .eq(EventLog::getEventType, eventLog.getEventType())
                .eq(EventLog::getEventName, eventLog.getEventName())
                .eq(EventLog::getUserId, eventLog.getUserId())
        );
    }
}
