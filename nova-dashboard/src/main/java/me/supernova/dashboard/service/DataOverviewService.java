package me.supernova.dashboard.service;

import me.supernova.dashboard.model.bo.DataOverviewBo;
import me.supernova.dashboard.model.vo.DataOverviewVo;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * 数据概览服务接口
 */
public interface DataOverviewService {

    /**
     * 查询数据概览信息
     *
     * @param request 查询请求参数
     * @return 数据概览信息列表
     */
    List<DataOverviewVo> queryDataOverview(DataOverviewBo request);

    /**
     * 流式查询数据概览信息
     *
     * @param request 查询请求参数
     * @param emitter SSE发射器
     */
    void queryDataOverviewStream(DataOverviewBo request, SseEmitter emitter);
}