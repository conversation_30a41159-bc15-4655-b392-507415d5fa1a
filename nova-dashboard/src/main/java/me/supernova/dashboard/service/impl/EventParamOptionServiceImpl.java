package me.supernova.dashboard.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.supernova.dashboard.mapper.EventParamOptionMapper;
import me.supernova.dashboard.model.entity.EventParamOption;
import me.supernova.dashboard.service.EventParamOptionService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 事件参数选项表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class EventParamOptionServiceImpl extends ServiceImpl<EventParamOptionMapper, EventParamOption> implements EventParamOptionService {

    @Override
    public List<String> getOptions(String eventKey) {
        if (StrUtil.isNotBlank(eventKey)) {
            return list(Wrappers.lambdaQuery(EventParamOption.class)
                    .eq(EventParamOption::getKey, eventKey))
                    .stream()
                    .map(EventParamOption::getValue)
                    .toList();
        }
        return List.of();
    }

    /**
     * 批量保存并忽略唯一键冲突
     *
     * @param entityList 实体列表
     * @param batchSize  批次大小
     */
    public void saveBatchIgnoreDuplicate(List<EventParamOption> entityList, int batchSize) {
        if (entityList == null || entityList.isEmpty()) {
            return;
        }

        // 过滤掉value为null的记录
        List<EventParamOption> validList = entityList.stream()
                .filter(item -> item != null && item.getValue() != null)
                .collect(Collectors.toList());

        if (!validList.isEmpty()) {
            // 使用SQL的INSERT IGNORE语法
            baseMapper.insertBatchIgnore(validList, batchSize);
        }
    }
}
