package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.dashboard.model.bo.DeviceIdentifierQueryBO;
import me.supernova.dashboard.model.bo.UserInfoQueryBO;
import me.supernova.dashboard.model.entity.UserInfo;
import me.supernova.dashboard.model.vo.ClientInfoVo;
import me.supernova.dashboard.model.vo.UserInfoListVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 设备信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public interface UserInfoService extends IService<UserInfo> {

    /**
     * 更新或创建设备信息
     *
     * @param dto 设备信息
     * @return 更新后的设备信息
     */
    Optional<UserInfo> updateOrCreateDeviceInfoOpt(ClientInfoVo dto);

    @Transactional(rollbackFor = Exception.class)
    UserInfo updateOrCreateDeviceInfo(ClientInfoVo dto);

    @Transactional(rollbackFor = Exception.class)
    Long getUserId(ClientInfoVo dto);

    /**
     * 分页查询用户列表
     *
     * @param query     查询条件
     * @param pageQuery 分页参数
     * @return 用户列表
     */
    Page<UserInfo> selectPageUserList(UserInfoQueryBO query, PageQuery pageQuery);

    /**
     * 根据设备标识符查询用户信息
     *
     * @param query 设备标识符查询条件
     * @return 用户信息列表
     */
    List<UserInfoListVO> queryUsersByDeviceIdentifiers(DeviceIdentifierQueryBO query);

    /**
     * 修改用户去广告状态
     *
     * @param userId 用户ID
     * @param noAds  去广告状态
     * @return 是否修改成功
     */
    boolean updateUserNoAdsStatus(Long userId, Boolean noAds);

}
