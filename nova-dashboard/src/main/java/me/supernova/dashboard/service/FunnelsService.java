package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.dashboard.model.bo.FunnelAnalysisBO;
import me.supernova.dashboard.model.dto.UserEventSequenceDTO;
import me.supernova.dashboard.model.entity.Funnels;

import java.util.List;

/**
 * <p>
 * 漏斗表，记录各类漏斗信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface FunnelsService extends IService<Funnels> {
    /**
     * 分析漏斗事件（使用FunnelAnalysisBO对象）
     *
     * @param analysisBO 分析参数
     * @return 分析结果
     */
    List<UserEventSequenceDTO> analyzeFunnelEvents(FunnelAnalysisBO analysisBO);
}