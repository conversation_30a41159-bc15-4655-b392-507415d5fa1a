package me.supernova.dashboard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.dashboard.mapper.RobotRankRecordMapper;
import me.supernova.dashboard.model.entity.RobotRankRecord;
import me.supernova.dashboard.service.RobotRankRecordService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 积分记录表，存储用户积分及排名信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Service
public class RobotRankRecordServiceImpl extends ServiceImpl<RobotRankRecordMapper, RobotRankRecord>
        implements RobotRankRecordService {

    private static final String SEQUENCE_KEY = "robot:rank:sequence";
    private static final int SEQUENCE_TTL_DAYS = 60;
    private static final int SEQUENCE_ROTATE_DAYS = 30;

    private int getSequenceFromRedis() {
        Integer sequence = RedisUtils.getCacheObject(SEQUENCE_KEY);
        if (sequence == null) {
            return 0;
        }

        //每 30 天轮换一次
        long timeToLive = RedisUtils.getTimeToLive(SEQUENCE_KEY);
        if (Duration.ofSeconds(timeToLive).toDays() < SEQUENCE_ROTATE_DAYS) {
            RedisUtils.setCacheObject(SEQUENCE_KEY, sequence + 1, Duration.ofDays(SEQUENCE_TTL_DAYS));
        }
        return sequence;
    }

    @Override
    public List<RobotRankRecord> getRecordList(Integer hour) {
        int sequence = getSequenceFromRedis();
        List<Integer> integers = baseMapper.selectAllSequences();
        if (integers.isEmpty()) {
            return Collections.emptyList();
        }

        if (!integers.contains(sequence)) {
            sequence = integers.getFirst();
            RedisUtils.setCacheObject(SEQUENCE_KEY, sequence, Duration.ofDays(SEQUENCE_TTL_DAYS));
        }

        return list(new LambdaQueryWrapper<RobotRankRecord>()
                .eq(RobotRankRecord::getIsRunning, true)
                .eq(RobotRankRecord::getSequence, sequence)
                .eq(hour != null, RobotRankRecord::getTimeUpdate, hour)
                .orderByDesc(RobotRankRecord::getScore)
                .orderByDesc(RobotRankRecord::getTimeUpdate));
    }

}
