package me.supernova.dashboard.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import me.supernova.dashboard.model.entity.Orders;
import me.supernova.dashboard.model.enums.EventTypeEnum;
import me.supernova.dashboard.model.event.LoginRegisterEvent;
import me.supernova.dashboard.model.event.PaidEvent;
import me.supernova.dashboard.model.vo.EventLogVo;
import me.supernova.dashboard.service.EventLogService;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UserEventListener {

    private final EventLogService eventLogService;

    @EventListener(LoginRegisterEvent.class)
    public void handleUserEvent(LoginRegisterEvent event) {
        EventLogVo eventLogVo = new EventLogVo();
        eventLogVo.setUserId(event.userId());
        eventLogVo.setClientInfo(event.clientInfo());
        eventLogVo.setEventId(IdUtil.fastSimpleUUID());
        eventLogVo.setEventType(EventTypeEnum.SERVER_EVENT);
        eventLogVo.setEventName(event.eventName());

        eventLogService.saveServerEvent(eventLogVo);
    }

    @EventListener(PaidEvent.class)
    public void handlePaidEvent(PaidEvent event) {
        Orders order = event.order();
        EventLogVo eventLogVo = new EventLogVo();
        eventLogVo.setUserId(order.getUserId());
        eventLogVo.setEventId(IdUtil.fastSimpleUUID());
        eventLogVo.setClientInfo(event.clientInfoVo());
        eventLogVo.setEventType(EventTypeEnum.SERVER_EVENT);
        eventLogVo.setEventName("paid");
        eventLogVo.setEventValue(order.getOrderAmount());
        eventLogVo.setEventParams(Dict.of("orderId", order.getOrderNo()));
        eventLogService.saveServerEvent(eventLogVo);
    }
} 