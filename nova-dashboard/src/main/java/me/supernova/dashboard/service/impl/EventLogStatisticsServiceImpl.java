package me.supernova.dashboard.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.dashboard.mapper.EventLogMapper;
import me.supernova.dashboard.model.bo.DataOverviewBo;
import me.supernova.dashboard.model.dto.AdShowMetricsDTO;
import me.supernova.dashboard.model.dto.PaymentMetricsDTO;
import me.supernova.dashboard.model.dto.TimeConfig;
import me.supernova.dashboard.model.dto.UserMetricsDTO;
import me.supernova.dashboard.service.EventLogStatisticsService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 事件日志统计服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventLogStatisticsServiceImpl implements EventLogStatisticsService {

    private final EventLogMapper eventLogMapper;

    /**
     * 获取查询的时间范围和时区配置
     *
     * @param date 查询日期
     * @param qryBo 查询参数
     * @return 时间配置信息
     */
    private TimeConfig getTimeRangeAndColumn(LocalDate date, DataOverviewBo qryBo) {
        LocalDateTime startTime = date.atStartOfDay();
        LocalDateTime endTime = date.atTime(LocalTime.MAX);
        int timezone = qryBo.getTimezone() != null ? qryBo.getTimezone() : 8;
        String timeColumn = timezone == 8 ? "event_time" : "event_time_utc";
        return new TimeConfig(startTime, endTime, timeColumn);
    }

    @Override
    public BigDecimal countTotalAppTime(LocalDate date, DataOverviewBo qryBo) {
        TimeConfig timeConfig = getTimeRangeAndColumn(date, qryBo);
        return eventLogMapper.countTotalAppTime(timeConfig, qryBo);
    }

    @Override
    public BigDecimal sumIapRevenue(LocalDate date, DataOverviewBo qryBo) {
        TimeConfig timeConfig = getTimeRangeAndColumn(date, qryBo);
        return eventLogMapper.sumIapRevenue(timeConfig, qryBo);
    }

    @Override
    public BigDecimal sumAdRevenue(LocalDate date, DataOverviewBo qryBo) {
        TimeConfig timeConfig = getTimeRangeAndColumn(date, qryBo);
        return eventLogMapper.sumAdRevenue(timeConfig, qryBo);
    }

    @Override
    public int countNewPayingUsers(LocalDate date, DataOverviewBo qryBo) {
        TimeConfig timeConfig = getTimeRangeAndColumn(date, qryBo);
        return eventLogMapper.countNewPayingUsers(timeConfig, qryBo);
    }

    @Override
    public int countPayingUsers(LocalDate date, DataOverviewBo qryBo) {
        TimeConfig timeConfig = getTimeRangeAndColumn(date, qryBo);
        return eventLogMapper.countPayingUsers(timeConfig, qryBo);
    }

    @Override
    public PaymentMetricsDTO calculatePaymentDto(LocalDate date, DataOverviewBo qryBo) {
        TimeConfig timeConfig = getTimeRangeAndColumn(date, qryBo);
        return eventLogMapper.getPaymentMetrics(timeConfig, qryBo);
    }

    @Override
    public AdShowMetricsDTO countAdShowMetricsDTO(LocalDate date, DataOverviewBo qryBo) {
        TimeConfig timeConfig = getTimeRangeAndColumn(date, qryBo);
        return eventLogMapper.getAdShowMetrics(timeConfig, qryBo);
    }

    @Override
    public UserMetricsDTO getUserMetrics(LocalDate date, DataOverviewBo qryBo) {
        TimeConfig timeConfig = getTimeRangeAndColumn(date, qryBo);
        return eventLogMapper.getUserMetrics(timeConfig, qryBo);
    }
}
