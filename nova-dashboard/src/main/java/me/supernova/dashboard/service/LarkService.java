package me.supernova.dashboard.service;

import me.supernova.dashboard.model.vo.LarkUserInfoVo;
import org.springframework.scheduling.annotation.Async;

/**
 * 飞书服务接口
 */
public interface LarkService {

    /**
     * 获取飞书用户访问令牌
     *
     * @param code        授权码
     * @param redirectUri 重定向URI
     * @return 用户访问令牌
     */
    String getUserAccessToken(String code, String redirectUri);

    /**
     * 获取飞书用户信息
     *
     * @param userAccessToken 用户访问令牌
     * @return 飞书用户信息
     */
    LarkUserInfoVo getLarkUserInfo(String userAccessToken);

    /**
     * 异步同步飞书用户信息到系统用户
     *
     * @param larkOpenId openId
     */
    @Async
    void syncUserInfo(String larkOpenId);
} 