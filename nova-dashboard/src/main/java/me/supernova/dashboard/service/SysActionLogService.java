package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.dashboard.model.bo.SysActionLogBo;
import me.supernova.dashboard.model.entity.SysActionLog;
import me.supernova.dashboard.model.vo.SysActionLogVo;

import java.util.List;

/**
 * 系统日志 服务接口
 *
 * <AUTHOR>
 */
public interface SysActionLogService extends IService<SysActionLog> {

    /**
     * 查询系统日志列表
     */
    TableDataInfo<SysActionLogVo> selectPageActionLogList(SysActionLogBo bo, PageQuery pageQuery);

    /**
     * 查询系统日志列表
     */
    List<SysActionLogVo> selectActionLogList(SysActionLogBo bo);

    /**
     * 查询系统日志详细信息
     */
    SysActionLogVo selectActionLogById(Long id);
}
