package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.dashboard.model.entity.LevelConfig;
import me.supernova.dashboard.model.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 关卡信息表，存储游戏中各个关卡的详细信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface LevelConfigService extends IService<LevelConfig> {

    /**
     * 获取游戏配置（客户端接口）
     *
     * @param vo 基础请求参数
     * @return 游戏配置
     */
    GameConfigV2 getConfig(BaseVo vo);

    /**
     * 获取游戏配置（后台管理系统接口）
     *
     * @param abGroup AB测试组，如果为null则获取默认组
     * @return 游戏配置
     */
    AdminGameConfigV2 getAdminConfig(String abGroup);

    /**
     * 更新游戏配置（后台管理系统接口）
     *
     * @param config 游戏配置
     * @return 是否更新成功
     */
    boolean updateAdminConfig(AdminGameConfigV2 config);



    /**
     * 上传游戏配置XLSX文件到Redis（控制台端接口）
     *
     * @param file    XLSX文件
     * @param version 版本号
     * @return 上传结果消息，成功返回null，失败返回错误信息
     */
    String uploadGameConfigXlsx(MultipartFile file, String version);

    /**
     * 获取游戏配置XLSX数据（控制台端接口）
     *
     * @param version 版本号
     * @return 配置数据JSON字符串，如果版本不存在返回null
     */
    ExcelGameConfig getGameConfigXlsxData(String version);

    /**
     * 获取游戏配置XLSX内容（Worker端接口）
     *
     * @param vo 基础请求参数，包含clientInfo
     * @return XLSX文件内容，如果版本不存在返回null
     */
    ExcelGameConfig getGameConfigXlsxForWorker(BaseVo vo);

    /**
     * 获取所有游戏配置版本列表
     *
     * @return 版本信息列表，按上传时间倒序排列
     */
    List<VersionInfo> getAllVersions();

    /**
     * 删除指定版本的游戏配置
     *
     * @param version 版本号
     * @return 删除结果消息，成功返回null，失败返回错误信息
     */
    String deleteGameConfigVersion(String version);
}
