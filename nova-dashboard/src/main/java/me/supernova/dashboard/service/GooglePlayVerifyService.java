package me.supernova.dashboard.service;

import cn.hutool.core.util.StrUtil;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.utils.EnvUtil;
import me.supernova.dashboard.mapper.OrdersMapper;
import me.supernova.dashboard.model.bo.GooglePlayReceiptBo;
import me.supernova.dashboard.model.entity.Orders;
import me.supernova.dashboard.model.entity.UserInfo;
import me.supernova.dashboard.model.enums.OrderStatusEnum;
import me.supernova.dashboard.model.enums.PaymentEnvironment;
import me.supernova.dashboard.model.event.PaidEvent;
import me.supernova.dashboard.model.vo.ClientInfoVo;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Google Play 验证服务
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GooglePlayVerifyService {

    private final OrdersMapper ordersMapper;
    private final UserInfoService userInfoService;
    private final ApplicationEventPublisher eventPublisher;

    // 常量定义
    private static final Map<String, AndroidPublisher> PUBLISHER_MAP = new ConcurrentHashMap<>();
    private static final Map<String, String> ACCOUNT_MAP_LIVE = new HashMap<>() {{
        put("com.ASMR.puzzle.minigames.satis",
                // language=json
                """
                        ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                        """);
        put("thisfun.happysorthome",
                // language=json
                """
                        ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                        
                        """);
    }};
    private static final Map<String, String> ACCOUNT_MAP_SANDBOX = new HashMap<>() {{
        put("com.ASMR.puzzle.minigames.satis",
                // language=json
                """
                        ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                        """);

        put("thisfun.happysorthome",
                // language=json
                """
                        ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                        
                        """);
    }};
    private static final int MAX_RETRIES = 3;
    private static final long RETRY_DELAY_MS = 1000;


    /**
     * 获取或创建 AndroidPublisher 实例
     */
    private synchronized AndroidPublisher getAndroidPublisher(String packageName) {
        String serviceAccountJson = EnvUtil.isProd() ? ACCOUNT_MAP_LIVE.get(packageName) : ACCOUNT_MAP_SANDBOX.get(packageName);
        if (serviceAccountJson == null) {
            throw new IllegalArgumentException("未找到包名对应的服务账号配置：" + packageName);
        }

        return PUBLISHER_MAP.computeIfAbsent(packageName, key -> {
            try {
                HttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
                JsonFactory jsonFactory = GsonFactory.getDefaultInstance();
                GoogleCredentials credentials = GoogleCredentials
                        .fromStream(new ByteArrayInputStream(serviceAccountJson.getBytes()))
                        .createScoped(Collections.singleton(AndroidPublisherScopes.ANDROIDPUBLISHER));
                return new AndroidPublisher.Builder(httpTransport, jsonFactory, new HttpCredentialsAdapter(credentials))
                        .setApplicationName(packageName)
                        .build();
            } catch (Exception e) {
                log.error("创建 AndroidPublisher 实例失败：packageName={}", packageName, e);
                throw new RuntimeException("创建 AndroidPublisher 实例失败", e);
            }
        });
    }


    /**
     * 从 Google Play API 获取购买信息
     *
     * @param receipt     购买令牌
     * @param productId   产品ID
     * @param packageName 应用包名
     * @return 购买信息
     * @throws Exception API调用异常
     */
    private ProductPurchase fetchPurchaseFromGooglePlay(GooglePlayReceiptBo receipt, String productId, String packageName) throws Exception {
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount < MAX_RETRIES) {
            try {
                return getAndroidPublisher(packageName).purchases().products()
                        .get(packageName, productId, receipt.getPurchaseToken())
                        .execute();
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                if (retryCount < MAX_RETRIES) {
                    log.warn("Google Play API 调用失败，准备重试 {}/{}", retryCount, MAX_RETRIES);
                    Thread.sleep(RETRY_DELAY_MS);
                }
            }
        }

        log.error("Google Play API 调用失败，已重试{}次", MAX_RETRIES, lastException);
        throw lastException;
    }

    /**
     * 验证并处理 Google Play 购买订单
     * 包括：验证购买信息、更新订单状态、更新用户权限
     *
     * @param receipt      购买令牌
     * @param productId    产品ID
     * @param clientInfoVo 客户端信息
     * @param order        订单信息
     * @return 校验是否成功
     */
    public boolean processGooglePlayPurchase(GooglePlayReceiptBo receipt, String productId, ClientInfoVo clientInfoVo, Orders order) {

        String packageName = clientInfoVo.getPackageName();
        try {
            // 验证谷歌内购并获取购买信息
            ProductPurchase purchase = validateGooglePlayPurchase(receipt, productId, packageName);
            if (purchase == null) {
                markOrderAsFailed(order);
                return false;
            }

            // 确认购买
            acknowledgePurchase(receipt, productId, packageName);

            // 更新订单状态为已支付
            updateOrderAsPaid(order, purchase);

            // 发布用户登录事件
            eventPublisher.publishEvent(new PaidEvent(order, clientInfoVo));

            // 更新用户权限和相关付款数据
            updateUserInfo(order);

            return true;
        } catch (Exception e) {
            log.error("订单校验异常", e);
            markOrderAsFailed(order);
            return false;
        }
    }

    /**
     * 验证 Google Play 购买信息的有效性
     * 包括：购买状态、消费状态、订单ID、购买时间等验证
     *
     * @param receipt     购买令牌
     * @param productId   产品ID
     * @param packageName 应用包名
     * @return 验证通过的购买信息，验证失败返回null
     */
    private ProductPurchase validateGooglePlayPurchase(GooglePlayReceiptBo receipt, String productId, String packageName) {
        try {

            if (!receipt.getPackageName().equals(packageName)) {
                log.error("谷歌内购验证失败：包名不匹配，expected={}, actual={}", packageName, receipt.getPackageName());
                return null;
            }

            if (!receipt.getProductId().equals(productId)) {
                log.error("谷歌内购验证失败：产品ID不匹配，expected={}, actual={}", productId, receipt.getProductId());
                return null;
            }

            ProductPurchase purchase = fetchPurchaseFromGooglePlay(receipt, productId, packageName);

            if (purchase == null) {
                log.error("谷歌内购验证失败：购买信息为空");
                return null;
            }

            // 验证购买状态 (0: 已购买, 1: 已取消, 2: 待处理)
            if (purchase.getPurchaseState() != 0) {
                log.error("谷歌内购验证失败：购买状态异常，当前状态={}", purchase.getPurchaseState());
                return null;
            }

            // 验证消费状态 (0: 未消费, 1: 已消费)
            if (purchase.getConsumptionState() != 0) {
                log.error("谷歌内购验证失败：商品已被消费，当前状态={}", purchase.getConsumptionState());
                return null;
            }

            // 验证订单ID
            if (StrUtil.isBlank(purchase.getOrderId())) {
                log.error("谷歌内购验证失败：订单ID为空");
                return null;
            }

            // 验证购买时间
            if (purchase.getPurchaseTimeMillis() == null || purchase.getPurchaseTimeMillis() <= 0) {
                log.error("谷歌内购验证失败：购买时间无效");
                return null;
            }
            // 2025 年 4 月 21 日 这个校验暂时先不写,因为现在的游戏都是轻量级的,为了让用户可以有比较良好的 restore 的体验,所以暂时去掉这个判断逻辑
//            // 验证确认状态 (0: 未确认, 1: 已确认)
//            if (purchase.getAcknowledgementState() != 0) {
//                log.error("谷歌内购验证失败：当前订单并非未确认状态，当前状态={}", purchase.getAcknowledgementState());
//                return null;
//            }

            // 检查是否是沙盒支付
            boolean isSandbox = purchase.getPurchaseType() != null && purchase.getPurchaseType() == 0;
            if (isSandbox) {
                log.info("检测到沙盒支付：订单ID={}, 产品ID={}", purchase.getOrderId(), purchase.getProductId());
            }

            log.info("谷歌内购验证成功：订单ID={}, 产品ID={}, 购买时间={}, 是否沙盒={}",
                    purchase.getOrderId(),
                    purchase.getProductId(),
                    purchase.getPurchaseTimeMillis(),
                    isSandbox);

            return purchase;
        } catch (Exception e) {
            log.error("谷歌内购验证异常", e);
            return null;
        }
    }

    /**
     * 将订单标记为失败
     */
    private void markOrderAsFailed(Orders order) {
        order.setOrderStatus(OrderStatusEnum.FAILED.getCode());
        order.setUpdateTime(LocalDateTime.now());
        order.setVerified(false);
        ordersMapper.updateById(order);
    }

    /**
     * 更新订单为已支付状态
     */
    private void updateOrderAsPaid(Orders order, ProductPurchase purchase) {
        order.setOrderStatus(OrderStatusEnum.PAID.getCode());
        order.setPaymentTime(LocalDateTime.now());
        order.setPaymentEnvironment(purchase.getPurchaseType() != null && purchase.getPurchaseType() == 0 ? PaymentEnvironment.SANDBOX : PaymentEnvironment.PRODUCTION);
        order.setTransactionId(purchase.getOrderId());
        order.setUpdateTime(LocalDateTime.now());
        order.setVerified(true);
        ordersMapper.updateById(order);
    }

    /**
     * 更新用户信息
     */
    private void updateUserInfo(Orders order) {
        if (order.getProductId().equals("satisbox_purchase_tips_0.99")) {
            return;
        }
        UserInfo userInfo = userInfoService.getById(order.getUserId());
        userInfo.setNoAds(true);
        userInfo.setPaymentCount(userInfo.getPaymentCount() + 1);
        userInfo.setTotalPaymentAmount(userInfo.getTotalPaymentAmount().add(order.getOrderAmount()));
        userInfo.setUpdateTime(LocalDateTime.now());
        userInfoService.updateById(userInfo);
    }

    /**
     * 确认 Google Play 购买
     * 调用 Google Play API 确认购买，防止重复消费
     *
     * @param receipt     购买令牌
     * @param productId   产品ID
     * @param packageName 应用包名
     * @throws Exception API调用异常
     */
    private void acknowledgePurchase(GooglePlayReceiptBo receipt, String productId, String packageName) throws Exception {
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount < MAX_RETRIES) {
            try {
                getAndroidPublisher(packageName).purchases().products()
                        .acknowledge(packageName, productId, receipt.getPurchaseToken(), null)
                        .execute();
                log.info("Google Play 购买确认成功：订单ID={}, 产品ID={}", receipt.getOrderId(), productId);
                return;
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                if (retryCount < MAX_RETRIES) {
                    log.warn("Google Play API 确认购买失败，准备重试 {}/{}", retryCount, MAX_RETRIES);
                    Thread.sleep(RETRY_DELAY_MS);
                }
            }
        }

        log.error("Google Play API 确认购买失败，已重试{}次", MAX_RETRIES, lastException);
        throw lastException;
    }

    /**
     * 获取商品价格信息
     *
     * @param productId   产品ID
     * @param packageName 应用包名
     * @return 商品价格信息
     * @throws Exception API调用异常
     */
    public ProductPurchase getProductPrice(String productId, String packageName) throws Exception {
        try {
            return getAndroidPublisher(packageName).purchases().products()
                    .get(packageName, productId, null)
                    .execute();
        } catch (Exception e) {
            log.error("获取商品价格信息失败：productId={}", productId, e);
            throw e;
        }
    }
} 