package me.supernova.dashboard.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.exception.ServiceException;
import me.supernova.dashboard.mapper.SysUserMapper;
import me.supernova.dashboard.model.entity.SysUser;
import me.supernova.dashboard.model.vo.LarkUserInfoVo;
import me.supernova.dashboard.service.LarkService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 飞书服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LarkServiceImpl implements LarkService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final SysUserMapper userMapper;

    @Value("${lark.app-id}")
    private String appId;

    @Value("${lark.app-secret}")
    private String appSecret;

    private static final String USER_ACCESS_TOKEN_URL = "https://open.feishu.cn/open-apis/authen/v2/oauth/token";
    private static final String USER_INFO_URL = "https://open.feishu.cn/open-apis/authen/v1/user_info";
    private static final String LARK_USER_DETAIL_URL = "https://open.feishu.cn/open-apis/contact/v3/users/";

    @Override
    public String getUserAccessToken(String code, String redirectUri) {
        if (StrUtil.isBlank(code)) {
            throw new ServiceException("授权码不能为空");
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("grant_type", "authorization_code");
        requestBody.put("code", code);
        requestBody.put("client_id", appId);
        requestBody.put("client_secret", appSecret);
        requestBody.put("redirect_uri", redirectUri);
        requestBody.put("code_verifier", IdUtil.fastSimpleUUID());

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    USER_ACCESS_TOKEN_URL,
                    HttpMethod.POST,
                    new HttpEntity<>(requestBody, headers),
                    String.class
            );

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceException("获取飞书用户访问令牌失败：" + response.getStatusCode());
            }

            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            JsonNode accessTokenNode = jsonNode.get("access_token");
            if (accessTokenNode == null) {
                throw new ServiceException("获取飞书用户访问令牌失败：令牌字段不存在");
            }
            String accessToken = accessTokenNode.asText();
            if (StrUtil.isBlank(accessToken)) {
                throw new ServiceException("获取飞书用户访问令牌失败：令牌为空");
            }

            return accessToken;
        } catch (Exception e) {
            log.error("获取飞书用户访问令牌异常", e);
            throw new ServiceException("获取飞书用户访问令牌异常: " + e.getMessage());
        }
    }

    @Override
    public LarkUserInfoVo getLarkUserInfo(String userAccessToken) {
        if (StrUtil.isBlank(userAccessToken)) {
            throw new ServiceException("用户访问令牌不能为空");
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(userAccessToken);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    USER_INFO_URL,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    String.class
            );

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceException("获取飞书用户信息失败：" + response.getStatusCode());
            }

            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            JsonNode data = jsonNode.get("data");
            if (data == null) {
                throw new ServiceException("获取飞书用户信息失败：返回数据为空");
            }

            LarkUserInfoVo larkUserInfo = new LarkUserInfoVo();
            larkUserInfo.setOpenId(data.get("open_id").asText());
            larkUserInfo.setName(data.get("name").asText());
            larkUserInfo.setEnName(data.get("en_name").asText());
            larkUserInfo.setAvatarUrl(data.get("avatar_url").asText());

            return larkUserInfo;
        } catch (Exception e) {
            log.error("获取飞书用户信息异常", e);
            throw new ServiceException("获取飞书用户信息异常: " + e.getMessage());
        }
    }

    @Async
    @Override
    public void syncUserInfo(String larkOpenId) {
        if (StrUtil.isBlank(larkOpenId)) {
            log.error("同步飞书用户信息失败：飞书用户唯一标识不能为空");
            return;
        }

        try {
            // 1. 获取应用访问令牌（App Access Token）
            String appAccessToken = getAppAccessToken();

            // 2. 调用飞书API获取用户详细信息
            LarkUserInfoVo larkUserInfo = getLarkUserDetail(larkOpenId, appAccessToken);

            // 3. 更新系统用户信息
            updateUserInfo(larkOpenId, larkUserInfo);

            log.info("同步飞书用户信息成功：openId={}", larkOpenId);
        } catch (Exception e) {
            log.error("同步飞书用户信息异常", e);
        }
    }

    private String getAppAccessToken() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("app_id", appId);
        requestBody.put("app_secret", appSecret);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal",
                    HttpMethod.POST,
                    new HttpEntity<>(requestBody, headers),
                    String.class
            );

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceException("获取应用访问令牌失败：" + response.getStatusCode());
            }

            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            int code = jsonNode.get("code").asInt();

            if (code != 0) {
                String msg = jsonNode.get("msg").asText();
                throw new ServiceException("获取应用访问令牌失败：" + msg);
            }

            String appAccessToken = jsonNode.get("app_access_token").asText();
            if (appAccessToken == null) {
                throw new ServiceException("获取应用访问令牌失败：令牌为空");
            }

            return appAccessToken;
        } catch (Exception e) {
            log.error("获取应用访问令牌异常", e);
            throw new ServiceException("获取应用访问令牌异常: " + e.getMessage());
        }
    }

    private LarkUserInfoVo getLarkUserDetail(String openId, String appAccessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + appAccessToken);

        try {
            String url = LARK_USER_DETAIL_URL + openId + "?user_id_type=open_id";

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    String.class
            );

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceException("获取飞书用户详细信息失败：" + response.getStatusCode());
            }

            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            int code = jsonNode.get("code").asInt();

            if (code != 0) {
                String msg = jsonNode.get("msg").asText();
                throw new ServiceException("获取飞书用户详细信息失败：" + msg);
            }

            JsonNode data = jsonNode.get("data").get("user");
            if (data == null) {
                throw new ServiceException("获取飞书用户详细信息失败：返回数据为空");
            }

            LarkUserInfoVo larkUserInfo = new LarkUserInfoVo();

            // 设置用户基本信息
            larkUserInfo.setOpenId(openId);
            larkUserInfo.setName(data.get("name").asText());
            larkUserInfo.setEnName(data.get("en_name").asText());

            // 设置用户头像 - 优先使用高清头像
            JsonNode avatar = data.get("avatar");
            if (avatar != null) {
                String avatarUrl = null;
                JsonNode originNode = avatar.get("avatar_origin");
                if (originNode != null) {
                    avatarUrl = originNode.asText();
                }
                if (StrUtil.isBlank(avatarUrl)) {
                    JsonNode node640 = avatar.get("avatar_640");
                    if (node640 != null) {
                        avatarUrl = node640.asText();
                    }
                }
                if (StrUtil.isBlank(avatarUrl)) {
                    JsonNode node240 = avatar.get("avatar_240");
                    if (node240 != null) {
                        avatarUrl = node240.asText();
                    }
                }
                if (StrUtil.isBlank(avatarUrl)) {
                    JsonNode node72 = avatar.get("avatar_72");
                    if (node72 != null) {
                        avatarUrl = node72.asText();
                    }
                }
                if (StrUtil.isNotBlank(avatarUrl)) {
                    larkUserInfo.setAvatarUrl(avatarUrl);
                }
            }

            // 设置额外的用户信息
            larkUserInfo.setEmail(data.get("email").asText());
            larkUserInfo.setMobile(data.get("mobile").asText());
            larkUserInfo.setGender(data.get("gender").asInt());

            // 设置用户状态
            JsonNode status = data.get("status");
            if (status != null) {
                larkUserInfo.setIsActivated(status.get("is_activated").asBoolean());
            }

            return larkUserInfo;
        } catch (Exception e) {
            log.error("获取飞书用户详细信息异常", e);
            throw new ServiceException("获取飞书用户详细信息异常: " + e.getMessage());
        }
    }

    private void updateUserInfo(String larkOpenId, LarkUserInfoVo larkUserInfo) {
        // 1. 查询系统用户
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getLarkOpenId, larkOpenId));

        if (user == null) {
            return;
        }

        // 2. 更新用户信息
        SysUser updateUser = new SysUser(user.getUserId());

        // 更新用户昵称（如果存在）
        if (StrUtil.isNotBlank(larkUserInfo.getName())) {
            updateUser.setNickName(larkUserInfo.getName());
        }

        // 更新用户头像（如果存在）
        if (StrUtil.isNotBlank(larkUserInfo.getAvatarUrl())) {
            updateUser.setAvatar(larkUserInfo.getAvatarUrl());
        }

        // 更新用户邮箱（如果存在）
        if (StrUtil.isNotBlank(larkUserInfo.getEmail())) {
            updateUser.setEmail(larkUserInfo.getEmail());
        }

        // 更新用户手机号（如果存在）
        if (StrUtil.isNotBlank(larkUserInfo.getMobile())) {
            updateUser.setPhoneNumber(larkUserInfo.getMobile());
        }

        // 更新用户性别（如果存在）
        Integer gender = larkUserInfo.getGender();
        if (gender != null) {
            // 飞书：0-保密，1-男，2-女
            // 系统：0-男，1-女，2-未知
            String sex = switch (gender) {
                case 1 -> "0"; // 男
                case 2 -> "1"; // 女
                default -> "2"; // 未知
            };
            updateUser.setSex(sex);
        }

        // 执行更新操作
        userMapper.updateById(updateUser);
    }
} 