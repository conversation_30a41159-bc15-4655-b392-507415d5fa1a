package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.dashboard.model.entity.EventParamOption;

import java.util.List;

/**
 * <p>
 * 事件参数选项表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface EventParamOptionService extends IService<EventParamOption> {

    List<String> getOptions(String eventKey);

    /**
     * 批量保存并忽略唯一键冲突
     *
     * @param entityList 实体列表
     * @param batchSize  批次大小
     */
    void saveBatchIgnoreDuplicate(List<EventParamOption> entityList, int batchSize);
}
