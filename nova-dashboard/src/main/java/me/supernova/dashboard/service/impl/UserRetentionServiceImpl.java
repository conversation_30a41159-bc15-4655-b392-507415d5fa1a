package me.supernova.dashboard.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.supernova.dashboard.mapper.UserRetentionMapper;
import me.supernova.dashboard.model.bo.UserRetentionBo;
import me.supernova.dashboard.model.dto.UserRetentionMetricsDTO;
import me.supernova.dashboard.model.entity.UserRetention;
import me.supernova.dashboard.model.vo.UserRetentionVo;
import me.supernova.dashboard.service.UserRetentionService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户留存记录表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Service
public class UserRetentionServiceImpl extends ServiceImpl<UserRetentionMapper, UserRetention> implements UserRetentionService {

    private static final int DEFAULT_DAYS = 7;
    private static final int DEFAULT_TIMEZONE = 8;
    private static final int SCALE = 2;
    private static final int PERCENTAGE = 100;

    @Override
    public List<UserRetentionVo> queryUserRetention(UserRetentionBo request) {
        validateAndSetDefaultDates(request);
        String recordType = determineRecordType(request);
        int timezone = determineTimezone(request);

        LocalDateTime startTime = request.getBeginDate().atStartOfDay();
        LocalDateTime endTime = request.getEndDate().atTime(23, 59, 59);

        List<UserRetentionMetricsDTO> metricsList = baseMapper.batchGetUserRetentionMetrics(
                startTime, endTime, recordType, timezone, request.getIpCountryCode(), request.getPlatform(),
                request.getGameName(), request.getStore(), request.getFirstVersion(), request.getLastVersion());

        return metricsList.stream()
                .map(metrics -> buildRetentionVo(metrics.getDate(), metrics))
                .collect(Collectors.toList());
    }

    /**
     * 验证并设置默认日期范围
     */
    private void validateAndSetDefaultDates(UserRetentionBo request) {
        if (request.getBeginDate() == null || request.getEndDate() == null) {
            request.setBeginDate(LocalDate.now().minusDays(DEFAULT_DAYS));
            request.setEndDate(LocalDate.now());
        }

        if (request.getBeginDate().isAfter(request.getEndDate())) {
            throw new RuntimeException("查询开始日期不能大于结束日期");
        }
    }

    /**
     * 确定记录类型
     */
    private String determineRecordType(UserRetentionBo request) {
        return request.getQueryType() == 1 ? "REGISTER" : "ACTIVE";
    }

    /**
     * 确定时区
     */
    private int determineTimezone(UserRetentionBo request) {
        return request.getTimezone() != null ? request.getTimezone() : DEFAULT_TIMEZONE;
    }

    /**
     * 构建留存数据对象
     */
    private UserRetentionVo buildRetentionVo(LocalDate date, UserRetentionMetricsDTO metrics) {
        if (metrics.getActiveUsers() == 0) {
            return createEmptyRetentionVo(date);
        }

        return UserRetentionVo.builder()
                .initialDate(date)
                .activeUsers(metrics.getActiveUsers())
                .day1Retention(calculateRetentionRate(metrics.getDay1RetainedUsers(), metrics.getActiveUsers()))
                .day1RetainedUsers(metrics.getDay1RetainedUsers())
                .day2Retention(calculateRetentionRate(metrics.getDay2RetainedUsers(), metrics.getActiveUsers()))
                .day2RetainedUsers(metrics.getDay2RetainedUsers())
                .day3Retention(calculateRetentionRate(metrics.getDay3RetainedUsers(), metrics.getActiveUsers()))
                .day3RetainedUsers(metrics.getDay3RetainedUsers())
                .day4Retention(calculateRetentionRate(metrics.getDay4RetainedUsers(), metrics.getActiveUsers()))
                .day4RetainedUsers(metrics.getDay4RetainedUsers())
                .day5Retention(calculateRetentionRate(metrics.getDay5RetainedUsers(), metrics.getActiveUsers()))
                .day5RetainedUsers(metrics.getDay5RetainedUsers())
                .day6Retention(calculateRetentionRate(metrics.getDay6RetainedUsers(), metrics.getActiveUsers()))
                .day6RetainedUsers(metrics.getDay6RetainedUsers())
                .day7Retention(calculateRetentionRate(metrics.getDay7RetainedUsers(), metrics.getActiveUsers()))
                .day7RetainedUsers(metrics.getDay7RetainedUsers())
                .day8Retention(calculateRetentionRate(metrics.getDay8RetainedUsers(), metrics.getActiveUsers()))
                .day8RetainedUsers(metrics.getDay8RetainedUsers())
                .day9Retention(calculateRetentionRate(metrics.getDay9RetainedUsers(), metrics.getActiveUsers()))
                .day9RetainedUsers(metrics.getDay9RetainedUsers())
                .build();
    }

    /**
     * 创建空的留存数据对象
     */
    private UserRetentionVo createEmptyRetentionVo(LocalDate date) {
        return UserRetentionVo.builder()
                .initialDate(date)
                .activeUsers(0)
                .day1Retention(BigDecimal.ZERO)
                .day1RetainedUsers(0)
                .day2Retention(BigDecimal.ZERO)
                .day2RetainedUsers(0)
                .day3Retention(BigDecimal.ZERO)
                .day3RetainedUsers(0)
                .day4Retention(BigDecimal.ZERO)
                .day4RetainedUsers(0)
                .day5Retention(BigDecimal.ZERO)
                .day5RetainedUsers(0)
                .day6Retention(BigDecimal.ZERO)
                .day6RetainedUsers(0)
                .day7Retention(BigDecimal.ZERO)
                .day7RetainedUsers(0)
                .day8Retention(BigDecimal.ZERO)
                .day8RetainedUsers(0)
                .day9Retention(BigDecimal.ZERO)
                .day9RetainedUsers(0)
                .build();
    }

    /**
     * 计算留存率
     *
     * @param retainedUsers 留存用户数
     * @param totalUsers    总用户数
     * @return 留存率（百分比）
     */
    private BigDecimal calculateRetentionRate(int retainedUsers, int totalUsers) {
        if (totalUsers == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(retainedUsers)
                .divide(BigDecimal.valueOf(totalUsers), SCALE, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(PERCENTAGE));
    }
}
