package me.supernova.dashboard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.dashboard.model.bo.DeveloperNotification;
import me.supernova.dashboard.model.bo.VoidedPurchaseNotification;
import me.supernova.dashboard.model.entity.Orders;
import me.supernova.dashboard.model.entity.UserInfo;
import me.supernova.dashboard.model.enums.OrderStatusEnum;
import me.supernova.dashboard.service.GooglePayNotificationService;
import me.supernova.dashboard.service.OrdersService;
import me.supernova.dashboard.service.UserInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Google支付通知服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GooglePayNotificationServiceImpl implements GooglePayNotificationService {

    private final OrdersService ordersService;
    private final UserInfoService userInfoService;

    /**
     * 处理Google支付退款通知
     *
     * @param developerNotification 开发者通知对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void voidedNotification(DeveloperNotification developerNotification) {
        if (developerNotification == null) {
            log.warn("google_pay,收到谷歌退款通知为null，不处理");
            return;
        }

        VoidedPurchaseNotification voidedPurchaseNotification = developerNotification.getVoidedPurchaseNotification();
        if (voidedPurchaseNotification == null) {
            log.warn("google_pay,收到谷歌退款通知的voidedPurchaseNotification为null，不处理");
            return;
        }

        String purchaseToken = voidedPurchaseNotification.getPurchaseToken();
        String orderId = voidedPurchaseNotification.getOrderId();
        Integer productType = voidedPurchaseNotification.getProductType();

        log.info("google_pay,收到谷歌退款通知,purchaseToken:{},orderId:{},productType:{}",
                purchaseToken, orderId, productType);

        // 根据订单ID查询订单
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Orders::getTransactionId, orderId);
        queryWrapper.eq(Orders::getOrderStatus, OrderStatusEnum.PAID.getCode());
        Orders order = ordersService.getOne(queryWrapper);

        if (order == null) {
            log.warn("google_pay,收到谷歌退款通知,未找到对应订单,orderId:{}", orderId);
            return;
        }

        // 更新订单状态为已退款
        order.setOrderStatus(OrderStatusEnum.REFUND.getCode());
        order.setUpdateTime(LocalDateTime.now());
        order.setRefundTime(LocalDateTime.now());

        boolean updated = ordersService.updateById(order);
        if (updated) {
            log.info("google_pay,谷歌退款通知处理成功,orderId:{}", orderId);

            // 回收用户的免广告权益
            revokeUserNoAdsPrivilege(order.getUserId());
        } else {
            log.error("google_pay,谷歌退款通知处理失败,orderId:{}", orderId);
        }
    }

    /**
     * 回收用户的免广告权益
     *
     * @param userId 用户ID
     */
    private void revokeUserNoAdsPrivilege(Long userId) {
        try {
            UserInfo userInfo = userInfoService.getById(userId);
            if (userInfo != null) {
                userInfo.setNoAds(false);
                userInfo.setUpdateTime(LocalDateTime.now());
                boolean updated = userInfoService.updateById(userInfo);
                if (updated) {
                    log.info("google_pay,用户免广告权益回收成功,userId:{}", userId);
                } else {
                    log.error("google_pay,用户免广告权益回收失败,userId:{}", userId);
                }
            } else {
                log.warn("google_pay,未找到用户信息,userId:{}", userId);
            }
        } catch (Exception e) {
            log.error("google_pay,回收用户免广告权益异常,userId:{}", userId, e);
        }
    }
} 