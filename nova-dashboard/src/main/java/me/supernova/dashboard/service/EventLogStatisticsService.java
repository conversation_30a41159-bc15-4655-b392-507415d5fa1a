package me.supernova.dashboard.service;

import me.supernova.dashboard.model.bo.DataOverviewBo;
import me.supernova.dashboard.model.dto.AdShowMetricsDTO;
import me.supernova.dashboard.model.dto.PaymentMetricsDTO;
import me.supernova.dashboard.model.dto.UserMetricsDTO;

import java.math.BigDecimal;
import java.time.LocalDate;

public interface EventLogStatisticsService {

    /**
     * 统计总应用使用时间
     *
     * @param date  日期
     * @param qryBo 查询参数
     * @return 总应用使用时间
     */
    BigDecimal countTotalAppTime(LocalDate date, DataOverviewBo qryBo);

    /**
     * 统计应用内购收入
     *
     * @param date  日期
     * @param qryBo 查询参数
     * @return 应用内购收入
     */
    BigDecimal sumIapRevenue(LocalDate date, DataOverviewBo qryBo);

    /**
     * 统计广告收入
     *
     * @param date  日期
     * @param qryBo 查询参数
     * @return 广告收入
     */
    BigDecimal sumAdRevenue(LocalDate date, DataOverviewBo qryBo);

    /**
     * 统计新付费用户数
     *
     * @param date  日期
     * @param qryBo 查询参数
     * @return 新付费用户数
     */
    int countNewPayingUsers(LocalDate date, DataOverviewBo qryBo);

    /**
     * 统计付费用户数
     *
     * @param date  日期
     * @param qryBo 查询参数
     * @return 付费用户数
     */
    int countPayingUsers(LocalDate date, DataOverviewBo qryBo);

    /**
     * 统计支付次数
     *
     * @param date  日期
     * @param qryBo 查询参数
     * @return 支付次数
     */
    PaymentMetricsDTO calculatePaymentDto(LocalDate date, DataOverviewBo qryBo);

    /**
     * 统计视频观看次数
     *
     * @param date  日期
     * @param qryBo 查询参数
     * @return 激励视频观看次数
     */
    AdShowMetricsDTO countAdShowMetricsDTO(LocalDate date, DataOverviewBo qryBo);

    /**
     * 获取用户指标
     *
     * @param date  日期
     * @param qryBo 查询参数
     * @return 用户指标
     */
    UserMetricsDTO getUserMetrics(LocalDate date, DataOverviewBo qryBo);

}
