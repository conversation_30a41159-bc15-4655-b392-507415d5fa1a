package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.dashboard.mapper.EventLogMapper;
import me.supernova.dashboard.model.entity.EventLog;
import me.supernova.dashboard.model.vo.EventLogVo;

import java.util.List;

/**
 * <p>
 * 游戏事件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface EventLogService extends IService<EventLog> {

    /**
     * 获取基础Mapper
     *
     * @return EventLogMapper
     */
    EventLogMapper getBaseMapper();

    /**
     * 保存事件
     *
     * @param vo 事件信息
     */
    void saveEvent(EventLogVo vo);

    /**
     * 保存事件
     *
     * @param vo 事件信息
     */
    void saveServerEvent(EventLogVo vo);

    /**
     * 查询ID大于指定值的记录
     *
     * @param id    ID值
     * @param limit 限制数量
     * @return 事件列表
     */
    List<EventLog> listByGreaterId(Long id, int limit);
}
