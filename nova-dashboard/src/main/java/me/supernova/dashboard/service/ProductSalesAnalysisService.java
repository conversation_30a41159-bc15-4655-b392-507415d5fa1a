package me.supernova.dashboard.service;

import me.supernova.dashboard.model.bo.ProductSalesAnalysisBo;
import me.supernova.dashboard.model.vo.ProductSalesAnalysisVo;

import java.util.List;

/**
 * 商品销售数据分析服务接口
 */
public interface ProductSalesAnalysisService {

    /**
     * 查询商品销售数据分析
     *
     * @param qryBo 查询参数
     * @return 商品销售数据列表
     */
    List<ProductSalesAnalysisVo> queryProductSalesAnalysis(ProductSalesAnalysisBo qryBo);
}
