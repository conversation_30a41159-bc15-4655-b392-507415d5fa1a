package me.supernova.dashboard.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.constant.MdcConstants;
import me.supernova.common.core.exception.ServiceException;
import me.supernova.common.core.utils.ServletUtils;
import me.supernova.common.ip.util.AddressUtils;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.dashboard.mapper.OrdersMapper;
import me.supernova.dashboard.model.bo.GooglePlayReceiptBo;
import me.supernova.dashboard.model.bo.OrderInfoQueryBO;
import me.supernova.dashboard.model.entity.Orders;
import me.supernova.dashboard.model.enums.OrderStatusEnum;
import me.supernova.dashboard.model.enums.PaymentEnvironment;
import me.supernova.dashboard.model.enums.PaymentMethod;
import me.supernova.dashboard.model.vo.ClientInfoVo;
import me.supernova.dashboard.model.vo.DiamondRechargeDto;
import me.supernova.dashboard.service.GooglePlayVerifyService;
import me.supernova.dashboard.service.OrdersService;
import me.supernova.dashboard.service.UserInfoService;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdersServiceImpl extends ServiceImpl<OrdersMapper, Orders> implements OrdersService {

    private final GooglePlayVerifyService googlePlayVerifyService;
    private final UserInfoService userInfoService;

    /**
     * 生成订单号
     * 格式：时间戳 + 6位递增数
     */
    private String generateOrderNo() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String key = "order:no:" + timestamp;
        long sequence = RedisUtils.incrAtomicValue(key);
        // 设置过期时间为1天
        RedisUtils.expire(key, 86400);
        return timestamp + String.format("%06d", sequence);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean verifyOrder(DiamondRechargeDto dto) {
        // 参数校验
        if (dto == null) {
            throw new ServiceException("订单校验参数不能为空");
        }

        if (dto.getUserId() == null) {
            ClientInfoVo clientInfoVo = BeanUtil.copyProperties(dto.getClientInfo(), ClientInfoVo.class);
            dto.setUserId(clientInfoVo.getUserId(userInfoService));
        }

        // 创建订单
        Orders order = new Orders();
        order.setOrderNo(generateOrderNo());
        order.setUserId(dto.getUserId());
        order.setGameName(dto.getClientInfo().getGameName());
        order.setProductId(dto.getProductId());

        //客户端没传productPrice,直接根据 productId 判断价格
        if (dto.getProductPrice().compareTo(BigDecimal.ZERO) >= 0) {
            String[] s = dto.getProductId().split("_");
            String last = Arrays.stream(s).toList().getLast();
            if (StrUtil.isNotBlank(last)) {
                if (NumberUtil.isDouble(last)) {
                    dto.setProductPrice(new BigDecimal(last));
                }
            }
        }
        order.setOrderAmount(dto.getProductPrice());
        order.setPaymentEnvironment(PaymentEnvironment.VERIFYING);
        order.setOrderStatus(OrderStatusEnum.VERIFYING.getCode());
        order.setPaymentMethod(PaymentMethod.GOOGLE_PAY.getCode());
        //这个暂时直接写死
        order.setPaid(true);
        order.setPurchaseTokenRaw(dto.getReceipt());
        order.setDeviceCountryCode(dto.getClientInfo().getDeviceCountryCode());
        order.setIpCountryCode(AddressUtils.getCountryCode(ServletUtils.getCfCountryCode(), ServletUtils.getClientIP()));
        order.setPaymentTime(LocalDateTime.now());
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());

        MDC.put(MdcConstants.ORDER_ID, order.getOrderNo());

        // 解析购买凭证
        GooglePlayReceiptBo receipt = parseGooglePlayReceipt(dto.getReceipt());
        if (receipt != null) {
            order.setPurchaseToken(receipt);
            order.setTransactionId(receipt.getOrderId());
            save(order);

        } else {
            save(order);
            log.info("购买凭证解析失败!");
            return false;
        }

        // 检查 receipt 是否已被使用
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Orders::getPurchaseTokenRaw, dto.getReceipt())
                .eq(Orders::getOrderStatus, OrderStatusEnum.PAID.getCode());
        if (count(wrapper) > 0) {
            throw new ServiceException("该支付凭证已被使用");
        }


        // 同步验证谷歌内购
        return googlePlayVerifyService.processGooglePlayPurchase(receipt, dto.getProductId(), dto.getClientInfo(), order);
    }

    @Override
    public Page<Orders> selectPageOrderList(OrderInfoQueryBO query, PageQuery pageQuery) {
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        if (query.getOrderNo() != null) {
            wrapper.eq(Orders::getOrderNo, query.getOrderNo());
        }
        if (query.getTransactionId() != null) {
            wrapper.eq(Orders::getTransactionId, query.getTransactionId());
        }
        if (query.getUserId() != null) {
            wrapper.eq(Orders::getUserId, query.getUserId().toString());
        }
        if (query.getStatus() != null) {
            wrapper.eq(Orders::getOrderStatus, query.getStatus());
        }
        if (query.getStartTime() != null) {
            wrapper.ge(Orders::getCreateTime, query.getStartTime());
        }
        if (query.getEndTime() != null) {
            wrapper.le(Orders::getCreateTime, query.getEndTime());
        }
        if (query.getIpCountryCode() != null) {
            wrapper.eq(Orders::getIpCountryCode, query.getIpCountryCode());
        }
        if (query.getDeviceCountryCode() != null) {
            wrapper.eq(Orders::getDeviceCountryCode, query.getDeviceCountryCode());
        }
        wrapper.orderByDesc(Orders::getCreateTime);

        return this.page(pageQuery.build(), wrapper);
    }

    /**
     * 解析 Google Play 购买凭证
     *
     * @param receiptJson 购买凭证JSON字符串
     * @return 解析后的购买凭证对象
     */
    private GooglePlayReceiptBo parseGooglePlayReceipt(String receiptJson) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(receiptJson);

            // 解析 Payload 字段
            String payload = rootNode.get("Payload").asText();
            JsonNode payloadNode = mapper.readTree(payload);

            // 解析内部 json 字段
            String innerJson = payloadNode.get("json").asText();
            JsonNode purchaseData = mapper.readTree(innerJson);

            // 构建购买凭证对象
            GooglePlayReceiptBo receipt = new GooglePlayReceiptBo();
            receipt.setOrderId(purchaseData.get("orderId").asText());
            receipt.setPackageName(purchaseData.get("packageName").asText());
            receipt.setProductId(purchaseData.get("productId").asText());
            receipt.setPurchaseToken(purchaseData.get("purchaseToken").asText());
            receipt.setPurchaseTime(purchaseData.get("purchaseTime").asLong());
            receipt.setPurchaseState(purchaseData.get("purchaseState").asInt());
            receipt.setAcknowledged(purchaseData.get("acknowledged").asBoolean());

            // 解析商品详情
            JsonNode skuDetailsArray = payloadNode.get("skuDetails");
            if (skuDetailsArray != null && skuDetailsArray.isArray() && !skuDetailsArray.isEmpty()) {
                JsonNode skuDetails = mapper.readTree(skuDetailsArray.get(0).asText());
                receipt.setPrice(skuDetails.get("price").asText());
                receipt.setPriceAmountMicros(skuDetails.get("price_amount_micros").asLong());
                receipt.setPriceCurrencyCode(skuDetails.get("price_currency_code").asText());
            }

            return receipt;
        } catch (Exception e) {
            log.error("解析 Google Play 购买凭证失败", e);
            return null;
        }
    }

}
