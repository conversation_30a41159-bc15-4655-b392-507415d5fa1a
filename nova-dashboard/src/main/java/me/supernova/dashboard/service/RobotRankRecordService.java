package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.dashboard.model.entity.RobotRankRecord;

import java.util.List;

/**
 * <p>
 * 积分记录表，存储用户积分及排名信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
public interface RobotRankRecordService extends IService<RobotRankRecord> {

    List<RobotRankRecord> getRecordList(Integer hour);
}
