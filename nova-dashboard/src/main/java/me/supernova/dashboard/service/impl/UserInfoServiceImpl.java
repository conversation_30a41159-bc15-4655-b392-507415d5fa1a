package me.supernova.dashboard.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.exception.ServiceException;
import me.supernova.common.core.utils.ServletUtils;
import me.supernova.common.ip.util.AddressUtils;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.dashboard.mapper.UserInfoMapper;
import me.supernova.dashboard.model.bo.DeviceIdentifierQueryBO;
import me.supernova.dashboard.model.bo.UserInfoQueryBO;
import me.supernova.dashboard.model.entity.UserInfo;
import me.supernova.dashboard.model.event.LoginRegisterEvent;
import me.supernova.dashboard.model.vo.ClientInfoVo;
import me.supernova.dashboard.model.vo.UserInfoListVO;
import me.supernova.dashboard.service.ABTestService;
import me.supernova.dashboard.service.UserInfoService;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <p>
 * 设备信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    private final ApplicationEventPublisher eventPublisher;
    private final ABTestService abTestService;


    /**
     * 更新或创建设备信息
     *
     * @param dto 客户端信息
     * @return 可选的用户信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Optional<UserInfo> updateOrCreateDeviceInfoOpt(ClientInfoVo dto) {
        if (!isValidRequest(dto)) {
            return Optional.empty();
        }

        return executeWithLock(dto.getUuid(), () -> processDeviceInfo(dto));
    }

    /**
     * 验证请求参数
     */
    private boolean isValidRequest(ClientInfoVo dto) {
        if (dto == null || StrUtil.isBlank(dto.getUuid())) {
            log.warn("设备信息为空或UUID为空");
            return false;
        }
        return true;
    }

    /**
     * 使用分布式锁执行业务逻辑
     */
    private Optional<UserInfo> executeWithLock(String uuid, Supplier<Optional<UserInfo>> action) {
        RLock lock = RedisUtils.getClient().getLock(uuid);
        boolean isLocked = false;

        try {
            isLocked = lock.tryLock(3, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("获取设备锁失败, uuid: {}", uuid);
                return Optional.empty();
            }
            return action.get();
        } catch (InterruptedException e) {
            log.error("获取设备锁被中断, uuid: {}", uuid, e);
            Thread.currentThread().interrupt();
            throw new ServiceException("处理设备信息时发生中断");
        } finally {
            if (isLocked) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("释放设备锁时发生异常, uuid: {}", uuid, e);
                }
            }
        }
    }

    /**
     * 处理设备信息的核心业务逻辑
     */
    private Optional<UserInfo> processDeviceInfo(ClientInfoVo dto) {
        if (!hasValidDeviceIdentifier(dto)) {
            log.warn("设备标识符验证失败, uuid: {}", dto.getUuid());
            return Optional.empty();
        }

        UserInfo existingUser = queryUserByPriority(dto);
        return Optional.of(existingUser != null ?
                updateExistingUser(existingUser, dto) :
                createNewUser(dto));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UserInfo updateOrCreateDeviceInfo(ClientInfoVo dto) {
        Optional<UserInfo> userInfo = updateOrCreateDeviceInfoOpt(dto);
        if (userInfo.isEmpty()) {
            throw new ServiceException("无法找到对应的用户,或者参数不足,无法继续操作");
        }
        return userInfo.get();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long getUserId(ClientInfoVo dto) {
        return updateOrCreateDeviceInfo(dto).getUserId();
    }

    @Override
    public Page<UserInfo> selectPageUserList(UserInfoQueryBO query, PageQuery pageQuery) {
        return page(pageQuery.build(), buildQueryWrapper(query));
    }

    @Override
    public List<UserInfoListVO> queryUsersByDeviceIdentifiers(DeviceIdentifierQueryBO query) {
        LambdaQueryWrapper<UserInfo> lqw = buildDeviceIdentifierQueryWrapper(query);
        List<UserInfo> userInfoList = this.list(lqw);

        if (CollUtil.isEmpty(userInfoList)) {
            return List.of();
        }

        return BeanUtil.copyToList(userInfoList, UserInfoListVO.class);
    }

    /**
     * 构建设备标识符查询条件
     */
    private LambdaQueryWrapper<UserInfo> buildDeviceIdentifierQueryWrapper(DeviceIdentifierQueryBO query) {
        LambdaQueryWrapper<UserInfo> lqw = new LambdaQueryWrapper<>();

        // 如果有模糊搜索关键词，则在所有设备标识符字段中进行模糊匹配
        if (StrUtil.isNotBlank(query.getFuzzySearch())) {
            lqw.and(wrapper -> wrapper
                    .like(UserInfo::getIdfv, query.getFuzzySearch())
                    .or().like(UserInfo::getIdfa, query.getFuzzySearch())
                    .or().like(UserInfo::getGpsAdid, query.getFuzzySearch())
                    .or().like(UserInfo::getAdjustAdid, query.getFuzzySearch())
                    .or().like(UserInfo::getUuid, query.getFuzzySearch())
            );
        } else {
            // 精确匹配查询，支持多个条件的OR逻辑
            boolean hasCondition = false;

            if (StrUtil.isNotBlank(query.getIdfv())) {
                lqw.eq(UserInfo::getIdfv, query.getIdfv());
                hasCondition = true;
            }

            if (StrUtil.isNotBlank(query.getIdfa())) {
                if (hasCondition) {
                    lqw.or();
                }
                lqw.eq(UserInfo::getIdfa, query.getIdfa());
                hasCondition = true;
            }

            if (StrUtil.isNotBlank(query.getGpsadid())) {
                if (hasCondition) {
                    lqw.or();
                }
                lqw.eq(UserInfo::getGpsAdid, query.getGpsadid());
                hasCondition = true;
            }

            if (StrUtil.isNotBlank(query.getAdjustadid())) {
                if (hasCondition) {
                    lqw.or();
                }
                lqw.eq(UserInfo::getAdjustAdid, query.getAdjustadid());
                hasCondition = true;
            }

            if (StrUtil.isNotBlank(query.getUuid())) {
                if (hasCondition) {
                    lqw.or();
                }
                lqw.eq(UserInfo::getUuid, query.getUuid());
                hasCondition = true;
            }

            // 如果没有任何查询条件，返回空结果
            if (!hasCondition) {
                lqw.eq(UserInfo::getUserId, -1); // 不存在的ID，确保返回空结果
            }
        }

        // 按创建时间降序排列
        lqw.orderByDesc(UserInfo::getCreateTime);

        return lqw;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<UserInfo> buildQueryWrapper(UserInfoQueryBO query) {
        LambdaQueryWrapper<UserInfo> lqw = new LambdaQueryWrapper<>();

        lqw.eq(query.getUserId() != null, UserInfo::getUserId, query.getUserId())
                .eq(StrUtil.isNotBlank(query.getGameName()), UserInfo::getGameName, query.getGameName())
                .eq(StrUtil.isNotBlank(query.getGameVersion()), UserInfo::getGameVersion, query.getGameVersion())
                .eq(StrUtil.isNotBlank(query.getLastVersion()), UserInfo::getLastVersion, query.getLastVersion())
                .eq(StrUtil.isNotBlank(query.getDeviceCountryCode()), UserInfo::getDeviceCountryCode,
                        query.getDeviceCountryCode())
                .eq(StrUtil.isNotBlank(query.getPlatform()), UserInfo::getPlatform, query.getPlatform())
                .eq(StrUtil.isNotBlank(query.getPackageName()), UserInfo::getPackageName, query.getPackageName())
                .eq(StrUtil.isNotBlank(query.getStore()), UserInfo::getStore, query.getStore())
                .eq(query.getNoAds() != null, UserInfo::getNoAds, query.getNoAds())
                .ge(query.getBeginTime() != null, UserInfo::getCreateTime, query.getBeginTime())
                .le(query.getEndTime() != null, UserInfo::getCreateTime, query.getEndTime())
                .ge(query.getLastLoginBeginTime() != null, UserInfo::getLastLoginTime, query.getLastLoginBeginTime())
                .le(query.getLastLoginEndTime() != null, UserInfo::getLastLoginTime, query.getLastLoginEndTime())
                .eq(StrUtil.isNotBlank(query.getIdfv()), UserInfo::getIdfv, query.getIdfv())
                .eq(StrUtil.isNotBlank(query.getIdfa()), UserInfo::getIdfa, query.getIdfa())
                .eq(StrUtil.isNotBlank(query.getGpsAdid()), UserInfo::getGpsAdid, query.getGpsAdid())
                .eq(StrUtil.isNotBlank(query.getAdjustAdid()), UserInfo::getAdjustAdid, query.getAdjustAdid())
                .eq(StrUtil.isNotBlank(query.getUuid()), UserInfo::getUuid, query.getUuid());

        // AB测试分组筛选条件 - 使用PostgreSQL的jsonb操作符查询levelSetting字段
        if (StrUtil.isNotBlank(query.getAbTestGameConfigGroup())) {
            lqw.apply("ab_test_group ->> 'levelSetting' = {0}", query.getAbTestGameConfigGroup());
        }

        // 默认按创建时间降序
        lqw.orderByDesc(UserInfo::getCreateTime);

        return lqw;
    }

    /**
     * 按优先级查询用户
     */
    private UserInfo queryUserByPriority(ClientInfoVo clientInfo) {
        // 按优先级依次查询：adjust_adid > idfv > idfa > gps_adid > uuid
        if (StrUtil.isNotBlank(clientInfo.getAdjustAdid())) {
            UserInfo user = this.getOne(Wrappers.lambdaQuery(UserInfo.class)
                    .eq(UserInfo::getAdjustAdid, clientInfo.getAdjustAdid())
                    .last("limit 1"));
            if (user != null) {
                return user;
            }
        }

        if (StrUtil.isNotBlank(clientInfo.getIdfv())) {
            UserInfo user = this.getOne(Wrappers.lambdaQuery(UserInfo.class)
                    .eq(UserInfo::getIdfv, clientInfo.getIdfv())
                    .last("limit 1"));
            if (user != null) {
                return user;
            }
        }

        if (StrUtil.isNotBlank(clientInfo.getIdfa())) {
            UserInfo user = this.getOne(Wrappers.lambdaQuery(UserInfo.class)
                    .eq(UserInfo::getIdfa, clientInfo.getIdfa())
                    .last("limit 1"));
            if (user != null) {
                return user;
            }
        }

        if (StrUtil.isNotBlank(clientInfo.getGpsAdid())) {
            UserInfo user = this.getOne(Wrappers.lambdaQuery(UserInfo.class)
                    .eq(UserInfo::getGpsAdid, clientInfo.getGpsAdid())
                    .last("limit 1"));
            if (user != null) {
                return user;
            }
        }

        if (StrUtil.isNotBlank(clientInfo.getUuid())) {
            return this.getOne(Wrappers.lambdaQuery(UserInfo.class)
                    .eq(UserInfo::getUuid, clientInfo.getUuid())
                    .last("limit 1"));
        }

        return null;
    }

    /**
     * 更新现有用户信息
     */
    private UserInfo updateExistingUser(UserInfo existingUser, ClientInfoVo clientInfo) {
        // 更新用户信息
        existingUser.setLastVersion(clientInfo.getGameVersion()); // 更新最近版本
        existingUser.setDeviceCountryCode(clientInfo.getDeviceCountryCode());
        existingUser.setLanguageCode(clientInfo.getLanguageCode());
        existingUser.setTimezoneUtc(clientInfo.getTimezoneUtc());
        existingUser.setDeviceModel(clientInfo.getDeviceModel());
        existingUser.setManufacturer(clientInfo.getManufacturer());
        existingUser.setOsVersion(clientInfo.getOsVersion());
        existingUser.setTelecomOperators(clientInfo.getTelecomOperators());
        existingUser.setResolution(clientInfo.getResolution());
        existingUser.setPlatform(clientInfo.getPlatform());
        existingUser.setDpr(clientInfo.getDpr());
        existingUser.setLastLoginTime(LocalDateTime.now());
        existingUser.setLastLoginTimeUtc(LocalDateTime.now(ZoneOffset.UTC));
        existingUser.setLastIp(ServletUtils.getClientIP());

        existingUser.setIdfa(clientInfo.getIdfa());
        existingUser.setIdfv(clientInfo.getIdfv());
        existingUser.setGpsAdid(clientInfo.getGpsAdid());
        existingUser.setUuid(clientInfo.getUuid());
        existingUser.setAdjustAdid(clientInfo.getAdjustAdid());
        if (StrUtil.isBlank(existingUser.getIp())) {
            existingUser.setIp(ServletUtils.getClientIP());
        }
        if (StrUtil.isBlank(existingUser.getIpCountryCode())) {
            existingUser.setIpCountryCode(
                    AddressUtils.getCountryCode(ServletUtils.getCfCountryCode(), ServletUtils.getClientIP()));
        }

        //现在的判断很简单,可能之后会逐渐添加
        if (existingUser.getAbTestGroup() == null) {
            Dict abTestGroup = abTestService.getABTestGroup(existingUser.getUserId());
            existingUser.setAbTestGroup(abTestGroup);
        }

        this.updateById(existingUser);

        // 发布用户登录事件
        eventPublisher.publishEvent(new LoginRegisterEvent(existingUser.getUserId(), clientInfo, "login"));

        return existingUser;
    }

    /**
     * 创建新用户
     */
    private UserInfo createNewUser(ClientInfoVo clientInfo) {
        UserInfo newUser = new UserInfo();
        BeanUtils.copyProperties(clientInfo, newUser);
        newUser.setIp(ServletUtils.getClientIP());
        newUser.setLastIp(ServletUtils.getClientIP());
        newUser.setIpCountryCode(
                AddressUtils.getCountryCode(ServletUtils.getCfCountryCode(), ServletUtils.getClientIP()));
        newUser.setLastLoginTime(LocalDateTime.now());
        newUser.setNoAds(false);
        newUser.setLastVersion(clientInfo.getGameVersion());
        newUser.setLastLoginTimeUtc(LocalDateTime.now(ZoneOffset.UTC));
        newUser.setRegisterTime(LocalDateTime.now());
        newUser.setRegisterTimeUtc(LocalDateTime.now(ZoneOffset.UTC));
        this.save(newUser);

        //在用户创建的时候分配用户的AB 测试组
        Dict abTestGroup = abTestService.getABTestGroup(newUser.getUserId());
        newUser.setAbTestGroup(abTestGroup);
        this.updateById(newUser);

        // 发布用户注册事件
        eventPublisher.publishEvent(new LoginRegisterEvent(newUser.getUserId(), clientInfo, "register"));
        eventPublisher.publishEvent(new LoginRegisterEvent(newUser.getUserId(), clientInfo, "login"));

        return newUser;
    }

    /**
     * 验证设备标识符是否有效
     *
     * @param clientInfo 设备信息
     * @return 是否有效
     */
    private boolean hasValidDeviceIdentifier(ClientInfoVo clientInfo) {
        return StrUtil.isNotBlank(clientInfo.getIdfv()) ||
                StrUtil.isNotBlank(clientInfo.getIdfa()) ||
                StrUtil.isNotBlank(clientInfo.getAdjustAdid()) ||
                StrUtil.isNotBlank(clientInfo.getGpsAdid()) ||
                StrUtil.isNotBlank(clientInfo.getUuid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserNoAdsStatus(Long userId, Boolean noAds) {
        if (userId == null) {
            log.warn("用户ID不能为空");
            return false;
        }

        UserInfo userInfo = this.getById(userId);
        if (userInfo == null) {
            log.warn("用户不存在, userId: {}", userId);
            return false;
        }

        userInfo.setNoAds(noAds);
        boolean result = this.updateById(userInfo);

        if (result) {
            log.info("用户去广告状态修改成功, userId: {}, noAds: {}", userId, noAds);
        } else {
            log.error("用户去广告状态修改失败, userId: {}, noAds: {}", userId, noAds);
        }

        return result;
    }
}
