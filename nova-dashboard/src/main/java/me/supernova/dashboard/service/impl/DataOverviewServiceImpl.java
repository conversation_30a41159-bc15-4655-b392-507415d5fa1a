package me.supernova.dashboard.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.dashboard.model.bo.DataOverviewBo;
import me.supernova.dashboard.model.dto.AdShowMetricsDTO;
import me.supernova.dashboard.model.dto.PaymentMetricsDTO;
import me.supernova.dashboard.model.dto.UserMetricsDTO;
import me.supernova.dashboard.model.vo.DataOverviewVo;
import me.supernova.dashboard.service.DataOverviewService;
import me.supernova.dashboard.service.EventLogStatisticsService;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 数据概览服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataOverviewServiceImpl implements DataOverviewService {

    private final EventLogStatisticsService eventLogStatisticsService;

    @Override
    public List<DataOverviewVo> queryDataOverview(DataOverviewBo qryBo) {
        if (qryBo.getStartDate() == null || qryBo.getEndDate() == null) {
            // 默认最近 7 天
            qryBo.setStartDate(LocalDate.now().minusDays(7));
            qryBo.setEndDate(LocalDate.now());
        }

        if (qryBo.getStartDate().isAfter(qryBo.getEndDate())) {
            throw new RuntimeException("查询开始日期不能大于结束日期");
        }

        // 检查查询时间范围不能超过50天
        long daysBetween = qryBo.getStartDate().until(qryBo.getEndDate()).getDays() + 1;
        if (daysBetween > 50) {
            throw new RuntimeException("查询时间范围不能超过50天");
        }

        return qryBo.getStartDate()
                .datesUntil(qryBo.getEndDate().plusDays(1))
                .toList()
                .parallelStream()
                .map(date -> processDailyData(date, qryBo))
                .sorted(Comparator.comparing(DataOverviewVo::getDate).reversed())
                .toList();
    }

    /**
     * 处理每日数据
     *
     * @param date  日期
     * @param qryBo 查询参数
     * @return 数据概览信息
     */
    private DataOverviewVo processDailyData(LocalDate date, DataOverviewBo qryBo) {
        // 1. 基础用户指标
        UserMetricsDTO userMetrics = eventLogStatisticsService.getUserMetrics(date, qryBo);
        int newUsers = userMetrics.getNewUsers();
        int dau = userMetrics.getDau();
        int retention = userMetrics.getRetention();
        BigDecimal retentionRate = calculatePerUserMetric(retention, dau);

        // 2. 用户行为指标
        BigDecimal totalAppTime = eventLogStatisticsService.countTotalAppTime(date, qryBo);
        //换算成分钟
        totalAppTime = totalAppTime.divide(BigDecimal.valueOf(60), 4, RoundingMode.HALF_UP);
        BigDecimal averageUsageTime = calculatePerUserMetric(totalAppTime, dau);

        // 3. 收入指标
        BigDecimal iapRevenue = eventLogStatisticsService.sumIapRevenue(date, qryBo);
        BigDecimal adRevenue = eventLogStatisticsService.sumAdRevenue(date, qryBo);
        BigDecimal totalRevenue = iapRevenue.add(adRevenue != null ? adRevenue : BigDecimal.ZERO);

        // 4. 付费指标
        int newPayingUsers = eventLogStatisticsService.countNewPayingUsers(date, qryBo);
        BigDecimal newUserPayRate = calculatePerUserMetric(newPayingUsers, newUsers);
        int payingUsers = eventLogStatisticsService.countPayingUsers(date, qryBo);
        BigDecimal payRate = calculatePerUserMetric(payingUsers, dau);

        // 5. 支付行为指标
        PaymentMetricsDTO paymentMetricsDTO = eventLogStatisticsService.calculatePaymentDto(date, qryBo);
        int paymentCount = paymentMetricsDTO.getTotalPaymentCount();
        int newPaymentCount = paymentMetricsDTO.getNewUserPaymentCount();

        // 6. 广告指标
        AdShowMetricsDTO adShowMetricsDTO = eventLogStatisticsService.countAdShowMetricsDTO(date, qryBo);
        int rewardedVideoCount = adShowMetricsDTO.getRewardedVideoCount();
        int interstitialCount = adShowMetricsDTO.getInterstitialCount();

        // 7. 复合指标计算
        BigDecimal arpdau = calculatePerUserMetric(totalRevenue, dau);
        BigDecimal arppu = calculatePerUserMetric(iapRevenue, payingUsers);
        BigDecimal ipuRv = calculatePerUserMetric(rewardedVideoCount, dau);
        BigDecimal ipuInt = calculatePerUserMetric(interstitialCount, dau);

        // 8. LTV 指标计算
        BigDecimal ltvIap = calculatePerUserMetric(iapRevenue, newUsers);
        BigDecimal ltvIaa = calculatePerUserMetric(adRevenue, newUsers);

        // 9. 构建返回对象
        return DataOverviewVo.builder()
                .date(date)
                .newUsers(newUsers)
                .dau(dau)
                .retentionRate(retentionRate)
                .averageUsageTime(averageUsageTime)
                .iapRevenue(iapRevenue)
                .adRevenue(adRevenue)
                .totalRevenue(totalRevenue)
                .newPayingUsers(newPayingUsers)
                .newUserPayRate(newUserPayRate)
                .payingUsers(payingUsers)
                .payRate(payRate)
                .paymentCount(paymentCount)
                .newPaymentCount(newPaymentCount)
                .arpdau(arpdau)
                .arppu(arppu)
                .ipuRv(ipuRv)
                .ipuInt(ipuInt)
                .ltvIap(ltvIap)
                .ltvIaa(ltvIaa)
                .build();
    }

    /**
     * 计算人均指标
     *
     * @param numerator   分子
     * @param denominator 分母
     * @return 计算结果，如果分母为0或分子为null则返回0
     */
    private BigDecimal calculatePerUserMetric(BigDecimal numerator, int denominator) {
        if (numerator == null || denominator <= 0) {
            return BigDecimal.ZERO;
        }
        return numerator.divide(BigDecimal.valueOf(denominator), 4, RoundingMode.HALF_UP);
    }


    private BigDecimal calculatePerUserMetric(int numerator, int denominator) {
        return calculatePerUserMetric(BigDecimal.valueOf(numerator), denominator);
    }

    @Override
    public void queryDataOverviewStream(DataOverviewBo qryBo, SseEmitter emitter) {
        try {
            // 参数验证和默认值设置
            validateAndSetDefaults(qryBo);

            List<LocalDate> dates = qryBo.getStartDate()
                    .datesUntil(qryBo.getEndDate().plusDays(1))
                    .sorted(Comparator.reverseOrder()) // 按日期倒序
                    .toList();

            // 发送总数信息
            emitter.send(SseEmitter.event()
                    .name("total")
                    .data(Map.of("total", dates.size())));

            // 逐个处理并发送数据
            for (int i = 0; i < dates.size(); i++) {
                LocalDate date = dates.get(i);
                DataOverviewVo data = processDailyData(date, qryBo);

                // 发送数据项
                emitter.send(SseEmitter.event()
                        .name("data")
                        .data(data));

                // 发送进度信息
                emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(Map.of("current", i + 1, "total", dates.size())));

                // 避免过快发送，添加小延迟
                Thread.sleep(10);
            }

            // 发送完成信号
            emitter.send(SseEmitter.event()
                    .name("complete")
                    .data("查询完成"));

            emitter.complete();

        } catch (Exception e) {
            emitter.completeWithError(e);
        }
    }

    /**
     * 验证参数并设置默认值
     */
    private void validateAndSetDefaults(DataOverviewBo qryBo) {
        if (qryBo.getStartDate() == null || qryBo.getEndDate() == null) {
            // 默认最近 7 天
            qryBo.setStartDate(LocalDate.now().minusDays(7));
            qryBo.setEndDate(LocalDate.now());
        }

        if (qryBo.getStartDate().isAfter(qryBo.getEndDate())) {
            throw new RuntimeException("查询开始日期不能大于结束日期");
        }

        // 检查查询时间范围不能超过50天
        long daysBetween = qryBo.getStartDate().until(qryBo.getEndDate()).getDays() + 1;
        if (daysBetween > 50) {
            throw new RuntimeException("查询时间范围不能超过50天");
        }
    }
}