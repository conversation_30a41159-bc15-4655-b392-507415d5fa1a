package me.supernova.dashboard.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.constant.CacheConstants;
import me.supernova.common.core.constant.Constants;
import me.supernova.common.core.domain.dto.RoleDTO;
import me.supernova.common.core.domain.model.LoginUser;
import me.supernova.common.core.enums.UserStatus;
import me.supernova.common.core.exception.ServiceException;
import me.supernova.common.core.utils.ServletUtils;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.common.satoken.utils.LoginHelper;
import me.supernova.dashboard.mapper.SysUserMapper;
import me.supernova.dashboard.model.bo.LarkLoginBo;
import me.supernova.dashboard.model.bo.LoginBo;
import me.supernova.dashboard.model.constant.ErrorEnum;
import me.supernova.dashboard.model.entity.SysUser;
import me.supernova.dashboard.model.event.LoginInfoEvent;
import me.supernova.dashboard.model.vo.LarkUserInfoVo;
import me.supernova.dashboard.model.vo.LoginVo;
import me.supernova.dashboard.model.vo.SysDeptVo;
import me.supernova.dashboard.model.vo.SysRoleVo;
import me.supernova.dashboard.service.*;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserLoginService {

    private final static Integer MAX_RETRY_COUNT = 5;

    private final static Integer LOCK_TIME = 30;

    private final SysUserMapper userMapper;
    private final SysPermissionService permissionService;
    private final SysDeptService deptService;
    private final SysRoleService roleService;
    private final SysLoginInfoService sysLoginInfoService;
    private final LarkService larkService;

    /**
     * 处理共同的登录逻辑
     *
     * @param user         系统用户
     * @param loginMessage 登录成功的消息
     * @return 登录信息
     */
    private LoginVo handleCommonLogin(SysUser user, String loginMessage) {
        // 检查用户状态
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            throw new ServiceException("用户已被停用");
        }

        // 更新用户登录信息
        SysUser updateUser = new SysUser();
        updateUser.setUserId(user.getUserId());
        updateUser.setLoginDate(LocalDateTime.now());
        updateUser.setLoginIp(ServletUtils.getClientIP());
        userMapper.updateById(updateUser);

        // 记录登录信息
        recordLoginInfoEvent(user.getUserName(), Constants.LOGIN_SUCCESS, loginMessage);

        // 生成token
        LoginHelper.login(buildLoginUser(user));

        return new LoginVo(StpUtil.getTokenValue());
    }

    public LoginVo login(LoginBo req) {
        //判断用户是否存在
        SysUser user = loadUserByUsername(req.getUsername());

        // 检查用户登录信息
        checkLogin(req.getUsername(), req.getPassword(), user.getPassword());

        return handleCommonLogin(user, "登录成功");
    }

    /**
     * 检查用户登录信息
     * 此方法用于验证用户登录的合法性通过用户名、明文密码和哈希密码进行验证
     * 它还跟踪连续登录失败次数，并在超过最大尝试次数时锁定用户登录
     *
     * @param username  用户名
     * @param plainPwd  明文密码
     * @param hashedPwd 哈希密码
     */
    public void checkLogin(String username, String plainPwd, String hashedPwd) {
        String errorKey = CacheConstants.PWD_ERR_CNT_KEY + username;

        // 1. 判断用户是否被锁定
        int errorNumber = ObjectUtil.defaultIfNull(RedisUtils.getCacheObject(errorKey), 0);
        if (errorNumber >= MAX_RETRY_COUNT) {
            String message = StrUtil.format("连续登陆次数超过 {} 次,锁定 {} 分钟", MAX_RETRY_COUNT, LOCK_TIME);
            recordLoginInfoEvent(username, Constants.LOGIN_FAIL, message);
            throw new ServiceException(message);
        }

        //2. 判断用户名密码是否匹配
        if (!BCrypt.checkpw(plainPwd, hashedPwd)) {
            // 错误次数递增并记录
            errorNumber++;
            RedisUtils.setCacheObject(errorKey, errorNumber, Duration.ofMinutes(LOCK_TIME));
            // 达到规定错误次数 则锁定登录
            String message = errorNumber >= MAX_RETRY_COUNT ?
                    StrUtil.format("连续登陆次数超过 {} 次,锁定 {} 分钟", MAX_RETRY_COUNT, LOCK_TIME) :
                    StrUtil.format("登录失败次数: {}, 还剩 {} 次尝试, 超过后将锁定 {} 分钟", errorNumber, MAX_RETRY_COUNT - errorNumber, LOCK_TIME);
            recordLoginInfoEvent(username, Constants.LOGIN_FAIL, message);
            throw new ServiceException(message);
        }

        //3. 登录成功 清空错误次数
        RedisUtils.deleteObject(errorKey);
    }

    private void recordLoginInfoEvent(String username, String loginStatus, String message) {
        LoginInfoEvent loginInfoEvent = new LoginInfoEvent();
        loginInfoEvent.setIp(ServletUtils.getClientIP());
        loginInfoEvent.setOs(ServletUtils.getOs());
        loginInfoEvent.setBrowser(ServletUtils.getBrowser());
        loginInfoEvent.setStatus(loginStatus);
        loginInfoEvent.setUsername(username);
        loginInfoEvent.setMessage(message);

        sysLoginInfoService.recordLoginInfo(loginInfoEvent);
    }

    /**
     * 构建登录用户
     */
    public LoginUser buildLoginUser(SysUser user) {
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(user.getUserId());
        loginUser.setDeptId(user.getDeptId());
        loginUser.setUsername(user.getUserName());
        loginUser.setNickname(user.getNickName());
        loginUser.setUserType(user.getUserType());
        loginUser.setMenuPermission(permissionService.getMenuPermission(user.getUserId()));
        loginUser.setRolePermission(permissionService.getRolePermission(user.getUserId()));

        if (user.getDeptId() != null) {
            SysDeptVo dept = deptService.selectDeptById(user.getDeptId());
            if (dept != null) {
                loginUser.setDeptName(dept.getDeptName() != null ? dept.getDeptName() : "");
            }
        }
        List<SysRoleVo> roles = roleService.selectRolesByUserId(user.getUserId());
        loginUser.setRoles(BeanUtil.copyToList(roles, RoleDTO.class));
        return loginUser;
    }

    private SysUser loadUserByUsername(String username) {
        SysUser user = userMapper.selectOne(
                new LambdaQueryWrapper<SysUser>().eq(
                        SysUser::getUserName, username
                )
        );
        if (user == null) {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException("用户不存在!");
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException("用户已被停用!");
        }
        return user;
    }

    public LoginVo larkLogin(LarkLoginBo req) {
        // 获取飞书用户信息
        LarkUserInfoVo larkUserInfo = getLarkUserInfo(req);

        // 处理用户登录或绑定
        SysUser larkUser = processLarkUser(larkUserInfo);

        // 异步同步飞书用户信息
        larkService.syncUserInfo(larkUserInfo.getOpenId());

        return handleCommonLogin(larkUser, "飞书登录成功");
    }

    private LarkUserInfoVo getLarkUserInfo(LarkLoginBo req) {
        String userAccessToken = larkService.getUserAccessToken(req.getCode(), req.getRedirectUri());
        return larkService.getLarkUserInfo(userAccessToken);
    }

    private SysUser processLarkUser(LarkUserInfoVo larkUserInfo) {
        SysUser larkUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getLarkOpenId, larkUserInfo.getOpenId()));

        if (larkUser != null) {
            return larkUser;
        }

        return bindLarkUser(larkUserInfo);
    }

    private SysUser bindLarkUser(LarkUserInfoVo larkUserInfo) {
        LoginUser loginUser = LoginHelper.getLoginUserOpt()
                .orElseThrow(() -> new ServiceException(ErrorEnum.FEISHU_ACCOUNT_NOT_BOUND));

        // 更新用户飞书绑定信息
        SysUser updateSysUser = new SysUser();
        updateSysUser.setUserId(loginUser.getUserId());
        updateSysUser.setLarkOpenId(larkUserInfo.getOpenId());
        userMapper.updateById(updateSysUser);

        // 记录绑定信息
        recordLoginInfoEvent(loginUser.getUsername(), Constants.LOGIN_SUCCESS, "飞书账号绑定成功");

        return userMapper.selectById(loginUser.getUserId());
    }

}