package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.dashboard.model.bo.UserRetentionBo;
import me.supernova.dashboard.model.entity.UserRetention;
import me.supernova.dashboard.model.vo.UserRetentionVo;

import java.util.List;

/**
 * <p>
 * 用户留存记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
public interface UserRetentionService extends IService<UserRetention> {

    List<UserRetentionVo> queryUserRetention(UserRetentionBo request);
}
