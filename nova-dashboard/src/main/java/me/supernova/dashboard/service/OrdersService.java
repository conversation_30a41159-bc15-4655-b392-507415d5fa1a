package me.supernova.dashboard.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.dashboard.model.bo.OrderInfoQueryBO;
import me.supernova.dashboard.model.entity.Orders;
import me.supernova.dashboard.model.vo.DiamondRechargeDto;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public interface OrdersService extends IService<Orders> {

    /**
     * 订单校验
     *
     * @param dto 订单校验请求
     */
    Boolean verifyOrder(DiamondRechargeDto dto);

    /**
     * 分页查询订单列表
     *
     * @param query     查询条件
     * @param pageQuery 分页参数
     * @return 订单列表
     */
    Page<Orders> selectPageOrderList(OrderInfoQueryBO query, PageQuery pageQuery);
}
