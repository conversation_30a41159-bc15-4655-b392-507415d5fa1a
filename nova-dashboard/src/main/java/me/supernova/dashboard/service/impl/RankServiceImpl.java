package me.supernova.dashboard.service.impl;

import lombok.RequiredArgsConstructor;
import me.supernova.dashboard.model.dto.RankDto;
import me.supernova.dashboard.model.vo.RankResponse;
import me.supernova.dashboard.model.vo.RankVo;
import me.supernova.dashboard.service.RankService;
import me.supernova.dashboard.service.UserInfoService;
import me.supernova.dashboard.service.UserRankService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 排行榜服务实现类
 */
@Service
@RequiredArgsConstructor
public class RankServiceImpl implements RankService {

    private final UserRankService userRankService;
    private final UserInfoService userInfoService;

    private static final int TOP_N = 100;

    @Override
    public RankResponse getRank(RankDto dto) {
        Long userId = dto.getClientInfo().getUserId(userInfoService);

        // 更新用户分数
        userRankService.updateScore(userId, dto);
        List<RankVo> rankVoList = userRankService.getTopNRankVos(TOP_N, 100);
        if (rankVoList == null || rankVoList.isEmpty()) {
            return RankResponse.builder().build();
        }

        // 获取当前用户排名
        Integer currentUserRank = null;
        if (dto.getScore() >= 100) {
            currentUserRank = userRankService.getRank(userId);
        }

        // 返回排行榜
        return RankResponse.builder()
                .rankList(rankVoList)
                .currentUserRank(currentUserRank)
                .build();
    }

}