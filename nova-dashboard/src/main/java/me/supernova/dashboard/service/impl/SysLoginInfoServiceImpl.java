package me.supernova.dashboard.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.constant.Constants;
import me.supernova.common.ip.util.AddressUtils;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.dashboard.mapper.SysLoginInfoMapper;
import me.supernova.dashboard.model.bo.SysLoginInfoBo;
import me.supernova.dashboard.model.entity.SysLoginInfo;
import me.supernova.dashboard.model.event.LoginInfoEvent;
import me.supernova.dashboard.model.vo.SysLoginInfoVo;
import me.supernova.dashboard.service.SysLoginInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 系统访问日志情况信息 服务层处理
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class SysLoginInfoServiceImpl extends ServiceImpl<SysLoginInfoMapper, SysLoginInfo> implements SysLoginInfoService {

    private final SysLoginInfoMapper baseMapper;

    /**
     * 记录登录信息
     *
     * @param loginInfoEvent 登录事件
     */
    @Async
    @Override
    public void recordLoginInfo(LoginInfoEvent loginInfoEvent) {
        final String ip = loginInfoEvent.getIp();

        String address = AddressUtils.getAddressByIP(ip);
        String s = getBlock(ip) +
                address +
                getBlock(loginInfoEvent.getUsername()) +
                getBlock(loginInfoEvent.getStatus()) +
                getBlock(loginInfoEvent.getMessage());
        // 打印信息到日志
        log.info(s, loginInfoEvent.getArgs());
        // 封装对象
        SysLoginInfoBo loginInfo = new SysLoginInfoBo();
        loginInfo.setUserName(loginInfoEvent.getUsername());
        loginInfo.setIpaddr(ip);
        loginInfo.setLoginLocation(address);
        loginInfo.setBrowser(loginInfoEvent.getBrowser());
        loginInfo.setOs(loginInfoEvent.getOs());
        loginInfo.setMsg(loginInfoEvent.getMessage());
        // 日志状态
        if (StringUtils.equalsAny(loginInfoEvent.getStatus(), Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            loginInfo.setStatus(Constants.SUCCESS);
        } else if (Constants.LOGIN_FAIL.equals(loginInfoEvent.getStatus())) {
            loginInfo.setStatus(Constants.FAIL);
        }
        // 插入数据
        insertloginInfo(loginInfo);
    }

    private String getBlock(Object msg) {
        if (msg == null) {
            msg = "";
        }
        return "[" + msg + "]";
    }

    @Override
    public TableDataInfo<SysLoginInfoVo> selectPageloginInfoList(SysLoginInfoBo loginInfo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysLoginInfo> lqw = new LambdaQueryWrapper<SysLoginInfo>()
                .like(StrUtil.isNotBlank(loginInfo.getIpaddr()), SysLoginInfo::getIpaddr, loginInfo.getIpaddr())
                .eq(StrUtil.isNotBlank(loginInfo.getStatus()), SysLoginInfo::getStatus, loginInfo.getStatus())
                .like(StrUtil.isNotBlank(loginInfo.getUserName()), SysLoginInfo::getUserName, loginInfo.getUserName())
                .between(loginInfo.getBeginTime() != null && loginInfo.getEndTime() != null,
                        SysLoginInfo::getLoginTime, loginInfo.getBeginTime(), loginInfo.getEndTime());
        if (StrUtil.isBlank(pageQuery.getOrderByColumn())) {
            lqw.orderByDesc(SysLoginInfo::getInfoId);
        }
        Page<SysLoginInfoVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 新增系统登录日志
     *
     * @param bo 访问日志对象
     */
    @Override
    public void insertloginInfo(SysLoginInfoBo bo) {
        SysLoginInfo loginInfo = BeanUtil.copyProperties(bo, SysLoginInfo.class);
        loginInfo.setLoginTime(LocalDateTime.now());

        baseMapper.insert(loginInfo);
    }

    /**
     * 查询系统登录日志集合
     *
     * @param loginInfo 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLoginInfoVo> selectLoginInfoList(SysLoginInfoBo loginInfo) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SysLoginInfo>()
                .like(StrUtil.isNotBlank(loginInfo.getIpaddr()), SysLoginInfo::getIpaddr, loginInfo.getIpaddr())
                .eq(StrUtil.isNotBlank(loginInfo.getStatus()), SysLoginInfo::getStatus, loginInfo.getStatus())
                .like(StrUtil.isNotBlank(loginInfo.getUserName()), SysLoginInfo::getUserName, loginInfo.getUserName())
                .between(loginInfo.getBeginTime() != null && loginInfo.getEndTime() != null, SysLoginInfo::getLoginTime, loginInfo.getBeginTime(), loginInfo.getEndTime())
                .orderByDesc(SysLoginInfo::getInfoId));
    }

    /**
     * 批量删除系统登录日志
     *
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    @Override
    public int deleteLoginInfoByIds(Long[] infoIds) {
        return baseMapper.deleteByIds(Arrays.asList(infoIds));
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLoginInfo() {
        baseMapper.delete(new LambdaQueryWrapper<>());
    }
}
