package me.supernova.dashboard.job;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.dashboard.model.entity.EventLog;
import me.supernova.dashboard.model.entity.EventParamOption;
import me.supernova.dashboard.model.enums.EventTypeEnum;
import me.supernova.dashboard.service.EventLogService;
import me.supernova.dashboard.service.EventParamOptionService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 事件参数扫描任务
 * <p>
 * 该任务的主要功能是扫描事件表(EventLog)中的数据，并将事件参数以树形结构存储到Redis中。
 * 通过定时任务每小时执行一次，确保Redis中的数据与数据库保持同步。
 * <p>
 * 事件存储示例：
 * 1. 事件数据示例：
 * EventLog {
 * eventType: "CLICK",
 * eventName: "button",
 * param1: "home",
 * param2: "top",
 * param3: "banner",
 * param4: "nav",
 * param5: "search"
 * ...
 * }
 * <p>
 * 2. Redis存储示例：
 * - 键格式：event:param:tree:{eventType}:{eventName}:{param1}:{param2}...
 * - 值：Set<String> 存储该路径下的所有可能值
 * <p>
 * 具体存储示例：
 * 1. 事件类型层级：
 * event:param:tree:CLICK -> Set("button", "link", "image")
 * event:param:tree:CLICK:button -> Set("home", "about", "contact")
 * event:param:tree:CLICK:button:home -> Set("top", "middle", "bottom")
 * event:param:tree:CLICK:button:home:top -> Set("banner", "nav", "search")
 * <p>
 * <p>
 * 这种存储结构可以用于：
 * 1. 快速查询某个事件类型下的所有可能值
 * 2. 构建事件参数的树形选择器
 * 3. 分析事件参数的分布情况
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EventParamScanJob {

    private final EventLogService eventLogService;
    private final EventParamOptionService eventParamOptionService;

    private static final String SCAN_MAX_ID_KEY = "event:scan:max_processed_id";
    public static final String EVENT_PARAM_TREE_PREFIX = "event:param:tree:";
    private static final int BATCH_SIZE = 1000;
    private static final int PARAM_COUNT = 12; // 总共12个节点 (事件类型、事件名、param1-param10)

    @Scheduled(cron = "0 30 * * * ?")
    public void execute() {
        log.info("开始执行事件参数扫描任务");
        long startTime = System.currentTimeMillis();
        try {
            scanEventTableBatch();
        } catch (Exception e) {
            log.error("事件参数扫描任务执行失败", e);
        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            log.info("事件参数扫描任务执行完成，耗时：{}ms", costTime);
        }
    }

    /**
     * 批量扫描事件表方法
     * 特点：
     * 1. 只存储已处理的最大ID作为断点
     * 2. 每次处理1000条记录
     * 3. 扫描直到全表结束
     */
    public void scanEventTableBatch() {
        log.info("开始批量扫描事件表");

        // 获取已处理的最大ID
        String lastMaxId = RedisUtils.getCacheObject(SCAN_MAX_ID_KEY);

        if (lastMaxId == null) lastMaxId = "0";


        log.info("从ID:{}开始扫描", lastMaxId);

        long lastMaxIdLong = Long.parseLong(lastMaxId);
        int pageNum = 1;
        boolean hasMore = true;
        int totalProcessed = 0;
        long currentMaxId = lastMaxIdLong;
        long batchStartTime;

        while (hasMore) {
            batchStartTime = System.currentTimeMillis();

            // 分页查询数据，每次获取BATCH_SIZE条，查询ID大于lastMaxId的数据
            List<EventLog> eventList = eventLogService.listByGreaterId(lastMaxIdLong, BATCH_SIZE);

            if (eventList == null || eventList.isEmpty()) {
                hasMore = false;
                log.info("批量扫描事件表完成，没有更多数据");
                continue;
            }

            int batchSize = eventList.size();
            log.info("正在处理第{}批数据，数量：{}", pageNum, batchSize);

            // 并行处理当前批次数据以提高性能
            processEventBatch(eventList);
            totalProcessed += batchSize;

            // 更新最大ID
            long oldMaxId = currentMaxId;
            currentMaxId = updateMaxId(eventList, currentMaxId);

            // 确保ID是递增的，防止异常情况
            if (currentMaxId > oldMaxId) {
                // 记录当前处理的最大ID到Redis
                RedisUtils.setCacheObject(SCAN_MAX_ID_KEY, String.valueOf(currentMaxId));
            } else {
                log.warn("当前批次最大ID {}不大于上一批次最大ID {}，可能存在数据异常", currentMaxId, oldMaxId);
            }

            // 如果本次获取的数据量小于批次大小，表示没有更多数据了
            if (batchSize < BATCH_SIZE) {
                hasMore = false;
                log.info("批量扫描事件表完成，本批次数据量小于{}，扫描结束", BATCH_SIZE);
            }

            // 更新lastMaxId，准备下一轮查询
            lastMaxIdLong = currentMaxId;
            pageNum++;

            log.info("第{}批数据处理完成，耗时：{}ms", pageNum - 1, System.currentTimeMillis() - batchStartTime);
        }

        log.info("事件表批量扫描完成，共处理{}批数据，总处理记录数：{}，最大ID：{}",
                pageNum - 1, totalProcessed, currentMaxId);
    }

    /**
     * 处理一批事件数据
     *
     * @param eventList 事件列表
     */
    private void processEventBatch(List<EventLog> eventList) {
        if (eventList == null || eventList.isEmpty()) {
            log.info("事件列表为空，跳过处理");
            return;
        }

        // 使用ConcurrentHashMap替代synchronized包装的HashMap，性能更好
        Map<String, Set<String>> batchMap = new HashMap<>(eventList.size() * 2);

        eventList.forEach(event -> {
            if (event == null) {
                log.warn("跳过处理：事件对象为空");
                return;
            }
            String[] nodes = buildNodesArray(event);
            processEventNodes(nodes, batchMap);
        });

        if (!batchMap.isEmpty()) {
            executeBatchRedisOperations(batchMap);
        }
        log.info("成功处理{}条事件数据", eventList.size());
    }

    // 抽取节点处理逻辑为单独方法，提高可读性
    private void processEventNodes(String[] nodes, Map<String, Set<String>> batchMap) {
        if (StrUtil.isBlank(nodes[0])) {
            return;
        }

        // 处理事件类型和事件名称
        String eventTypeKey = EVENT_PARAM_TREE_PREFIX + nodes[0];
        if (StrUtil.isNotBlank(nodes[1])) {
            batchMap.computeIfAbsent(eventTypeKey, k -> new HashSet<>())
                    .add(nodes[1]);
        }

        // 处理事件参数,这里处理 general事件的特殊情况,eventNameKey 为空
        String eventNameKey = EVENT_PARAM_TREE_PREFIX + nodes[0];
        if (StrUtil.isNotBlank(nodes[1])) {
            eventNameKey = eventNameKey + ":" + nodes[1];
        }

        for (int i = 2; i < nodes.length; i++) {
            if (StrUtil.isNotBlank(nodes[i])) {
                String paramKey = eventNameKey + ":param" + (i - 1);
                batchMap.computeIfAbsent(paramKey, k -> new HashSet<>())
                        .add(nodes[i]);
            }
        }
    }

    private static String[] buildNodesArray(EventLog event) {
        String[] nodes = new String[PARAM_COUNT];
        nodes[0] = event.getEventType() != null ? event.getEventType().name() : null;

        // 如果是自定义事件类型，跳过eventName
        if (event.getEventType() == EventTypeEnum.GENERAL_EVENT) {
            nodes[1] = null;
        } else {
            nodes[1] = event.getEventName();
        }

        nodes[2] = event.getParam1();
        nodes[3] = event.getParam2();
        nodes[4] = event.getParam3();
        nodes[5] = event.getParam4();
        nodes[6] = event.getParam5();
        nodes[7] = event.getParam6();
        nodes[8] = event.getParam7();
        nodes[9] = event.getParam8();
        nodes[10] = event.getParam9();
        return nodes;
    }

    /**
     * 批量执行Redis操作
     *
     * @param batchOperations 批量操作集合
     */
    private void executeBatchRedisOperations(Map<String, Set<String>> batchOperations) {
        if (batchOperations == null || batchOperations.isEmpty()) {
            log.debug("批量操作集合为空，跳过处理");
            return;
        }

        // 预估容量，避免频繁扩容
        int estimatedSize = batchOperations.values().stream()
                .mapToInt(Set::size)
                .sum();
        List<EventParamOption> eventParamOptions = new ArrayList<>(estimatedSize);

        // 使用Stream API优化循环
        batchOperations.forEach((key, values) ->
                values.forEach(value -> {
                    EventParamOption option = new EventParamOption();
                    option.setKey(key);
                    option.setValue(value);
                    option.setCreateTime(LocalDateTime.now());
                    eventParamOptions.add(option);
                })
        );

        // 批量保存，使用事务提高性能
        if (!eventParamOptions.isEmpty()) {
            eventParamOptionService.saveBatchIgnoreDuplicate(eventParamOptions, 1000);
            log.debug("成功保存{}条事件参数选项", eventParamOptions.size());
        }
    }

    /**
     * 更新最大ID
     *
     * @param eventList    事件列表
     * @param currentMaxId 当前最大ID
     * @return 更新后的最大ID
     */
    private long updateMaxId(List<EventLog> eventList, long currentMaxId) {
        return eventList.stream()
                .mapToLong(EventLog::getId)
                .max()
                .orElse(currentMaxId);
    }

}
