package me.supernova.dashboard.job;

import cn.hutool.core.util.RandomUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.dashboard.model.entity.RobotRankRecord;
import me.supernova.dashboard.model.entity.UserRank;
import me.supernova.dashboard.model.vo.RankVo;
import me.supernova.dashboard.service.RobotRankRecordService;
import me.supernova.dashboard.service.UserRankService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class RobotScoreRecordJob {

    private final RobotRankRecordService robotRankRecordService;
    private final UserRankService userRankService;

    // 每个小时
    @Scheduled(cron = "30 0 * * * ?")
    public void execute() {
        log.debug("机器人分数记录任务开始执行...");

        Integer hour = ZonedDateTime.now(ZoneOffset.UTC).getHour();
        if (!RedisUtils.isExistsObject("robtScoreJobInit")) {
            hour = null;
        }

        List<RobotRankRecord> recordList = robotRankRecordService.getRecordList(hour);
        log.debug("获取到 {} 条机器人记录", recordList.size());

        // 获取前100名真人用户
        List<RankVo> topNUserRankVos = userRankService.getTopNUserRankVos(1000, 100);
        log.debug("获取到 {} 名用户", topNUserRankVos.size());

        // 反转得到按分数升序的列表
        List<RankVo> reversedRank = topNUserRankVos.reversed();

        recordList.forEach(record -> handleScoreChange(record, reversedRank));

        //不设置过期时间,保证数字为 null 的时候只有一次
        RedisUtils.setCacheObject("robtScoreJobInit", "123");
    }

    private UserRank getOrCreateRobotUserRank(RobotRankRecord record) {
        UserRank userRank = userRankService.getRecordByUserId(record.getUid());
        if (userRank == null) {
            log.debug("机器人用户 {} 不存在，准备创建新用户...", record.getUid());
            userRank = new UserRank();
            LocalDateTime currentTime = LocalDateTime.now(); // 提取currentTime
            userRank.setUserId(record.getUid());
            userRank.setScore(record.getInitialScore());
            userRank.setCreateTime(currentTime);
            userRank.setUpdateTime(currentTime);
            userRank.setCountryCode(record.getCountryCode());
            userRank.setNickname(record.getNickname());
            userRank.setAvatar(record.getAvatar());
            userRank.setRobot(true);
            userRankService.save(userRank);
            log.debug("机器人用户 {} 创建成功", record.getUid());
        }
        return userRank;
    }

    /**
     * 机器人规则
     * 1. 分值和排名更新时间
     * 每日 utc0 点开始统计排行榜前 100 名分值，根据条件判断在更新时间间隔（time_update）条件下，刷新机器人排名和分值.
     * 2. 机器人轮换
     * 机器人超过 30 天后不再更新排名，轮换其他机器人，并每30 天循环一轮次机器人，具体以robota_config为准
     * 3. 分值和排名计算规则
     * - P：玩家分值数组，P[i] 表示第i名玩家的分值
     * - R：机器人分值数组，R[i] 表示离 P[i] 最近的1 个机器人的分值
     * - T：（threshold）空缺阈值（分值差距超过此值时，启动机器人填充空缺阈值）
     * - A：advance每日基础积分增长范围
     * - S：score当前分值
     * 分值计算
     * 1. 判断空缺 ：当P[i] - R[i] > t时，向上判断，启动机器人填充机制
     * 2. 按空缺阀值T增加积分
     * 3. 机器人每日分值：S+A+T
     */
    private void handleScoreChange(RobotRankRecord record, List<RankVo> reversedRank) {

        // 根据机器人的 id 找到用户
        UserRank userRank = getOrCreateRobotUserRank(record);
        log.debug("开始处理机器人 UID: {} 的分数变更, 当前分数: {}", userRank.getUserId(), userRank.getScore());

        // 获取 S
        Long s = record.getScore();

        // 获取 A
        Long a = getAScore(record);

        // 获取 T
        Long t = getTScore(record, reversedRank);
        log.debug("机器人 UID: {}, S: {}, A: {}, T: {}", userRank.getUserId(), s, a, t);

        // 更新分值
        long newScore = s + a + t;
        userRank.setScore(newScore);
        userRank.setUpdateTime(LocalDateTime.now()); // 设置更新时间
        userRankService.updateById(userRank);
        record.setScore(newScore);
        record.setLastUpdate(LocalDateTime.now());
        robotRankRecordService.updateById(record);
        log.debug("机器人 UID: {} 分数更新成功，新分数为: {}", userRank.getUserId(), newScore);

    }

    /**
     * 获取 T 分
     * 遍历按分数升序排列的玩家列表 (reversedUserRank)，找到第一个分数高于当前机器人的玩家。
     * 如果该玩家与机器人的分差大于机器人配置的阈值 (threshold)，则 T 分为该阈值。
     * 否则 (包括没有找到更高分玩家的情况)，T 分为 0。
     */
    private Long getTScore(RobotRankRecord robotRecord, List<RankVo> reversedUserRank) {
        Long robotScore = robotRecord.getScore();
        Integer threshold = robotRecord.getThreshold();
        log.debug("计算T分：机器人 UID: {}, 当前分数: {}, 阈值: {}", robotRecord.getUid(), robotScore, threshold);

        // 如果机器人配置的阈值为 null，则 T 分为 0
        if (threshold == null) {
            log.debug("机器人 UID: {} 阈值为 null, T分为0", robotRecord.getUid());
            return 0L;
        }

        // reversedUserRank 是按分数升序排列的玩家列表
        // 找到第一个分数高于当前机器人的玩家
        return reversedUserRank.stream()
                .filter(userRank -> userRank.getScore() > robotScore)
                .findFirst()
                .map(closestHigherScoreUser -> {
                    // 计算该玩家与机器人的分差
                    long scoreDifference = closestHigherScoreUser.getScore() - robotScore;
                    log.debug("机器人 UID: {}, 最近的更高分玩家分数: {}, 分差: {}", robotRecord.getUid(), closestHigherScoreUser.getScore(), scoreDifference);
                    // 如果分差大于机器人配置的阈值，则T分为阈值，否则为0
                    long tScore = scoreDifference > threshold ? threshold.longValue() : 0L;
                    log.debug("机器人 UID: {}, 计算得到 T分: {}", robotRecord.getUid(), tScore);
                    return tScore;
                })
                .orElseGet(() -> {
                    log.debug("机器人 UID: {} 未找到分数更高的玩家, T分为0", robotRecord.getUid());
                    return 0L; // 如果没有找到分数比机器人高的玩家，则T分为0
                });
    }

    /**
     * 每日积分增长随机范围
     * 随机范围: 5 * [1, advance]
     */
    private Long getAScore(RobotRankRecord record) {
        Integer advanceValue = record.getAdvance();
        log.debug("计算A分：机器人 UID: {}, advanceValue: {}", record.getUid(), advanceValue);
        // 如果 advanceValue 为 null 或者小于1, 则默认为1. 确保 advance 至少为1.
        int advance = (advanceValue == null || advanceValue < 1) ? 1 : advanceValue;

        // RandomUtil.randomInt(inclusiveOrigin, exclusiveBound)
        // 若希望得到 [1, advance] 范围内的整数，则参数应为 (1, advance + 1)
        // 例如, advance = 1, randomInt(1, 2) -> 1
        // 例如, advance = 5, randomInt(1, 6) -> 1, 2, 3, 4, 5
        int randomMultiplier = RandomUtil.randomInt(1, advance + 1);
        long aScore = randomMultiplier * 5L;
        log.debug("机器人 UID: {}, advance: {}, 随机乘数: {}, 计算得到 A分: {}", record.getUid(), advance, randomMultiplier, aScore);
        return aScore;
    }
}
