package me.supernova.dashboard.job;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.dashboard.mapper.EventLogMapper;
import me.supernova.dashboard.model.dto.TimeConfig;
import me.supernova.dashboard.model.dto.UserRetentionDTO;
import me.supernova.dashboard.model.entity.UserRetention;
import me.supernova.dashboard.service.UserRetentionService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserRetentionJob {
    private final UserRetentionService userRetentionService;
    private final EventLogMapper eventLogMapper;

    private static final int RETENTION_DAYS = 9;
    private static final int LOCAL_TIMEZONE = 8;
    private static final int UTC_TIMEZONE = 0;

    /**
     * 每天 上午 7 点执行
     */
    @Scheduled(cron = "0 0 7 * * ?")
    public void execute() {
        LocalDateTime startDay = LocalDateTime.now().minusDays(RETENTION_DAYS);

        log.info("开始执行用户留存计算任务，计算日期: {}", startDay.toLocalDate());
        processAndSaveRetentionForEventType(startDay, "register", "REGISTER");
        processAndSaveRetentionForEventType(startDay, "login", "ACTIVE");
        log.info("用户留存计算任务执行完成");
    }

    private void processAndSaveRetentionForEventType(LocalDateTime startDay, String eventType, String recordType) {
        LocalDateTime endDate = startDay.plusDays(RETENTION_DAYS);

        // 处理本地时区
        log.info("开始处理本地时区 {}留存，计算日期: {}", recordType, startDay.toLocalDate());
        TimeConfig localTimeConfig = new TimeConfig(startDay, endDate, "event_time");
        List<UserRetentionDTO> localRetention = eventLogMapper.calculateUserRetention(localTimeConfig, eventType);
        saveRetentionData(localRetention, recordType, LOCAL_TIMEZONE);

        // 处理UTC时区
        log.info("开始处理UTC时区 {}留存，计算日期: {}", recordType, startDay.toLocalDate());
        TimeConfig utcTimeConfig = new TimeConfig(startDay, endDate, "event_time_utc");
        List<UserRetentionDTO> utcRetention = eventLogMapper.calculateUserRetention(utcTimeConfig, eventType);
        saveRetentionData(utcRetention, recordType, UTC_TIMEZONE);
    }

    /**
     * 保存留存数据
     */
    private void saveRetentionData(List<UserRetentionDTO> retentionData, String recordType, int timezone) {

        //获取所有的日期
        List<LocalDate> dates = retentionData.stream()
                .map(UserRetentionDTO::getStartDate)
                .distinct()
                .toList();
        if (dates.isEmpty()) {
            log.info("没有留存数据，记录类型: {}, 时区: {}", recordType, timezone);
            return;
        }
        //先清理数据
        userRetentionService.lambdaUpdate()
                .eq(UserRetention::getTimezone, timezone)
                .eq(UserRetention::getRecordType, recordType)
                .in(UserRetention::getStartDate, dates)
                .remove();

        log.info("开始保存留存数据，记录类型: {}, 时区: {}", recordType, timezone);
        for (UserRetentionDTO data : retentionData) {
            UserRetention retention = new UserRetention();
            retention.setUserId(data.getUserId());
            retention.setRecordType(recordType);
            retention.setTimezone(timezone);
            retention.setStartDate(data.getStartDate());
            retention.setDay1Retained(data.getDay1Retained() > 0);
            retention.setDay2Retained(data.getDay2Retained() > 0);
            retention.setDay3Retained(data.getDay3Retained() > 0);
            retention.setDay4Retained(data.getDay4Retained() > 0);
            retention.setDay5Retained(data.getDay5Retained() > 0);
            retention.setDay6Retained(data.getDay6Retained() > 0);
            retention.setDay7Retained(data.getDay7Retained() > 0);
            retention.setDay8Retained(data.getDay8Retained() > 0);
            retention.setDay9Retained(data.getDay9Retained() > 0);
            retention.setCreatedAt(LocalDateTime.now());
            retention.setUpdatedAt(LocalDateTime.now());
            // 先查询是否存在记录
            UserRetention existingRetention = userRetentionService.lambdaQuery()
                    .eq(UserRetention::getUserId, data.getUserId())
                    .eq(UserRetention::getTimezone, timezone)
                    .eq(UserRetention::getRecordType, recordType)
                    .eq(UserRetention::getStartDate, data.getStartDate())
                    .one();

            if (existingRetention != null) {
                // 如果存在，更新记录
                retention.setId(existingRetention.getId());
                userRetentionService.updateById(retention);
            } else {
                // 如果不存在，插入新记录
                userRetentionService.save(retention);
            }
        }
    }
}