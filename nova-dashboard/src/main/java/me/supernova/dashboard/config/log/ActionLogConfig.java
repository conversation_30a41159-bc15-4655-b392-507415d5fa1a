package me.supernova.dashboard.config.log;

import me.supernova.common.act.log.core.dao.ActionLogDao;
import me.supernova.dashboard.service.SysActionLogService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ActionLogConfig {

    @Bean
    public ActionLogDao logDao(SysActionLogService actionLogService) {
        return new ActionLogDataBaseDao(actionLogService);
    }
}