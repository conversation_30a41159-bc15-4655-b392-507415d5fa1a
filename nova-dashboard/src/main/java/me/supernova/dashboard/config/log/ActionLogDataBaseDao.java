package me.supernova.dashboard.config.log;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import me.supernova.common.act.log.core.dao.ActionLogDao;
import me.supernova.common.act.log.core.model.LogRecord;
import me.supernova.common.act.log.core.model.LogRequest;
import me.supernova.common.act.log.core.model.LogResponse;
import me.supernova.common.core.constant.MdcConstants;
import me.supernova.common.core.domain.R;
import me.supernova.common.json.utils.JsonUtils;
import me.supernova.dashboard.model.entity.SysActionLog;
import me.supernova.dashboard.service.SysActionLogService;

import java.util.Map;

public class ActionLogDataBaseDao implements ActionLogDao {
    private final SysActionLogService actionLogService;

    public ActionLogDataBaseDao(SysActionLogService actionLogService) {
        this.actionLogService = actionLogService;
    }

    @Override
    public void add(LogRecord logRecord) {
        SysActionLog actionLog = new SysActionLog();

        // 设置请求信息
        LogRequest logRequest = logRecord.getRequest();
        setRequest(actionLog, logRequest);

        // 设置响应信息
        LogResponse logResponse = logRecord.getResponse();
        setResponse(actionLog, logResponse);

        // 设置基本信息
        actionLog.setDescription(logRecord.getDescription() != null ? logRecord.getDescription() : "");
        actionLog.setModule(logRecord.getModule() != null ? logRecord.getModule() : "");

        actionLog.setTimeTaken(logRecord.getTimeTaken().toMillis());
        actionLog.setCreateTime(logRecord.getTimestamp());

        actionLogService.save(actionLog);
    }

    /**
     * 设置请求信息
     *
     * @param actionLog  日志信息
     * @param logRequest 请求信息
     */
    private void setRequest(SysActionLog actionLog, LogRequest logRequest) {
        actionLog.setRequestMethod(logRequest.getMethod());
        actionLog.setRequestHost(logRequest.getUrl().getHost());
        actionLog.setRequestPath(logRequest.getUrl().getPath());
        actionLog.setRequestHeaders(logRequest.getHeaders());
        actionLog.setRequestBody(JsonUtils.parseMap(logRequest.getBody()));
        actionLog.setRequestParam(logRequest.getParam());
        actionLog.setIp(logRequest.getIp());
        actionLog.setAddress(logRequest.getAddress());
        actionLog.setBrowser(logRequest.getBrowser());
        actionLog.setOs(StrUtil.subBefore(logRequest.getOs(), " or", false));
    }

    /**
     * 设置响应信息
     *
     * @param actionLog   日志信息
     * @param logResponse 响应信息
     */
    private void setResponse(SysActionLog actionLog, LogResponse logResponse) {
        Map<String, String> responseHeaders = logResponse.getHeaders();
        actionLog.setTraceId(responseHeaders.get(MdcConstants.TRACE_ID));
        actionLog.setResponseHeaders(responseHeaders);
        actionLog.setResponseBody(JsonUtils.parseMap(logResponse.getBody()));
        actionLog.setStatusCode(logResponse.getStatus());
        actionLog.setStatus(logResponse.getStatus() >= HttpStatus.HTTP_BAD_REQUEST ? 2 : 1);
        if (StrUtil.isNotBlank(logResponse.getBody())) {
            try {
                R<?> result = JSONUtil.toBean(logResponse.getBody(), R.class);
                if (R.isError(result)) {
                    actionLog.setStatus(2);
                    actionLog.setErrorMsg(result.getMessage());
                }
            } catch (Exception e) {
                // 响应体不是有效的JSON格式或不是R类型，忽略错误状态检查
                // 保持原有的状态码判断逻辑
            }
        }
    }
}