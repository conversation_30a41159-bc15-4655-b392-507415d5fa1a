package me.supernova.dashboard.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@Schema(description = "用户留存分析响应")
public class UserRetentionVo {

    @Schema(description = "初始日期")
    private LocalDate initialDate;

    @Schema(description = "激活用户数")
    private Integer activeUsers;

    @Schema(description = "Day 1 留存率")
    private BigDecimal day1Retention;

    @Schema(description = "Day 1 留存用户数")
    private Integer day1RetainedUsers;

    @Schema(description = "Day 2 留存率")
    private BigDecimal day2Retention;

    @Schema(description = "Day 2 留存用户数")
    private Integer day2RetainedUsers;

    @Schema(description = "Day 3 留存率")
    private BigDecimal day3Retention;

    @Schema(description = "Day 3 留存用户数")
    private Integer day3RetainedUsers;

    @Schema(description = "Day 4 留存率")
    private BigDecimal day4Retention;

    @Schema(description = "Day 4 留存用户数")
    private Integer day4RetainedUsers;

    @Schema(description = "Day 5 留存率")
    private BigDecimal day5Retention;

    @Schema(description = "Day 5 留存用户数")
    private Integer day5RetainedUsers;

    @Schema(description = "Day 6 留存率")
    private BigDecimal day6Retention;

    @Schema(description = "Day 6 留存用户数")
    private Integer day6RetainedUsers;

    @Schema(description = "Day 7 留存率")
    private BigDecimal day7Retention;

    @Schema(description = "Day 7 留存用户数")
    private Integer day7RetainedUsers;

    @Schema(description = "Day 8 留存率")
    private BigDecimal day8Retention;

    @Schema(description = "Day 8 留存用户数")
    private Integer day8RetainedUsers;

    @Schema(description = "Day 9 留存率")
    private BigDecimal day9Retention;

    @Schema(description = "Day 9 留存用户数")
    private Integer day9RetainedUsers;
} 