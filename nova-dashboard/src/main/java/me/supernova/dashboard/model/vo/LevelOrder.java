package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "关卡顺序映射")
public class LevelOrder {
    @Schema(description = "关卡ID", example = "1")
    @JsonProperty("LevelId")
    private Integer levelId;

    @Schema(description = "关卡名称", example = "第一关")
    @JsonProperty("LevelName")
    private String levelName;

    @Schema(description = "关卡顺序", example = "1")
    @JsonProperty("Order")
    private Integer order;
} 