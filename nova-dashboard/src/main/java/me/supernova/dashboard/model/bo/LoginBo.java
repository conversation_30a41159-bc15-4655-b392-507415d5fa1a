package me.supernova.dashboard.model.bo;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import static me.supernova.common.core.constant.UserConstants.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoginBo {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Length(
            min = USERNAME_MIN_LENGTH,
            max = USERNAME_MAX_LENGTH,
            message = "用户名不合法"
    )
    private String username = "";


    /**
     * 用户密码
     */
    @NotBlank(message = "密码不能为空")
    @Length(
            min = PASSWORD_MIN_LENGTH,
            max = PASSWORD_MAX_LENGTH,
            message = "用户密码长度必须在{min}到{max}个字符之间"
    )
    private String password = "";
}