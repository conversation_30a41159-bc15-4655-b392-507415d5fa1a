package me.supernova.dashboard.model.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/2 09:56
 * @desc
 */
@Data
public class DeveloperNotification {
    /**
     * 此通知的版本。最初，此值为“1.0”。此版本与其他版本字段不同。
     */
    private String version;
    /**
     * 与此通知相关的应用的软件包名称（例如“com.some.thing”）
     */
    private String packageName;
    /**
     * 事件发生的时间戳，以从公元纪年开始计算的毫秒数表示。
     */
    private Long eventTimeMillis;
    /**
     * 如果此字段存在，则此通知与订阅相关，并且此字段包含与订阅相关的其他信息。请注意，此字段与 testNotification 和 oneTimeProductNotification 互斥。
     */
    private OneTimeProductNotification oneTimeProductNotification;
    /**
     * 如果此字段存在，则此通知与一次性购买相关，并且此字段包含与购买交易相关的其他信息。请注意，此字段与 testNotification 和 subscriptionProductNotification 互斥。
     */
    private SubscriptionNotification subscriptionNotification;
    /**
     * 如果此字段存在，则此通知与作废的购买交易相关，并且此字段包含与作废的购买交易相关的其他信息。请注意，此字段与 oneTimeProductNotification、subscriptionNotification 和 testNotification 互斥。
     */
    private VoidedPurchaseNotification voidedPurchaseNotification;
    /**
     * 如果此字段存在，则此通知与测试发布相关。这些只通过 Google Play 管理中心发送。请注意，此字段与 subscriptionNotification 和 oneTimeProductNotification 互斥。
     */
    private TestNotification testNotification;
}
