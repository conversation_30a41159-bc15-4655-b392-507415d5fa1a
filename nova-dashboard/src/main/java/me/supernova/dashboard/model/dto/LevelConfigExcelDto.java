package me.supernova.dashboard.model.dto;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 关卡配置Excel导入DTO
 *
 * <AUTHOR>
 */
@Data
public class LevelConfigExcelDto {

    @ExcelProperty("id")
    private String id;

    @ExcelProperty("level_id")
    private String levelId;

    @ExcelProperty("mode_type")
    private String modeType;

    @ExcelProperty("txt_cn")
    private String txtCn;

    @ExcelProperty("level_order")
    private String levelOrder;

    @ExcelProperty("time_limited")
    private String timeLimited;

    @ExcelProperty("unlock_conditions")
    private String unlockConditions;

    @ExcelProperty("lv_pass_params")
    private String lvPassParams;

    @ExcelProperty("level_switch")
    private String levelSwitch;

    @ExcelProperty("hint_txt_switch")
    private String hintTxtSwitch;

    @ExcelProperty("hint_txt_zh")
    private String hintTxtZh;

    @ExcelProperty("hint_txt_en")
    private String hintTxtEn;

    @ExcelProperty("hint_ui_zh")
    private String hintUiZh;

    @ExcelProperty("hint_ui_en")
    private String hintUiEn;
}
