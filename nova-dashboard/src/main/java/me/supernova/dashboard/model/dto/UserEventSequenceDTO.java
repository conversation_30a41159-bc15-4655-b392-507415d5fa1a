package me.supernova.dashboard.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户事件序列数据传输对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserEventSequenceDTO {

    /**
     * 事件名称
     */
    private String filterName;

    /**
     * 独立用户数
     */
    private Integer userCount;
    /**
     * 排序
     */
    private Integer sort;

}