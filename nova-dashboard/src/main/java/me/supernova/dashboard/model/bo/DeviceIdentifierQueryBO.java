package me.supernova.dashboard.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 设备标识符查询BO
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@Schema(description = "设备标识符查询BO")
public class DeviceIdentifierQueryBO {

    @Schema(description = "iOS设备标识符")
    @Size(max = 255, message = "iOS设备标识符长度不能超过255个字符")
    private String idfv;

    @Schema(description = "iOS广告标识符")
    @Size(max = 255, message = "iOS广告标识符长度不能超过255个字符")
    private String idfa;

    @Schema(description = "Google Play Services广告ID")
    @Size(max = 255, message = "Google Play Services广告ID长度不能超过255个字符")
    private String gpsadid;

    @Schema(description = "Adjust广告标识符")
    @Size(max = 255, message = "Adjust广告标识符长度不能超过255个字符")
    private String adjustadid;

    @Schema(description = "设备UUID")
    @Size(max = 255, message = "设备UUID长度不能超过255个字符")
    private String uuid;

    @Schema(description = "模糊搜索关键词，将在所有设备标识符字段中进行模糊匹配")
    @Size(max = 255, message = "模糊搜索关键词长度不能超过255个字符")
    private String fuzzySearch;
}
