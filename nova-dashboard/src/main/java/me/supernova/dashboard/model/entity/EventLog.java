package me.supernova.dashboard.model.entity;

import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import me.supernova.common.mybatis.helper.JSONTypePgHandler;
import me.supernova.dashboard.model.enums.EventTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 游戏事件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Getter
@Setter
@ToString
@TableName("event_log")
@Schema(name = "EventLog", description = "游戏事件表")
public class EventLog {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 事件ID
     */
    @Schema(description = "事件ID")
    private String eventId;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型")
    private EventTypeEnum eventType;

    /**
     * 事件名
     */
    @Schema(description = "事件名")
    private String eventName;

    /**
     * 事件发生时间
     */
    @Schema(description = "事件发生时间")
    private LocalDateTime eventTime;

    /**
     * 事件发生时间(Utc)
     */
    @Schema(description = "事件发生时间(Utc)")
    private LocalDateTime eventTimeUtc;

    /**
     * 事件数值(如付费金额)
     */
    @Schema(description = "事件数值(如付费金额)")
    private BigDecimal eventValue;

    @Schema(description = "参数 1")
    private String param1;

    @Schema(description = "参数 2")
    private String param2;

    @Schema(description = "参数 3")
    private String param3;

    @Schema(description = "参数 4")
    private String param4;

    @Schema(description = "参数 5")
    private String param5;

    @Schema(description = "参数 6")
    private String param6;

    @Schema(description = "参数 7")
    private String param7;

    @Schema(description = "参数 8")
    private String param8;

    @Schema(description = "参数 9")
    private String param9;

    @TableField(typeHandler = JSONTypePgHandler.class)
    private Dict eventParams;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 客户端生成的唯一标识符
     */
    @Schema(description = "客户端生成的唯一标识符")
    private String uuid;

    /**
     * iOS设备标识符
     */
    @Schema(description = "iOS设备标识符")
    private String idfv;

    /**
     * iOS广告标识符
     */
    @Schema(description = "iOS广告标识符")
    private String idfa;

    /**
     * adjust广告ID
     */
    @Schema(description = "adjust广告ID")
    private String adjustAdid;

    /**
     * 游戏名称
     */
    @Schema(description = "游戏名称")
    private String gameName;

    /**
     * 游戏版本
     */
    @Schema(description = "游戏版本")
    private String gameVersion;

    /**
     * 平台(Android/iOS)
     */
    @Schema(description = "平台(Android/iOS)")
    private String platform;

    /**
     * ip国家码
     */
    @Schema(description = "ip国家码")
    private String ipCountryCode;

    /**
     * 设备国家码
     */
    @Schema(description = "设备国家码")
    private String deviceCountryCode;

    /**
     * 语言码
     */
    @Schema(description = "语言码")
    private String languageCode;

    /**
     * 操作系统版本
     */
    @Schema(description = "操作系统版本")
    private String osVersion;

    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    private String ip;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
