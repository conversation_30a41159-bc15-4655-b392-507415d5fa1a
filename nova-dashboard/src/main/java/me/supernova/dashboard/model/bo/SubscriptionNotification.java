package me.supernova.dashboard.model.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/2 09:52
 * @desc
 */
@Data
public class SubscriptionNotification {
    /**
     * 此通知的版本。最初，此值为“1.0”。此版本与其他版本字段不同。
     */
    private String version;
    /**
     * 订阅的 notificationType
     * (1) ONE_TIME_PRODUCT_PURCHASED - 用户成功购买了一次性商品。
     * (2) ONE_TIME_PRODUCT_CANCELED - 用户已取消待处理的一次性商品购买交易。
     */
    private int notificationType;
    /**
     * 购买订阅时向用户设备提供的令牌。
     */
    private String purchaseToken;
    /**
     * 购买的一次性商品的商品 ID（例如“sword_001”）。
     */
    private String subscriptionId;
}
