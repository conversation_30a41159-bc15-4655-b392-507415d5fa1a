package me.supernova.dashboard.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 * @since 2024-03-31
 */
@Getter
@AllArgsConstructor
public enum PaymentMethod {

    /**
     * Google Pay
     */
    GOOGLE_PAY("GOOGLE_PAY", "Google Pay"),

    /**
     * Apple Pay
     */
    APPLE_PAY("APPLE_PAY", "Apple Pay");

    private final String code;
    private final String description;

    public static PaymentMethod getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PaymentMethod value : values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
} 