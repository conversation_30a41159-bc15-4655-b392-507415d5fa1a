package me.supernova.dashboard.model.vo;

import cn.hutool.core.lang.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "用户信息列表VO")
public class UserInfoListVO {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "游戏名称")
    private String gameName;

    @Schema(description = "游戏版本")
    private String gameVersion;

    @Schema(description = "最近版本")
    private String lastVersion;

    @Schema(description = "付费总金额")
    private BigDecimal totalPaymentAmount;

    @Schema(description = "付费次数")
    private Integer paymentCount;

    @Schema(description = "国家码(ISO 3166-1)")
    private String deviceCountryCode;

    @Schema(description = "语言码")
    private String languageCode;

    @Schema(description = "时区")
    private Integer timezoneUtc;

    @Schema(description = "设备型号")
    private String deviceModel;

    @Schema(description = "制造商")
    private String manufacturer;

    @Schema(description = "系统版本")
    private String osVersion;

    @Schema(description = "运营商")
    private String telecomOperators;

    @Schema(description = "分辨率")
    private String resolution;

    @Schema(description = "设备类型(Android/iOS)")
    private String platform;

    @Schema(description = "包名")
    private String packageName;

    @Schema(description = "下载渠道")
    private String store;

    @Schema(description = "设备像素比")
    private BigDecimal dpr;

    @Schema(description = "是否拥有免广告权益")
    private Boolean noAds;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "last_ip")
    private String lastIp;


    @Schema(description = "ip对应的国家码(ISO 3166-1)")
    private String ipCountryCode;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "最近登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "iOS设备标识符")
    private String idfv;

    @Schema(description = "iOS广告标识符")
    private String idfa;

    @Schema(description = "Google Play Services广告ID")
    private String gpsAdid;

    @Schema(description = "adjustAdid")
    private String adjustAdid;

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "ABTest 分组")
    private Dict abTestGroup;
} 