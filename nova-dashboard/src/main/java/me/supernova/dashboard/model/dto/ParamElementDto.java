package me.supernova.dashboard.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class ParamElementDto {

    /**
     * 漏斗层名称
     */
    private String filterName;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件类型
     */
    private String eventType;
    /**
     * 排序
     */
    private Integer sort;

    private List<String> param1;
    private List<String> param2;
    private List<String> param3;
    private List<String> param4;
    private List<String> param5;
    private List<String> param6;
    private List<String> param7;
    private List<String> param8;
    private List<String> param9;
}