package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "响应")
public class GameConfigV2 {


    @Schema(description = "AB测试的组")
    @JsonProperty("AbGroup")
    private String abGroup;

    /**
     * 间隔广告配置
     */
    @Schema(description = "间隔广告配置")
    @JsonProperty("IntervalAdConfig")
    private IntervalAdConfig intervalAdConfig;

    /**
     * 评分远程配置
     */
    @Schema(description = "评分远程配置")
    @JsonProperty("RateConfig")
    private RateConfig rateConfig;

    /**
     * 关卡数据
     */
    @Schema(description = "关卡数据")
    @JsonProperty("LevelConfigs")
    private List<LevelConfigVo> levelConfigs;

    @Data
    public static class IntervalAdConfig {
        /**
         * 间隔时间(秒)
         */
        @Schema(description = "间隔时间(秒)")
        @JsonProperty("PlayIntersInterval")
        private Long playIntersInterval;

        /**
         * 首次播放关卡
         */
        @Schema(description = "首次播放关卡")
        @JsonProperty("FirstNoIntersCount")
        private Integer firstNoIntersCount;

        /**
         * 间隔关卡数
         */
        @Schema(description = "间隔关卡数")
        @JsonProperty("PlayIntersIntervalLevel")
        private Integer playIntersIntervalLevel;
    }

    @Data
    public static class RateConfig {
        /**
         * 首次触发通关数
         */
        @Schema(description = "首次触发通关数")
        @JsonProperty("FirstTriggerLevelCount")
        private Integer firstTriggerLevelCount;

        /**
         * 间隔关卡数
         */
        @Schema(description = "间隔关卡数")
        @JsonProperty("IntervalLevelCount")
        private Integer intervalLevelCount;

        /**
         * 触发次数上限
         */
        @Schema(description = "触发次数上限")
        @JsonProperty("MaxTriggerCount")
        private Integer maxTriggerCount;
    }
}
