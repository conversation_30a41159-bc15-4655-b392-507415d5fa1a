package me.supernova.dashboard.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "漏斗事件响应参数")
@AllArgsConstructor
@NoArgsConstructor
public class FunnelEventVO {

    @Schema(description = "事件名称")
    private String eventName;

    @Schema(description = "排序序号")
    private Integer sort;

    @Schema(description = "事件人数")
    private Integer userCount;
} 