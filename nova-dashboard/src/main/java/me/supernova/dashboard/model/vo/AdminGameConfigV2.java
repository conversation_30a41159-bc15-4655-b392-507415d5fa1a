package me.supernova.dashboard.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 后台管理系统游戏配置V2
 * 与GameConfigV2不同，此类不包含@JsonProperty注解，用于后台管理系统的请求和响应
 */
@Data
@Schema(description = "后台管理系统游戏配置V2")
public class AdminGameConfigV2 {

    @Schema(description = "AB测试的组")
    private String abGroup;

    /**
     * 间隔广告配置
     */
    @Schema(description = "间隔广告配置")
    private IntervalAdConfig intervalAdConfig;

    /**
     * 评分远程配置
     */
    @Schema(description = "评分远程配置")
    private RateConfig rateConfig;

    /**
     * 关卡数据
     */
    @Schema(description = "关卡数据")
    private List<AdminLevelConfigVo> levelConfigs;

    @Data
    public static class IntervalAdConfig {
        /**
         * 间隔时间(秒)
         */
        @Schema(description = "间隔时间(秒)")
        private Long playIntersInterval;

        /**
         * 首次播放关卡
         */
        @Schema(description = "首次播放关卡")
        private Integer firstNoIntersCount;

        /**
         * 间隔关卡数
         */
        @Schema(description = "间隔关卡数")
        private Integer playIntersIntervalLevel;
    }

    @Data
    public static class RateConfig {
        /**
         * 首次触发通关数
         */
        @Schema(description = "首次触发通关数")
        private Integer firstTriggerLevelCount;

        /**
         * 间隔关卡数
         */
        @Schema(description = "间隔关卡数")
        private Integer intervalLevelCount;

        /**
         * 触发次数上限
         */
        @Schema(description = "触发次数上限")
        private Integer maxTriggerCount;
    }
}
