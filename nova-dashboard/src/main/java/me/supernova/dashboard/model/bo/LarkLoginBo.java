package me.supernova.dashboard.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LarkLoginBo {

    @NotBlank(message = "飞书登录code不能为空")
    @Schema(description = "飞书登录code")
    private String code;

    @Schema(description = "回调地址")
    private String redirectUri;
}
