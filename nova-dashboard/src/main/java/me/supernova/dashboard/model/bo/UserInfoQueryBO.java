package me.supernova.dashboard.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户信息查询BO
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@Schema(description = "用户信息查询BO")
public class UserInfoQueryBO {


    @Schema(description = "用户 ID")
    private Long userId;

    @Schema(description = "游戏名称")
    private String gameName;

    @Schema(description = "游戏版本")
    private String gameVersion;


    @Schema(description = "最近版本")
    private String lastVersion;


    @Schema(description = "付费总金额最小值")
    private BigDecimal minTotalPaymentAmount;


    @Schema(description = "付费总金额最大值")
    private BigDecimal maxTotalPaymentAmount;


    @Schema(description = "付费次数最小值")
    private Integer minPaymentCount;


    @Schema(description = "付费次数最大值")
    private Integer maxPaymentCount;


    @Schema(description = "国家码(ISO 3166-1)")
    private String deviceCountryCode;


    @Schema(description = "语言码")
    private String languageCode;


    @Schema(description = "时区")
    private Integer timezoneUtc;


    @Schema(description = "设备型号")
    private String deviceModel;


    @Schema(description = "制造商")
    private String manufacturer;


    @Schema(description = "系统版本")
    private String osVersion;


    @Schema(description = "运营商")
    private String telecomOperators;


    @Schema(description = "分辨率")
    private String resolution;


    @Schema(description = "设备类型(Android/iOS)")
    private String platform;


    @Schema(description = "包名")
    private String packageName;


    @Schema(description = "下载渠道")
    private String store;


    @Schema(description = "是否拥有免广告权益")
    private Boolean noAds;


    @Schema(description = "开始时间")
    private LocalDateTime beginTime;


    @Schema(description = "结束时间")
    private LocalDateTime endTime;


    @Schema(description = "最近登录开始时间")
    private LocalDateTime lastLoginBeginTime;


    @Schema(description = "最近登录结束时间")
    private LocalDateTime lastLoginEndTime;


    @Schema(description = "iOS设备标识符")
    private String idfv;


    @Schema(description = "iOS广告标识符")
    private String idfa;


    @Schema(description = "Google Play Services广告ID")
    private String gpsAdid;


    @Schema(description = "adjustAdid")
    private String adjustAdid;


    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "abTestGameConfigGroup")
    private String abTestGameConfigGroup;
}