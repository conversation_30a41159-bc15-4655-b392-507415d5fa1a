package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class DiamondRechargeDto extends OldClientInfoVo {

    @Schema(description = "用户id")
    @JsonProperty("UserId")
    private Long userId;

    @NotBlank(message = "凭证不能为空")
    @Schema(description = "凭证")
    @JsonProperty("Receipt")
    private String receipt;

    @NotBlank(message = "产品id不能为空")
    @Schema(description = "调用支付的时候需要上送的productId")
    @JsonProperty("ProductId")
    private String productId;

    @NotNull(message = "产品价格不能为空")
    @Schema(description = "产品价格")
    @JsonProperty("ProductPrice")
    private BigDecimal productPrice;

    @Schema(description = "客户端信息")
    @JsonProperty("ClientInfo")
    private ClientInfoVo clientInfo;
}