package me.supernova.dashboard.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 订单查询条件
 */
@Data
@Schema(description = "订单查询条件")
public class OrderInfoQueryBO {

    @Schema(description = "内部订单号")
    private String orderNo;

    @Schema(description = "平台订单号")
    private String transactionId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "订单状态")
    private String status;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "IP国家代码")
    private String ipCountryCode;


    @Schema(description = "设备国家代码")
    private String deviceCountryCode;
} 