package me.supernova.dashboard.model.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/2 09:53
 * @desc
 */
@Data
public class OneTimeProductNotification {
    /**
     * 此通知的版本。最初，此值为“1.0”。此版本与其他版本字段不同。
     */
    private String version;
    /**
     * 订阅的 notificationType
     */
    private Integer notificationType;
    /**
     * 购买订阅时向用户设备提供的令牌。
     */
    private String purchaseToken;
    private String sku;
}
