package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 游戏配置信息
 */
@Data
public class GameConfig {

    /**
     * 间隔时间(秒)
     */
    @JsonProperty("PlayIntersInterval")
    private Long playIntersInterval;

    /**
     * 首次播放关卡
     */
    @JsonProperty("FirstNoIntersCount")
    private Integer firstNoIntersCount;

    /**
     * 间隔关卡数
     */
    @JsonProperty("PlayIntersIntervalLevel")
    private Integer playIntersIntervalLevel;

    /**
     * 关卡数据
     */
    @JsonProperty("LevelOrders")
    private List<LevelOrder> levelOrders;

} 