package me.supernova.dashboard.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改用户去广告状态请求BO
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
@Schema(description = "修改用户去广告状态请求BO")
public class UpdateUserNoAdsBO {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 去广告状态
     */
    @Schema(description = "去广告状态", required = true)
    @NotNull(message = "去广告状态不能为空")
    private Boolean noAds;
}
