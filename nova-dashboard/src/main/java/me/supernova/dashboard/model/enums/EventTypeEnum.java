package me.supernova.dashboard.model.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 *
 */
@Schema(description = "事件类型枚举")
@Getter
public enum EventTypeEnum {
    SERVER_EVENT("SERVER_EVENT", "服务器事件"),
    GENERAL_EVENT("GENERAL_EVENT", "通用事件"),
    AD_EVENT("AD_EVENT", "广告事件"),
    /**
     * 2025 年 6 月 20 日
     * 客户端没时间接入PURCHASE_EVENT
     * 改为后端使用SERVER_EVENT打点
     * 如果后续有改动,请修改这处注释
     */
    PURCHASE_EVENT("PURCHASE_EVENT", "购买事件"),
    SESSION_EVENT("SESSION_EVENT", "会话事件,事件名称为: login,logout,heartbeat"),
    RESOURCE_EVENT("RESOURCE_EVENT", "资源事件"),
    USAGE_DURATION_EVENT("USAGE_DURATION_EVENT", "使用时长事件,事件名称为: usage_duration"),
    UNIQUE_EVENT("UNIQUE_EVENT", "唯一事件,按照用户去重,只保留最后一次的 value"),
    ;

    private final String code;
    private final String description;

    EventTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}