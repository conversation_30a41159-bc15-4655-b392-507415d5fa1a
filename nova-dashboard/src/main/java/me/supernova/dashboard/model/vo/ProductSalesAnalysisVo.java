package me.supernova.dashboard.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品销售数据分析响应对象
 */
@Data
@Builder
@Schema(description = "商品销售数据分析响应")
public class ProductSalesAnalysisVo {

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品ID")
    private String productId;

    @Schema(description = "SKU编码")
    private String sku;

    @Schema(description = "单价($)")
    private BigDecimal unitPrice;

    @Schema(description = "购买用户数")
    private Integer buyerCount;

    @Schema(description = "购买次数")
    private Integer purchaseCount;

    @Schema(description = "新用户购买数")
    private Integer newBuyerCount;

    @Schema(description = "新用户购买次数")
    private Integer newUserPurchaseCount;

    @Schema(description = "总金额($)")
    private BigDecimal totalAmount;

    @Schema(description = "销量占比(%)")
    private BigDecimal salesRatio;
}
