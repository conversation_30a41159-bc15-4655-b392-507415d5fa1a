package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户注册/登录响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户注册/登录响应")
public class UserRegisterResponse {

    @Schema(description = "用户ID", example = "123456")
    @JsonProperty("UserId")
    private Long userId;

    @Schema(description = "是否无广告用户", example = "false")
    @JsonProperty("NoAds")
    private Boolean noAds;

    @Schema(description = "AB测试分组", example = "{\"levelSetting\":\"A\"}")
    @JsonProperty("AbTestGroup")
    private AbtestGroupVo abTestGroup;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "AB测试分组")
    public static class AbtestGroupVo {
        @Schema(description = "等级设置", example = "A")
        @JsonProperty("levelSetting")
        private String levelSetting;
    }
}
