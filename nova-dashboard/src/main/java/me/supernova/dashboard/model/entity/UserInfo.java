package me.supernova.dashboard.model.entity;

import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import me.supernova.common.mybatis.core.entity.BaseEntity;
import me.supernova.common.mybatis.helper.JSONTypePgHandler;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Getter
@Setter
@ToString
@TableName(value = "user_info", autoResultMap = true)
@Schema(name = "UserInfo", description = "用户信息表")
public class UserInfo extends BaseEntity {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    /**
     * 游戏名称
     */
    @Schema(description = "游戏名称")
    private String gameName;

    /**
     * 游戏版本
     */
    @Schema(description = "游戏版本")
    private String gameVersion;

    /**
     * 国家码(ISO 3166-1)
     */
    @Schema(description = "国家码(ISO 3166-1)")
    private String deviceCountryCode;

    /**
     * 国家码(ISO 3166-1)
     */
    @Schema(description = "ip对应的国家码(ISO 3166-1)")
    private String ipCountryCode;

    /**
     * 语言码
     */
    @Schema(description = "语言码")
    private String languageCode;

    /**
     * 时区
     */
    @Schema(description = "时区")
    private Integer timezoneUtc;

    /**
     * 设备型号
     */
    @Schema(description = "设备型号")
    private String deviceModel;

    /**
     * 制造商
     */
    @Schema(description = "制造商")
    private String manufacturer;

    /**
     * 系统版本
     */
    @Schema(description = "系统版本")
    private String osVersion;

    /**
     * 运营商
     */
    @Schema(description = "运营商")
    private String telecomOperators;

    /**
     * 分辨率宽度
     */
    @Schema(description = "分辨率")
    private String resolution;
    /**
     * 设备类型(Android/iOS)
     */
    @Schema(description = "设备类型(Android/iOS)")
    private String platform;

    /**
     * 包名
     */
    @Schema(description = "包名")
    private String packageName;

    /**
     * 下载渠道
     */
    @Schema(description = "下载渠道")
    private String store;

    /**
     * 最近版本
     */
    @Schema(description = "最近版本")
    private String lastVersion;

    /**
     * 付费总金额
     */
    @Schema(description = "付费总金额")
    private BigDecimal totalPaymentAmount;

    /**
     * 付费次数
     */
    @Schema(description = "付费次数")
    private Integer paymentCount;

    /**
     * ip
     */
    @Schema(description = "ip")
    private String ip;


    /**
     * 最后一次登录 ip
     */
    @Schema(description = "last_ip")
    private String lastIp;

    /**
     * 设备像素比
     */
    @Schema(description = "设备像素比")
    private BigDecimal dpr;

    /**
     * iOS设备标识符
     */
    @Schema(description = "iOS设备标识符")
    private String idfv;

    /**
     * iOS广告标识符
     */
    @Schema(description = "iOS广告标识符")
    private String idfa;

    /**
     * Google Play Services广告ID
     */
    @Schema(description = "Google Play Services广告ID")
    private String gpsAdid;

    private String adjustAdid;

    private String uuid;

    /**
     * 是否拥有免广告权益
     */
    @Schema(description = "是否拥有免广告权益")
    private Boolean noAds;

    /**
     * 最近登录时间
     */
    @Schema(description = "最近登录时间")
    private LocalDateTime lastLoginTime;

    /**
     * 最近登录时间-UTC
     */
    @Schema(description = "最近登录时间-UTC")
    private LocalDateTime lastLoginTimeUtc;

    /**
     * 注册时间
     */
    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

    /**
     * 注册时间-UTC
     */
    @Schema(description = "注册时间-UTC")
    private LocalDateTime registerTimeUtc;


    @Schema(description = "ABTest 分组")
    @TableField(value = "ab_test_group", typeHandler = JSONTypePgHandler.class)
    private Dict abTestGroup;
}
