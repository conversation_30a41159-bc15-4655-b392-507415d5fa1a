package me.supernova.dashboard.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 游戏配置版本信息VO
 */
@Data
@Schema(description = "游戏配置版本信息")
public class VersionInfo {

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "状态（active/inactive）")
    private String status;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "上传者")
    private String uploader;

    public VersionInfo() {
    }

    public VersionInfo(String version, LocalDateTime uploadTime, String status) {
        this.version = version;
        this.uploadTime = uploadTime;
        this.status = status;
    }

    public VersionInfo(String version, LocalDateTime uploadTime, String status, String fileName) {
        this.version = version;
        this.uploadTime = uploadTime;
        this.status = status;
        this.fileName = fileName;
    }
}
