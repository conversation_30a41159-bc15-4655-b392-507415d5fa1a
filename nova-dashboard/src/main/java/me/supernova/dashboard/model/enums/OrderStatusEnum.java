package me.supernova.dashboard.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    CREATED("created", "已创建"),
    VERIFYING("verifying", "验证中"),
    PAID("paid", "已支付"),
    FAILED("failed", "支付失败"),
    REFUND("refund", "已退款");


    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 订单状态枚举
     */
    public static OrderStatusEnum getByCode(String code) {
        for (OrderStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 