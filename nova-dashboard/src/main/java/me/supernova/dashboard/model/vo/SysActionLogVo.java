package me.supernova.dashboard.model.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import me.supernova.common.translation.annotation.Translation;
import me.supernova.dashboard.model.constant.DictConstant;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统日志视图对象
 *
 * <AUTHOR>
 */
@Data
public class SysActionLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "日志编号")
    private Long id;

    /**
     * 创建者
     */
    @Translation(type = DictConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    @ExcelProperty(value = "用户")
    private String username;

    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 链路ID
     */
    @ExcelProperty(value = "链路ID")
    private String traceId;

    /**
     * 日志描述
     */
    @ExcelProperty(value = "日志描述")
    private String description;

    /**
     * 所属模块
     */
    @ExcelProperty(value = "所属模块")
    private String module;

    /**
     * 请求路径
     */
    @ExcelProperty(value = "请求路径")
    private String requestPath;

    /**
     * 请求方式
     */
    @ExcelProperty(value = "请求方式")
    private String requestMethod;

    /**
     * 请求头
     */
    private Object requestHeaders;

    /**
     * 请求头
     */
    private Object requestParam;


    /**
     * 请求体
     */
    private Object requestBody;

    /**
     * 状态码
     */
    @ExcelProperty(value = "状态码")
    private Integer statusCode;

    /**
     * 响应头
     */
    private Object responseHeaders;

    /**
     * 响应体
     */
    private Object responseBody;

    /**
     * 耗时（ms）
     */
    @ExcelProperty(value = "耗时（ms）")
    private Long timeTaken;

    /**
     * IP
     */
    @ExcelProperty(value = "操作IP")
    private String ip;

    /**
     * IP归属地
     */
    @ExcelProperty(value = "IP归属地")
    private String address;

    /**
     * 浏览器
     */
    @ExcelProperty(value = "浏览器")
    private String browser;

    /**
     * 操作系统
     */
    @ExcelProperty(value = "操作系统")
    private String os;

    /**
     * 状态（1：成功；2：失败）
     */
    @ExcelProperty(value = "状态")
    private Short status;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errorMsg;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "操作时间")
    private LocalDateTime createTime;
}