package me.supernova.dashboard.model.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/20 10:07
 * @desc
 */
@Data
public class VoidedPurchaseNotification {
    /**
     * 与作废的购买交易关联的令牌。当有新的购买交易发生时，系统会向开发者提供此信息。
     */
    private String purchaseToken;
    /**
     * 与作废的交易关联的唯一订单 ID。对于一次性购买，此字段代表了为这笔购买交易生成的唯一订单 ID。对于自动续订型订阅，系统会为每笔续订交易生成一个新的订单 ID。
     */
    private String orderId;
    /**
     * 作废的购买交易的 productType 可以具有以下值：
     * (1) PRODUCT_TYPE_SUBSCRIPTION - 订阅购买交易已作废。
     * (2) PRODUCT_TYPE_ONE_TIME - 一次性购买交易已作废。
     */
    private Integer productType;
}
