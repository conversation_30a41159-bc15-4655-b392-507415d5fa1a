package me.supernova.dashboard.model.vo;

import cn.hutool.core.lang.Dict;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import me.supernova.dashboard.model.enums.EventTypeEnum;

import java.math.BigDecimal;


/**
 * 事件上报请求参数
 */
@Data
public class EventLogVo {

    /**
     * 客户端信息
     */
    @Schema(description = "客户端信息")
    @NotNull(message = "客户端信息不能为空")
    @Valid
    @JsonProperty("ClientInfo")
    private ClientInfoVo clientInfo;

    /**
     * 事件ID
     */
    @Schema(description = "事件ID")
    @NotNull(message = "事件ID不能为空")
    @JsonProperty("EventId")
    private String eventId;

    /**
     * 事件类型(generalEvent/adEvent/purchaseEvent/sessionEvent/resourceEvent)
     */
    @Schema(description = "事件类型(generalEvent/adEvent/purchaseEvent/sessionEvent/resourceEvent)")
    @NotNull(message = "事件类型不能为空")
    @JsonProperty("EventType")
    private EventTypeEnum eventType;

    /**
     * 事件名称
     */
    @Schema(description = "事件名称")
    @NotNull(message = "事件名称不能为空")
    @JsonProperty("EventName")
    private String eventName;

    /**
     * 事件数值(如付费金额)
     */
    @Schema(description = "事件数值(如付费金额)")
    @JsonProperty("EventValue")
    private BigDecimal eventValue;

    /**
     * 事件参数
     */
    @Schema(description = "事件参数")
    @JsonProperty("EventParams")
    private Dict eventParams;

    /**
     * 用户ID
     */
    @Hidden
    private Long userId;


} 