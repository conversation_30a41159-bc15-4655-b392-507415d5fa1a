package me.supernova.dashboard.model.dto;

import lombok.Data;

import java.time.LocalDate;

/**
 * 用户留存指标DTO
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
public class UserRetentionMetricsDTO {
    private LocalDate date;
    /**
     * 活跃用户数
     */
    private int activeUsers;

    /**
     * 第1天留存用户数
     */
    private int day1RetainedUsers;

    /**
     * 第2天留存用户数
     */
    private int day2RetainedUsers;

    /**
     * 第3天留存用户数
     */
    private int day3RetainedUsers;

    /**
     * 第4天留存用户数
     */
    private int day4RetainedUsers;

    /**
     * 第5天留存用户数
     */
    private int day5RetainedUsers;

    /**
     * 第6天留存用户数
     */
    private int day6RetainedUsers;

    /**
     * 第7天留存用户数
     */
    private int day7RetainedUsers;

    /**
     * 第8天留存用户数
     */
    private int day8RetainedUsers;

    /**
     * 第9天留存用户数
     */
    private int day9RetainedUsers;
} 