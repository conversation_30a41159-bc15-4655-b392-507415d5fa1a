package me.supernova.dashboard.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import me.supernova.common.mybatis.core.entity.BaseEntity;
import me.supernova.common.mybatis.helper.JSONTypePgHandler;
import me.supernova.dashboard.model.enums.PaymentEnvironment;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Getter
@Setter
@ToString
@Schema(name = "Orders", description = "订单表")
public class Orders extends BaseEntity {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号/内部订单号")
    private String orderNo;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 游戏名称
     */
    @Schema(description = "游戏名称")
    private String gameName;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private String productId;

    /**
     * 购买令牌
     */
    @Schema(description = "购买令牌")
    @TableField(typeHandler = JSONTypePgHandler.class)
    private Object purchaseToken;

    /**
     * 购买令牌
     */
    @Schema(description = "购买令牌(未解析)")
    private String purchaseTokenRaw;

    @Schema(description = "国家代码")
    private String deviceCountryCode;

    @Schema(description = "国家代码")
    private String ipCountryCode;

    /**
     * 订单金额
     */
    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    /**
     * 订单状态(created/verifying/paid/failed/refund)
     */
    @Schema(description = "订单状态(created/verifying/paid/failed/refund)")
    private String orderStatus;

    /**
     * 支付状态,用户是否已经支付(其实就是客户端是否请求过校验接口)
     * 1.0.1版本不需要支付状态,写死为 true 就行,之后需要
     */
    @Schema(description = "支付状态")
    private Boolean paid;

    /**
     * 服务端校验状态
     */
    @Schema(description = "服务端校验状态")
    private Boolean verified;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式")
    private String paymentMethod;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    private LocalDateTime paymentTime;

    /**
     * 交易ID
     */
    @Schema(description = "交易ID/平台订单号")
    private String transactionId;

    @Schema(description = "退款时间")
    private LocalDateTime refundTime;

    /**
     * 支付环境
     */
    @Schema(description = "支付环境")
    private PaymentEnvironment paymentEnvironment;
}
