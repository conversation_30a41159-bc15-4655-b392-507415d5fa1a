package me.supernova.dashboard.model.vo;

import lombok.Data;

/**
 * 飞书用户信息VO
 */
@Data
public class LarkUserInfoVo {

    /**
     * 飞书用户唯一标识
     */
    private String openId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户英文名
     */
    private String enName;

    /**
     * 用户头像URL
     */
    private String avatarUrl;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户性别（0-保密，1-男，2-女）
     */
    private Integer gender;

    /**
     * 用户状态
     */
    private Boolean isActivated;
}