package me.supernova.dashboard.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 后台管理系统关卡配置VO
 * 与LevelConfigVo不同，此类不包含@JsonProperty注解，用于后台管理系统的请求和响应
 */
@Data
@Schema(description = "后台管理系统关卡数据")
public class AdminLevelConfigVo implements Serializable {

    /**
     * 关卡唯一标识，自增主键
     */
    @Schema(description = "关卡唯一标识，自增主键")
    private Integer id;

    /**
     * AB测试的组
     */
    @Schema(description = "AB测试的组")
    private String abGroup;


    @Schema(description = "关卡ID")
    private Integer levelId;

    /**
     * 关卡类型，不同类型代表不同的关卡分类
     */
    @Schema(description = "关卡类型，不同类型代表不同的关卡分类")
    private Integer levelType;

    /**
     * 关卡名称
     */
    @Schema(description = "关卡名称")
    private String levelName;

    /**
     * 关卡优先级，用于排序或解锁顺序
     */
    @Schema(description = "关卡优先级，用于排序或解锁顺序")
    private Integer levelPriority;

    /**
     * 关卡解锁条件，描述解锁该关卡所需满足的条件
     */
    @Schema(description = "关卡解锁条件，描述解锁该关卡所需满足的条件")
    private String unlockConditions;

    /**
     * 关卡解锁条件数组，用于前端展示
     */
    @Schema(description = "关卡解锁条件数组，用于前端展示")
    private List<String> unlockConditionsArray;

    /**
     * 关卡需求数量，用于表示通过关卡所需达到的条件数量
     */
    @Schema(description = "关卡需求数量，用于表示通过关卡所需达到的条件数量")
    private Integer requiredConditionCount;

    /**
     * 关卡是否启用，true为启用，false为未启用
     */
    @Schema(description = "关卡是否启用，true为启用，false为未启用")
    private Boolean enabled;

    /**
     * 是否启用关卡提示内容，true为启用，false为未启用
     */
    @Schema(description = "是否启用关卡提示内容，true为启用，false为未启用")
    private Boolean hintEnabled;

    /**
     * 关卡提示内容（中文）
     */
    @Schema(description = "关卡提示内容（中文）")
    private String hintContentZh;

    /**
     * 关卡提示内容（英文）
     */
    @Schema(description = "关卡提示内容（英文）")
    private String hintContentEn;

    /**
     * 关卡限时
     */
    @Schema(description = "关卡限时")
    private Integer timeLimitSeconds;
}
