package me.supernova.dashboard.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import me.supernova.dashboard.model.vo.ClientInfoVo;

@Data
public class RankDto {

    @Schema(description = "分数")
    @NotNull
    @JsonProperty("Score")
    private Long score;

    @NotBlank
    @Schema(description = "国家码")
    @JsonProperty("CountryCode")
    private String countryCode;

    @NotBlank
    @Schema(description = "昵称")
    @JsonProperty("Nickname")
    private String nickname;

    @NotBlank
    @Schema(description = "头像")
    @JsonProperty("Avatar")
    private String avatar;

    @Schema(description = "客户端信息")
    @NotNull(message = "客户端信息不能为空")
    @JsonProperty("ClientInfo")
    private ClientInfoVo clientInfo;
}