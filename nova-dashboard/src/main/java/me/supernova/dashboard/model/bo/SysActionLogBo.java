package me.supernova.dashboard.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.supernova.common.mybatis.core.entity.BaseEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统日志业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysActionLogBo extends BaseEntity {

    /**
     * ID
     */
    @Schema(description = "ID")
    private Long id;

    /**
     * 创建id
     */
    @Schema(description = "创建id")
    private List<Long> createByIds;

    /**
     * 链路ID
     */
    @Schema(description = "链路ID")
    private String traceId;

    /**
     * 日志描述
     */
    @Schema(description = "日志描述")
    private String description;

    /**
     * 所属模块
     */
    @Schema(description = "所属模块")
    private String module;

    /**
     * 请求路径
     */
    private String requestPath;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * IP
     */
    private String ip;

    /**
     * IP归属地
     */
    private String address;

    /**
     * 状态（1：成功；2：失败）
     */
    private Short status;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}