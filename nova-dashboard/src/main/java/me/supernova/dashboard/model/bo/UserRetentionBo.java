package me.supernova.dashboard.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@Schema(description = "用户留存分析查询参数")
public class UserRetentionBo {

    @Schema(description = "查询类型(1:新增用户留存 2:活跃用户留存)")
    private Integer queryType;

    @Schema(description = "开始日期")
    private LocalDate beginDate;

    @Schema(description = "结束日期")
    private LocalDate endDate;

    @Schema(description = "首次版本号")
    private String firstVersion;

    @Schema(description = "最近版本号")
    private String lastVersion;

    @Schema(description = "游戏名称")
    private String gameName;

    @Schema(description = "商店")
    private String store;

    @Schema(description = "平台(Android/iOS)")
    private String platform;

    @Schema(description = "IP国家地区")
    private List<String> ipCountryCode;

    @Schema(description = "时区")
    private Integer timezone;
} 