package me.supernova.dashboard.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 谷歌支付通知消息
 */
@Data
@Schema(description = "谷歌支付通知消息")
public class GooglePayNotificationMessage {

    @Schema(description = "消息内容")
    private MessageDTO message;

    @Data
    @Schema(description = "消息内容")
    public static class MessageDTO {
        @Schema(description = "属性")
        private AttributesDTO attributes;

        @Schema(description = "数据")
        private String data;

        @Schema(description = "消息ID")
        private String messageId;
    }

    @Data
    @Schema(description = "属性")
    public static class AttributesDTO {
        @Schema(description = "键")
        private String key;
    }
} 