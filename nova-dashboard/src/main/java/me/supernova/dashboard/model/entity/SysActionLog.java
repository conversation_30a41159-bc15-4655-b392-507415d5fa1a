package me.supernova.dashboard.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import me.supernova.common.mybatis.core.entity.BaseEntity;
import me.supernova.common.mybatis.helper.JSONTypePgHandler;

/**
 * <p>
 * 系统日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Getter
@Setter
@TableName("sys_action_log")
@Schema(name = "SysActionLog", description = "系统日志表")
public class SysActionLog extends BaseEntity {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "链路ID")
    private String traceId;

    @Schema(description = "日志描述")
    private String description;

    @Schema(description = "所属模块")
    private String module;

    @Schema(description = "请求HOST")
    private String requestHost;

    @Schema(description = "请求路径")
    private String requestPath;

    @Schema(description = "请求方式")
    private String requestMethod;

    @Schema(description = "请求头")
    @TableField(typeHandler = JSONTypePgHandler.class)
    private Object requestHeaders;

    @Schema(description = "请求参数")
    @TableField(typeHandler = JSONTypePgHandler.class)
    private Object requestParam;

    @Schema(description = "请求体")
    @TableField(typeHandler = JSONTypePgHandler.class)
    private Object requestBody;

    @Schema(description = "状态码")
    private Integer statusCode;

    @Schema(description = "响应头")
    @TableField(typeHandler = JSONTypePgHandler.class)
    private Object responseHeaders;

    @Schema(description = "响应体")
    @TableField(typeHandler = JSONTypePgHandler.class)
    private Object responseBody;

    @Schema(description = "耗时（ms）")
    private Long timeTaken;

    @Schema(description = "IP")
    private String ip;

    @Schema(description = "IP归属地")
    private String address;

    @Schema(description = "浏览器")
    private String browser;

    @Schema(description = "操作系统")
    private String os;

    @Schema(description = "状态（1：成功；2：失败）")
    private Integer status;

    @Schema(description = "错误信息")
    private String errorMsg;
}
