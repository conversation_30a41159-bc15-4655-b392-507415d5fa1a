package me.supernova.dashboard.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class BaseAnalysisBo {
    @Schema(description = "查询开始日期")
    private LocalDate startDate;

    @Schema(description = "查询结束日期")
    private LocalDate endDate;

    @Schema(description = "首次版本号")
    private String firstVersion;

    @Schema(description = "最近版本号")
    private String latestVersion;

    @Schema(description = "游戏名")
    private String gameName;

    @Schema(description = "商店")
    private String store;

    @Schema(description = "平台(Android/iOS)")
    private String platform;

    @Schema(description = "国家地区")
    private List<String> countryCode;

    @Schema(description = "时区")
    private Integer timezone;

    @Schema(description = "abTest分组")
    private String abTestGameConfigGroup;
}
