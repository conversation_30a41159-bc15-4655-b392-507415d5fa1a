package me.supernova.dashboard.model.constant;

import lombok.Getter;
import me.supernova.common.core.constant.ErrorCode;

/**
 * 系统错误码枚举
 * <p>
 * 错误码分配规则：
 * 1. 系统级错误码 (1000-1999)
 * - 1000-1099: 基础系统错误
 * - 1100-1199: 配置相关错误
 * - 1200-1299: 资源相关错误
 * - 1300-1399: 系统服务错误
 * <p>
 * 2. 认证相关错误码 (2000-2999)
 * - 2000-2099: 基础认证错误
 * - 2100-2199: 权限相关错误
 * - 2200-2299: 第三方认证错误（如飞书、企业微信等）
 * - 2300-2399: 会话相关错误
 * <p>
 * 3. 业务相关错误码 (3000-3999)
 * - 3000-3099: 用户相关错误
 * - 3100-3199: 组织架构相关错误
 * - 3200-3299: 业务规则错误
 * - 3300-3399: 业务状态错误
 * <p>
 * 4. 数据相关错误码 (4000-4999)
 * - 4000-4099: 数据验证错误
 * - 4100-4199: 数据操作错误
 * - 4200-4299: 数据同步错误
 * - 4300-4399: 数据转换错误
 * <p>
 * 5. 第三方服务错误码 (5000-5999)
 * - 5000-5099: 第三方服务通用错误
 * - 5100-5199: 飞书服务错误
 * - 5200-5299: 企业微信服务错误
 * - 5300-5399: 其他第三方服务错误
 */
@Getter
public enum ErrorEnum implements ErrorCode {

    // 系统级错误码 (1000-1999)


    // 认证相关错误码 (2000-2999)
    FEISHU_ACCOUNT_NOT_BOUND(2000, "无法根据飞书账号找到对应的用户，请确认是否绑定过飞书"),

    // 业务相关错误码 (3000-3999)


    // 数据相关错误码 (4000-4999)


    // 第三方服务错误码 (5000-5999)


    ;

    private final int code;
    private final String message;

    ErrorEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
