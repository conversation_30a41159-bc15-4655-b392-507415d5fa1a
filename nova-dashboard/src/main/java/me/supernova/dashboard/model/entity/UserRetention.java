package me.supernova.dashboard.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户留存记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Getter
@Setter
@ToString
@TableName("user_retention")
@Schema(name = "UserRetention", description = "用户留存记录表")
public class UserRetention {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    private Integer timezone;

    /**
     * 记录类型
     */
    @Schema(description = "记录类型")
    private String recordType;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private LocalDate startDate;

    /**
     * 第1天留存
     */
    @Schema(description = "第1天留存")
    private Boolean day1Retained;

    /**
     * 第2天留存
     */
    @Schema(description = "第2天留存")
    private Boolean day2Retained;

    /**
     * 第3天留存
     */
    @Schema(description = "第3天留存")
    private Boolean day3Retained;

    /**
     * 第4天留存
     */
    @Schema(description = "第4天留存")
    private Boolean day4Retained;

    /**
     * 第5天留存
     */
    @Schema(description = "第5天留存")
    private Boolean day5Retained;

    /**
     * 第6天留存
     */
    @Schema(description = "第6天留存")
    private Boolean day6Retained;

    /**
     * 第7天留存
     */
    @Schema(description = "第7天留存")
    private Boolean day7Retained;

    /**
     * 第8天留存
     */
    @Schema(description = "第8天留存")
    private Boolean day8Retained;

    /**
     * 第9天留存
     */
    @Schema(description = "第9天留存")
    private Boolean day9Retained;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
