package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RegisterLoginVo extends OldClientInfoVo {

    @Schema(description = "客户端信息")
    @JsonProperty("ClientInfo")
    private ClientInfoVo clientInfo;
}
