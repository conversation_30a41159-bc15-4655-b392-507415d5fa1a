package me.supernova.dashboard.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "支付环境枚举")
public enum PaymentEnvironment {

    @Schema(description = "校验中")
    VERIFYING(0, "校验中"),

    @Schema(description = "沙盒支付")
    SANDBOX(1, "沙盒支付"),

    @Schema(description = "生产环境支付")
    PRODUCTION(2, "生产环境支付");

    @EnumValue
    private final Integer code;
    private final String desc;

    PaymentEnvironment(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
} 