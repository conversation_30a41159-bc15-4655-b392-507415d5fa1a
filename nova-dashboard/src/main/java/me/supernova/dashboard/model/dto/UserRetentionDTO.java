package me.supernova.dashboard.model.dto;

import lombok.Data;

import java.time.LocalDate;

/**
 * 用户留存数据DTO
 */
@Data
public class UserRetentionDTO {
    private Long userId;
    private LocalDate startDate;
    private int day1Retained;
    private int day2Retained;
    private int day3Retained;
    private int day4Retained;
    private int day5Retained;
    private int day6Retained;
    private int day7Retained;
    private int day8Retained;
    private int day9Retained;
} 