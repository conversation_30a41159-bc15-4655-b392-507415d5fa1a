package me.supernova.dashboard.model.bo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import me.supernova.common.mybatis.core.entity.BaseEntity;

import java.time.LocalDateTime;

/**
 * 系统访问记录业务对象 sys_loginInfo
 *
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class SysLoginInfoBo extends BaseEntity {

    /**
     * 访问ID
     */
    private Long infoId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 登录状态（0成功 1失败）
     */
    private String status;

    /**
     * 提示消息
     */
    private String msg;

    /**
     * 访问时间
     */
    private LocalDateTime loginTime;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
