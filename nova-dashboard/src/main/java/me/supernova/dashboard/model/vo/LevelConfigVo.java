package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "关卡数据")
public class LevelConfigVo implements Serializable {

    /**
     * 关卡唯一标识，自增主键
     */
    @Schema(description = "关卡唯一标识，自增主键")
    @JsonIgnore
    private Integer id;

    /**
     * AB测试的组
     */
    @Schema(description = "AB测试的组")
    @JsonIgnore
    private String abGroup;

    /**
     * 关卡 ID
     */
    @Schema(description = "关卡 ID")
    @JsonProperty("level_id")
    private Integer levelId;

    /**
     * 关卡类型，不同类型代表不同的关卡分类
     */
    @Schema(description = "关卡类型，不同类型代表不同的关卡分类")
    @JsonProperty("mode_type")
    private Integer levelType;

    /**
     * 关卡名称
     */
    @Schema(description = "关卡名称")
    @JsonIgnore
    private String levelName;

    /**
     * 关卡优先级，用于排序或解锁顺序
     */
    @Schema(description = "关卡优先级，用于排序或解锁顺序")
    @JsonProperty("level_order")
    private Integer levelPriority;


    @JsonIgnore
    private String unlockConditions;

    /**
     * 关卡解锁条件，描述解锁该关卡所需满足的条件
     */
    @Schema(description = "关卡解锁条件，描述解锁该关卡所需满足的条件")
    @JsonProperty("unlock_conditions")
    private List<Integer> unlockConditionsArray;


    /**
     * 关卡需求数量，用于表示通过关卡所需达到的条件数量
     */
    @Schema(description = "关卡需求数量，用于表示通过关卡所需达到的条件数量")
    @JsonProperty("lv_pass_params")
    private Integer requiredConditionCount;

    /**
     * 关卡是否启用，true为启用，false为未启用
     */
    @Schema(description = "关卡是否启用，true为启用，false为未启用")
    @JsonProperty("level_switch")
    private Boolean enabled;

    /**
     * 是否启用关卡提示内容，true为启用，false为未启用
     */
    @Schema(description = "是否启用关卡提示内容，true为启用，false为未启用")
    @JsonProperty("hint_txt_switch")
    private Boolean hintEnabled;

    /**
     * 关卡提示内容（中文）
     */
    @JsonIgnore
    @JsonProperty("hint_txt_zh")
    private String hintContentZh;

    /**
     * 关卡提示内容（英文）
     */
    @Schema(description = "关卡提示内容（英文）")
    @JsonIgnore
    private String hintContentEn;

    /**
     * 关卡限时
     */
    @JsonProperty("time_limited")
    @Schema(description = "关卡限时")
    private Integer timeLimitSeconds;
}
