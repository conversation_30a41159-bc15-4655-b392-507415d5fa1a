package me.supernova.dashboard.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户排名表，存储用户分数及更新时间
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Getter
@Setter
@ToString
@TableName("user_rank")
@Schema(name = "UserRank", description = "用户排名表，存储用户分数及更新时间")
public class UserRank implements Serializable {

    /**
     * 主键，自增ID
     */
    @Schema(description = "主键，自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 用户分数
     */
    @Schema(description = "用户分数")
    private Long score;

    /**
     * 记录创建时间
     */
    @Schema(description = "记录创建时间")
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 国家码
     */
    @Schema(description = "国家码")
    private String countryCode;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;

    /**
     * 是否是机器人
     */
    @Schema(description = "是否是机器人")
    private Boolean robot;
}
