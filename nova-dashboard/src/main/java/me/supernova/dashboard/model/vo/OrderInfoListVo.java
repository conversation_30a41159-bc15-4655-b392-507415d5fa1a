package me.supernova.dashboard.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import me.supernova.dashboard.model.enums.PaymentEnvironment;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单列表展示对象
 */
@Data
@Schema(description = "订单列表展示对象")
public class OrderInfoListVo {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "订单编号/内部订单号")
    private String orderNo;

    @Schema(description = "交易ID/平台订单号")
    private String transactionId;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "游戏名称")
    private String gameName;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "购买令牌")
    private String purchaseToken;

    @Schema(description = "国家代码")
    private String deviceCountryCode;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @Schema(description = "订单状态(created/verifying/paid/failed)")
    private String orderStatus;

    @Schema(description = "支付方式")
    private String paymentMethod;

    @Schema(description = "支付时间")
    private LocalDateTime paymentTime;

    @Schema(description = "支付环境")
    private PaymentEnvironment paymentEnvironment;

    @Schema(description = "退款时间")
    private LocalDateTime refundTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "支付状态")
    private Boolean paid;

    @Schema(description = "服务端校验状态")
    private Boolean verified;
} 