package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import me.supernova.common.core.utils.ServletUtils;
import me.supernova.common.ip.util.AddressUtils;
import me.supernova.dashboard.service.UserInfoService;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

/**
 * 设备信息VO
 */
@Data
public class ClientInfoVo {

    @Schema(description = "AdjustAdid")
    @JsonProperty("AdjustAdid")
    private String adjustAdid;

    @Schema(description = "Idfv")
    @JsonProperty("Idfv")
    private String idfv;

    @Schema(description = "Idfa")
    @JsonProperty("Idfa")
    private String idfa;

    @Schema(description = "GpsAdid")
    @JsonProperty("GpsAdid")
    private String gpsAdid;

    @Schema(description = "Uuid")
    @NotBlank(message = "UUID不能为空")
    @Length(max = 32, message = "UUID不能超过32个字符")
    @JsonProperty("Uuid")
    private String uuid;

    @Schema(description = "游戏名称")
    @NotBlank(message = "游戏名称不能为空")
    @JsonProperty("GameName")
    private String gameName;

    @Schema(description = "游戏版本")
    @NotBlank(message = "游戏版本不能为空")
    @JsonProperty("GameVersion")
    private String gameVersion;

    @Schema(description = "国家码(ISO 3166-1)")
    @JsonProperty("CountryCode")
    private String deviceCountryCode;

    @Schema(description = "语言码")
    @JsonProperty("LanguageCode")
    private String languageCode;

    @Schema(description = "时区")
    @JsonProperty("TimezoneUtc")
    private Integer timezoneUtc;

    @Schema(description = "设备型号")
    @JsonProperty("DeviceModel")
    private String deviceModel;

    @Schema(description = "制造商")
    @JsonProperty("Manufacturer")
    private String manufacturer;

    @Schema(description = "系统版本")
    @JsonProperty("OsVersion")
    private String osVersion;

    @Schema(description = "运营商")
    @JsonProperty("TelecomOperators")
    private String telecomOperators;

    @Schema(description = "分辨率宽度")
    @JsonProperty("Resolution")
    private String resolution;

    @Schema(description = "设备类型(Android/iOS)")
    @NotBlank(message = "设备类型不能为空")
    @JsonProperty("Platform")
    private String platform;

    @Schema(description = "包名")
    @NotBlank(message = "包名不能为空")
    @JsonProperty("PackageName")
    private String packageName;

    @NotBlank(message = "商店参数不能为空!")
    @Schema(description = "下载渠道")
    @JsonProperty("Store")
    private String store;

    @Schema(description = "设备像素比")
    @JsonProperty("Dpr")
    private BigDecimal dpr;


    @Schema(description = "用户 id")
    @JsonProperty("UserId")
    private Long userId;

    public Long getUserId(UserInfoService userInfoService) {
        if (userId == null) {
            userId = userInfoService.getUserId(this);
        }
        return userId;
    }

    /**
     * 这个字段不要删除,为了给 BeanUtil复制属性
     */
    @Hidden
    private String ipCountryCode;

    /**
     * 重写获取国家码的方法,可以让 BeanUtil复制属性的时候获取到正确的国家码
     */
    public String getIpCountryCode() {
        return AddressUtils.getCountryCode(ServletUtils.getCfCountryCode(), ServletUtils.getClientIP());
    }

    /**
     * 这个字段不要删除,为了给 BeanUtil复制属性
     */
    @Hidden
    private String ip;

    /**
     * 这个和上面是一样的
     */
    public String getIp() {
        return ServletUtils.getClientIP();
    }


} 