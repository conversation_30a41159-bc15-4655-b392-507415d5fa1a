package me.supernova.dashboard.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品基础销售数据DTO
 */
@Data
public class ProductBasicSalesDTO {

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 平均单价
     */
    private BigDecimal unitPrice;

    /**
     * 购买用户数（去重）
     */
    private Integer buyerCount;

    /**
     * 购买次数
     */
    private Integer purchaseCount;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;
}
