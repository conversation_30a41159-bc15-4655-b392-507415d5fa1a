package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Excel游戏配置主类
 * 对应Excel文件解析后的完整配置数据结构
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@NoArgsConstructor
public class ExcelGameConfig {

    /**
     * 关卡配置列表
     */
    @JsonProperty("levelConfig")
    private List<LevelConfigItem> levelConfig;

    /**
     * 道具配置列表
     */
    @JsonProperty("itemConfig")
    private List<ItemConfigItem> itemConfig;

    /**
     * 通用配置对象
     */
    @JsonProperty("commonConfig")
    private CommonConfigItem commonConfig;

    /**
     * 收藏品配置列表
     */
    @JsonProperty("collectiblesConfig")
    private List<CollectiblesConfigItem> collectiblesConfig;

    /**
     * 收藏品图片配置列表
     */
    @JsonProperty("collectiblesImageConfig")
    private List<CollectiblesImageConfigItem> collectiblesImageConfig;

    /**
     * 关卡配置项
     */
    @Data
    @NoArgsConstructor
    public static class LevelConfigItem {
        /**
         * 编号
         */
        @JsonProperty("id")
        private Integer id;

        /**
         * 有效性
         */
        @JsonProperty("valid")
        private Integer valid;

        /**
         * 关卡编号
         */
        @JsonProperty("node")
        private Integer node;

        /**
         * 难度标识（ui用）
         */
        @JsonProperty("ishard")
        private String ishard;

        /**
         * 隶属章
         */
        @JsonProperty("chapter")
        private Integer chapter;

        /**
         * 隶属节
         */
        @JsonProperty("chapter_node")
        private Integer chapter_node;

        /**
         * 填色图片路径
         */
        @JsonProperty("pic")
        private String pic;

        /**
         * 关卡颜色枚举
         */
        @JsonProperty("color")
        private List<String> color;

        /**
         * rv卡槽状态（0:锁定/隐藏 1:解锁 2:广告解锁）
         */
        @JsonProperty("slot_state")
        private List<Integer> slot_state;

        /**
         * rv卡槽生成组合
         */
        @JsonProperty("slot")
        private List<Integer> slot;

        /**
         * rv卡槽生成组合切分
         */
        @JsonProperty("slot_cut")
        private List<List<Integer>> slot_cut;

        /**
         * card排序组合1
         */
        @JsonProperty("card_sort_001")
        private List<Integer> card_sort_001;

        /**
         * card组合1数量
         */
        @JsonProperty("card_num_001")
        private List<Integer> card_num_001;

        /**
         * 组合1额外属性
         */
        @JsonProperty("card_extra_001")
        private List<Integer> card_extra_001;

        /**
         * card排序组合2
         */
        @JsonProperty("card_sort_002")
        private List<Integer> card_sort_002;

        /**
         * card组合2数量
         */
        @JsonProperty("card_num_002")
        private List<Integer> card_num_002;

        /**
         * 组合2额外属性
         */
        @JsonProperty("card_extra_002")
        private List<Integer> card_extra_002;

        /**
         * card排序组合3
         */
        @JsonProperty("card_sort_003")
        private List<Integer> card_sort_003;

        /**
         * card组合3数量
         */
        @JsonProperty("card_num_003")
        private List<Integer> card_num_003;

        /**
         * 组合3额外属性
         */
        @JsonProperty("card_extra_003")
        private List<Integer> card_extra_003;

        /**
         * card排序组合4
         */
        @JsonProperty("card_sort_004")
        private List<Integer> card_sort_004;

        /**
         * card组合4数量
         */
        @JsonProperty("card_num_004")
        private List<Integer> card_num_004;

        /**
         * 组合4额外属性
         */
        @JsonProperty("card_extra_004")
        private List<Integer> card_extra_004;

        /**
         * 关卡开启消耗
         */
        @JsonProperty("cost")
        private String cost;

        /**
         * 关卡奖励
         */
        @JsonProperty("reward")
        private String reward;

        /**
         * 广告奖励
         */
        @JsonProperty("ads_reward")
        private String ads_reward;

    }

    /**
     * 道具配置项
     */
    @Data
    @NoArgsConstructor
    public static class ItemConfigItem {
        /**
         * 道具id
         */
        @JsonProperty("id")
        private Integer id;

        /**
         * 有效性
         */
        @JsonProperty("valid")
        private Integer valid;

        /**
         * 道具类型
         */
        @JsonProperty("type")
        private Integer type;

        /**
         * 道具子类
         */
        @JsonProperty("sub")
        private Integer sub;

        /**
         * 关联字段(礼包用)
         */
        @JsonProperty("reward_pool")
        private Integer reward_pool;

        /**
         * 道具名
         */
        @JsonProperty("name")
        private String name;

        /**
         * 道具描述
         */
        @JsonProperty("des")
        private String des;

        /**
         * 使用icon
         */
        @JsonProperty("icon")
        private List<String> icon;

        /**
         * 预留跳转,关联跳转表
         */
        @JsonProperty("jump")
        private List<Integer> jump;
    }

    /**
     * 通用配置项
     */
    @Data
    @NoArgsConstructor
    public static class CommonConfigItem {
        /**
         * 初始金钱
         */
        @JsonProperty("Initial_Money")
        private Integer Initial_Money;

        /**
         * 初始体力
         */
        @JsonProperty("Initial_Power")
        private Integer Initial_Power;

        /**
         * 体力恢复时间
         */
        @JsonProperty("Power_Recovery_Time")
        private Integer Power_Recovery_Time;

        /**
         * 体力恢复值
         */
        @JsonProperty("Power_Recovery_Value")
        private Integer Power_Recovery_Value;

        /**
         * 通用卡槽网格
         */
        @JsonProperty("General_Slot_Grid")
        private List<Integer> General_Slot_Grid;

        /**
         * 通用卡槽消耗
         */
        @JsonProperty("General_Slot_Cost")
        private List<Integer> General_Slot_Cost;

        /**
         * 继续游戏消耗
         */
        @JsonProperty("GoOnCost")
        private Integer GoOnCost;

        /**
         * 道具价值
         */
        @JsonProperty("Item_Value")
        private List<String> Item_Value;

        /**
         * 主线解锁
         */
        @JsonProperty("Main_Unlock")
        private List<String> Main_Unlock;

        /**
         * 收集解锁
         */
        @JsonProperty("Collect_Unlock")
        private List<String> Collect_Unlock;

        /**
         * 额外卡槽
         */
        @JsonProperty("Extra_Slot")
        private List<String> Extra_Slot;

        /**
         * 隐藏卡片
         */
        @JsonProperty("Hide_Card")
        private List<String> Hide_Card;

        /**
         * 额外撤销
         */
        @JsonProperty("Extra_Rv")
        private List<String> Extra_Rv;

        /**
         * 洗牌卡片
         */
        @JsonProperty("Shuffle_Card")
        private List<String> Shuffle_Card;

        /**
         * 锤子道具
         */
        @JsonProperty("Hammer")
        private List<String> Hammer;

        /**
         * 关卡完成奖励率
         */
        @JsonProperty("Level_Finish_Rward_Rates")
        private List<Integer> Level_Finish_Rward_Rates;

        /**
         * 颜色配置001
         */
        @JsonProperty("Color_001")
        private String Color_001;

        /**
         * 颜色配置002
         */
        @JsonProperty("Color_002")
        private String Color_002;

        /**
         * 颜色配置003
         */
        @JsonProperty("Color_003")
        private String Color_003;

        /**
         * 颜色配置004
         */
        @JsonProperty("Color_004")
        private String Color_004;

        /**
         * 颜色配置005
         */
        @JsonProperty("Color_005")
        private String Color_005;

        /**
         * 颜色配置006
         */
        @JsonProperty("Color_006")
        private String Color_006;

        /**
         * 颜色配置007
         */
        @JsonProperty("Color_007")
        private String Color_007;

        /**
         * 颜色配置007
         */
        @JsonProperty("Color_008")
        private String Color_008;

        /**
         * 礼包配置001
         */
        @JsonProperty("Giftbag_001")
        private List<String> Giftbag_001;

        /**
         * 商店道具001
         */
        @JsonProperty("ShopItem_001")
        private Integer ShopItem_001;

        /**
         * 商店道具002
         */
        @JsonProperty("ShopItem_002")
        private Integer ShopItem_002;

        /**
         * 商店道具003
         */
        @JsonProperty("ShopItem_003")
        private Integer ShopItem_003;

        /**
         * 商店道具004
         */
        @JsonProperty("ShopItem_004")
        private Integer ShopItem_004;

        /**
         * 商店道具005
         */
        @JsonProperty("ShopItem_005")
        private Integer ShopItem_005;

        /**
         * 商店道具006
         */
        @JsonProperty("ShopItem_006")
        private Integer ShopItem_006;
    }

    /**
     * 收藏品配置项
     */
    @Data
    @NoArgsConstructor
    public static class CollectiblesConfigItem {
        /**
         * 编号
         */
        @JsonProperty("id")
        private Integer id;

        /**
         * 主题名
         */
        @JsonProperty("theme_name")
        private String theme_name;

        /**
         * 主题图片路径
         */
        @JsonProperty("pic_path")
        private String pic_path;

        /**
         * 主题解锁关卡id
         */
        @JsonProperty("unlock_lvl")
        private Integer unlock_lvl;

        /**
         * 全解锁奖励id
         */
        @JsonProperty("reward_id")
        private List<String> reward_id;
    }

    /**
     * 收藏品图片配置项
     */
    @Data
    @NoArgsConstructor
    public static class CollectiblesImageConfigItem {
        /**
         * 编号
         */
        @JsonProperty("id")
        private Integer id;

        /**
         * 隶属主题
         */
        @JsonProperty("theme")
        private Integer theme;

        /**
         * 内部序号
         */
        @JsonProperty("order")
        private Integer order;

        /**
         * 图片名
         */
        @JsonProperty("image_id")
        private String image_id;

        /**
         * 使用颜色
         */
        @JsonProperty("use_color")
        private List<String> use_color;

        /**
         * 收集需求关卡id
         */
        @JsonProperty("require_level_id")
        private List<String> require_level_id;
    }
}
