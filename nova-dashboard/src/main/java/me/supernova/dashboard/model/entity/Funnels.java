package me.supernova.dashboard.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import me.supernova.common.mybatis.core.entity.BaseEntity;
import me.supernova.common.mybatis.helper.JSONTypePgHandler;
import me.supernova.dashboard.model.bo.BaseAnalysisBo;
import me.supernova.dashboard.model.dto.ParamElementDto;

import java.util.List;

/**
 * <p>
 * 漏斗表，记录各类漏斗信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Getter
@Setter
@ToString
@TableName(value = "funnels", autoResultMap = true)
@Schema(name = "Funnels", description = "漏斗表，记录各类漏斗信息")
public class Funnels extends BaseEntity {

    /**
     * 主键，自增
     */
    @Schema(description = "主键，自增")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 漏斗名称
     */
    @Schema(description = "漏斗名称")
    private String name;

    /**
     * 参数，JSONB 格式
     */
    @TableField(typeHandler = JSONTypePgHandler.class)
    @Schema(description = "参数，JSONB 格式")
    private Param param;

    @Data
    public static class Param {
        
        /**
         * 查询条件
         */
        private BaseAnalysisBo queryBo;

        /**
         * 参数列表
         */
        private List<ParamElementDto> paramList;
    }
}
