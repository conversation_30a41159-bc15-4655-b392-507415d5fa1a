package me.supernova.dashboard.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RankVo {
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 用户分数
     */
    @Schema(description = "用户分数")
    private Long score;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;

    /**
     * 国家码
     */
    @Schema(description = "国家码")
    private String countryCode;

    /**
     * 是否是机器人
     */
    @Schema(description = "是否是机器人")
    private Boolean robot;

    /**
     * 排名
     */
    @Schema(description = "排名")
    private Integer rank;

} 