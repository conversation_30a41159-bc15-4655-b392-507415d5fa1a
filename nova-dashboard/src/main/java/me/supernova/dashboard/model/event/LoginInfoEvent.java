package me.supernova.dashboard.model.event;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 登录事件
 *
 * <AUTHOR> Li
 */

@Data
public class LoginInfoEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户账号
     */
    private String username;

    /**
     * 登录状态 0成功 1失败
     */
    private String status;

    /**
     * 提示消息
     */
    private String message;

    /**
     * ip
     */
    private String ip;

    /**
     * browser
     */
    private String browser;

    /**
     * os
     */
    private String os;

    /**
     * 其他参数
     */
    private Object[] args;

}
