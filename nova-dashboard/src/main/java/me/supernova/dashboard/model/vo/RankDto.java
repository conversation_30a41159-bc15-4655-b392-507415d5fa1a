package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RankDto {

    @Schema(description = "分数")
    @NotNull(message = "分数不能为空")
    @JsonProperty("Score")
    private Integer score;


    @Schema(description = "客户端信息")
    @NotNull(message = "客户端信息不能为空")
    @JsonProperty("ClientInfo")
    private ClientInfoVo clientInfo;
}