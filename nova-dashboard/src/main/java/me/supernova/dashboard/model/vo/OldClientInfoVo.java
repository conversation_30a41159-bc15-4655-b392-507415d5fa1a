package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.Data;

import java.math.BigDecimal;

@Data

public class OldClientInfoVo {

    @Hidden
    @JsonProperty("AdjustAdid")
    private String adjustAdid;

    @Hidden
    @JsonProperty("Idfv")
    private String idfv;

    @Hidden
    @JsonProperty("Idfa")
    private String idfa;

    @Hidden
    @JsonProperty("GpsAdid")
    private String gpsAdid;

    @Hidden
    @JsonProperty("Uuid")
    private String uuid;

    @Hidden
    @JsonProperty("GameName")
    private String gameName;

    @Hidden
    @JsonProperty("GameVersion")
    private String gameVersion;

    @Hidden
    @JsonProperty("CountryCode")
    private String deviceCountryCode;

    @Hidden
    @JsonProperty("LanguageCode")
    private String languageCode;

    @Hidden
    @JsonProperty("TimezoneUtc")
    private Integer timezoneUtc;

    @Hidden
    @JsonProperty("DeviceModel")
    private String deviceModel;

    @Hidden
    @JsonProperty("Manufacturer")
    private String manufacturer;

    @Hidden
    @JsonProperty("OsVersion")
    private String osVersion;

    @Hidden
    @JsonProperty("TelecomOperators")
    private String telecomOperators;

    @Hidden
    @JsonProperty("Resolution")
    private String resolution;

    @Hidden
    @JsonProperty("Platform")
    private String platform;

    @Hidden
    @JsonProperty("PackageName")
    private String packageName;

    @Hidden
    @JsonProperty("Store")
    private String store;

    @Hidden
    @JsonProperty("Dpr")
    private BigDecimal dpr;
} 