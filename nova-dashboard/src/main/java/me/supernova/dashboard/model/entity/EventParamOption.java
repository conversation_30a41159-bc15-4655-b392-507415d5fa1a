package me.supernova.dashboard.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 事件参数选项表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Getter
@Setter
@ToString
@TableName("event_param_option")
@Schema(name = "EventParamOption", description = "事件参数选项表")
public class EventParamOption implements Serializable {

    /**
     * 主键，自增
     */
    @Schema(description = "主键，自增")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 参数名
     */
    @Schema(description = "参数名")
    private String key;

    /**
     * 值
     */
    @Schema(description = "值")
    private String value;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
