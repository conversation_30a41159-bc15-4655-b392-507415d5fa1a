package me.supernova.dashboard.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 积分记录表，存储用户积分及排名信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Getter
@Setter
@ToString
@TableName("robot_rank_record")
@Schema(name = "RobotRankRecord", description = "积分记录表，存储用户积分及排名信息")
public class RobotRankRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一自增 ID
     */
    @Schema(description = "唯一自增 ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一用户 ID
     */
    @Schema(description = "唯一用户 ID")
    private Long uid;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String nickname;

    private String countryCode;

    private String avatar;

    /**
     * 初始分值
     */
    @Schema(description = "初始分值")
    private Long initialScore;

    /**
     * 当前分值
     */
    @Schema(description = "当前分值")
    private Long score;

    /**
     * 最近更新时间，默认当前时间
     */
    @Schema(description = "最近更新时间，默认当前时间")
    private LocalDateTime lastUpdate;

    /**
     * 每天的更新时间(例:8,就是每天早上8点更新 UTC+0时间)
     */
    @Schema(description = "每天的更新时间(例:8,就是每天早上8点更新 UTC+0时间)")
    private Integer timeUpdate;

    /**
     * 空缺阀值
     */
    @Schema(description = "空缺阀值")
    private Integer threshold;

    /**
     * 每日积分增长随机范围
     */
    @Schema(description = "每日积分增长随机范围")
    private Integer advance;

    /**
     * 序列（30天轮换），用整数数组，默认空数组
     */
    @Schema(description = "序列（30天轮换），用整数数组，默认空数组")
    private Integer sequence;

    /**
     * 是否运行，默认关闭
     */
    @Schema(description = "是否运行，默认关闭")
    private Boolean isRunning;
}
