package me.supernova.dashboard.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@Schema(description = "数据概览响应")
public class DataOverviewVo {

    @Schema(description = "日期")
    private LocalDate date;

    @Schema(description = "安装量")
    private Integer installations;

    @Schema(description = "新增用户数")
    private Integer newUsers;

    @Schema(description = "DAU")
    private Integer dau;

    @Schema(description = "次日留存率")
    private BigDecimal retentionRate;

    @Schema(description = "平均使用时长(分钟)")
    private BigDecimal averageUsageTime;

    @Schema(description = "内购收入($)")
    private BigDecimal iapRevenue;

    @Schema(description = "广告收入($)")
    private BigDecimal adRevenue;

    @Schema(description = "总收入($)")
    private BigDecimal totalRevenue;

    @Schema(description = "新增付费率")
    private BigDecimal newUserPayRate;

    @Schema(description = "付费率")
    private BigDecimal payRate;

    @Schema(description = "ARPDAU")
    private BigDecimal arpdau;

    @Schema(description = "ARPPU")
    private BigDecimal arppu;

    @Schema(description = "激励视频人均次数")
    private BigDecimal ipuRv;

    @Schema(description = "插屏广告人均次数")
    private BigDecimal ipuInt;

    @Schema(description = "付费人数")
    private Integer payingUsers;

    @Schema(description = "付费次数")
    private Integer paymentCount;

    @Schema(description = "新增付费人数")
    private Integer newPayingUsers;

    @Schema(description = "新增付费次数")
    private Integer newPaymentCount;

    @Schema(description = "LTV-IAP (内购生命周期价值)")
    private BigDecimal ltvIap;

    @Schema(description = "LTV-IAA (广告生命周期价值)")
    private BigDecimal ltvIaa;
}