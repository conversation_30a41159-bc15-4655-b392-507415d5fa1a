package me.supernova.dashboard.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <p>
 * 关卡信息表，存储游戏中各个关卡的详细信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Getter
@Setter
@ToString
@TableName("level_config")
@Schema(name = "LevelConfig", description = "关卡信息表，存储游戏中各个关卡的详细信息")
public class LevelConfig implements Serializable {

    /**
     * 关卡唯一标识，自增主键
     */
    @Schema(description = "关卡唯一标识，自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * AB测试的组
     */
    @Schema(description = "AB测试的组")
    private String abGroup;

    /**
     * 关卡类型，不同类型代表不同的关卡分类
     */
    @Schema(description = "关卡 ID")
    private Integer levelId;

    /**
     * 关卡类型，不同类型代表不同的关卡分类
     */
    @Schema(description = "关卡类型，不同类型代表不同的关卡分类")
    private Integer levelType;

    /**
     * 关卡名称
     */
    @Schema(description = "关卡名称")
    private String levelName;

    /**
     * 关卡优先级，用于排序或解锁顺序
     */
    @Schema(description = "关卡优先级，用于排序或解锁顺序")
    private Integer levelPriority;

    /**
     * 关卡解锁条件，描述解锁该关卡所需满足的条件
     */
    @Schema(description = "关卡解锁条件，描述解锁该关卡所需满足的条件")
    private String unlockConditions;

    /**
     * 关卡需求数量，用于表示通过关卡所需达到的条件数量
     */
    @Schema(description = "关卡需求数量，用于表示通过关卡所需达到的条件数量")
    private Integer requiredConditionCount;

    /**
     * 关卡是否启用，true为启用，false为未启用
     */
    @Schema(description = "关卡是否启用，true为启用，false为未启用")
    private Boolean enabled;

    /**
     * 是否启用关卡提示内容，true为启用，false为未启用
     */
    @Schema(description = "是否启用关卡提示内容，true为启用，false为未启用")
    private Boolean hintEnabled;

    /**
     * 关卡提示内容（中文）
     */
    @Schema(description = "关卡提示内容（中文）")
    private String hintContentZh;

    /**
     * 关卡提示内容（英文）
     */
    @Schema(description = "关卡提示内容（英文）")
    private String hintContentEn;

    /**
     * 关卡限时
     */
    @Schema(description = "关卡限时")
    private Integer timeLimitSeconds;
}
