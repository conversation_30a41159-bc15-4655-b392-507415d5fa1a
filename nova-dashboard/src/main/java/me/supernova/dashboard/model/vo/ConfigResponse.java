package me.supernova.dashboard.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "游戏配置响应")
public class ConfigResponse {
    @Schema(description = "播放广告间隔时间(秒)", example = "120")
    @JsonProperty("PlayIntersInterval")
    private Long playIntersInterval;

    @Schema(description = "首次不播放广告次数", example = "6")
    @JsonProperty("FirstNoIntersCount")
    private Integer firstNoIntersCount;

    @Schema(description = "播放广告间隔等级", example = "6")
    @JsonProperty("PlayIntersIntervalLevel")
    private Integer playIntersIntervalLevel;

    @Schema(description = "关卡顺序列表", example = "[{\"LevelId\":1,\"Order\":1},{\"LevelId\":2,\"Order\":2}]")
    @JsonProperty("LevelOrders")
    private List<LevelOrder> levelOrders;
} 