package me.supernova.dashboard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.supernova.dashboard.model.entity.UserRank;
import me.supernova.dashboard.model.vo.RankVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户排名表，存储用户分数及更新时间 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
public interface UserRankMapper extends BaseMapper<UserRank> {

    /**
     * 获取指定用户的排名
     */
    Integer selectRankByUserId(@Param("userId") Long userId);

    /**
     * 获取TopN用户的排名信息，直接返回 RankVo 列表
     *
     * @param n        获取数量
     * @param minScore 最低分数
     * @return List<RankVo>
     */
    List<RankVo> selectTopNRankVos(@Param("n") int n, @Param("minScore") int minScore);

    List<RankVo> selectTopNUserRankVos(@Param("n") int n, @Param("minScore") int minScore);
}
