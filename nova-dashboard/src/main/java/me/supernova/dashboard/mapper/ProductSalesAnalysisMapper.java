package me.supernova.dashboard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.supernova.dashboard.model.bo.ProductSalesAnalysisBo;
import me.supernova.dashboard.model.dto.ProductBasicSalesDTO;
import me.supernova.dashboard.model.dto.ProductNewUserSalesDTO;
import me.supernova.dashboard.model.dto.TimeConfig;
import me.supernova.dashboard.model.entity.Orders;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品销售数据分析 Mapper 接口
 */
@Mapper
public interface ProductSalesAnalysisMapper extends BaseMapper<Orders> {

    /**
     * 查询商品基础销售数据
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 基础销售数据列表
     */
    List<ProductBasicSalesDTO> selectProductBasicSales(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") ProductSalesAnalysisBo qryBo
    );

    /**
     * 查询商品新用户销售数据
     *
     * @param productIds 商品ID列表
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 新用户销售数据列表
     */
    List<ProductNewUserSalesDTO> selectProductNewUserSales(
            @Param("productIds") List<String> productIds,
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") ProductSalesAnalysisBo qryBo
    );

    /**
     * 查询总销售额
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 总销售额
     */
    BigDecimal selectTotalSalesAmount(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") ProductSalesAnalysisBo qryBo
    );
}
