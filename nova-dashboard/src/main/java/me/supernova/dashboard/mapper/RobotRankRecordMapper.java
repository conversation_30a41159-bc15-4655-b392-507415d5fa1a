package me.supernova.dashboard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.supernova.dashboard.model.entity.RobotRankRecord;

import java.util.List;

/**
 * <p>
 * 积分记录表，存储用户积分及排名信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
public interface RobotRankRecordMapper extends BaseMapper<RobotRankRecord> {

    /**
     * 获取所有不同的sequence值
     *
     * @return sequence列表
     */
    List<Integer> selectAllSequences();
}
