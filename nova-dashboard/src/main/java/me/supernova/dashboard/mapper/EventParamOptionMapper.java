package me.supernova.dashboard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.supernova.dashboard.model.entity.EventParamOption;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 事件参数选项表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface EventParamOptionMapper extends BaseMapper<EventParamOption> {

    /**
     * 批量插入并忽略重复
     *
     * @param entityList 实体列表
     * @param batchSize  批次大小
     */
    void insertBatchIgnore(@Param("list") List<EventParamOption> entityList, @Param("batchSize") int batchSize);
}
