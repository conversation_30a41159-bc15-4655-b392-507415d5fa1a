package me.supernova.dashboard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import me.supernova.common.mybatis.mapper.BaseMapperPlus;
import me.supernova.dashboard.model.entity.SysDictData;
import me.supernova.dashboard.model.vo.SysDictDataVo;

import java.util.List;

/**
 * 字典表 数据层
 *
 * <AUTHOR> Li
 */
public interface SysDictDataMapper extends BaseMapperPlus<SysDictData, SysDictDataVo> {

    default List<SysDictDataVo> selectDictDataByType(String dictType) {
        return selectVoList(
                new LambdaQueryWrapper<SysDictData>()
                        .eq(SysDictData::getDictType, dictType)
                        .orderByAsc(SysDictData::getDictSort));
    }
}
