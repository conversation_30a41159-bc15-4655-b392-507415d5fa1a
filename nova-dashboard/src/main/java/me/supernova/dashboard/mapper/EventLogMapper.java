package me.supernova.dashboard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.supernova.dashboard.model.bo.BaseAnalysisBo;
import me.supernova.dashboard.model.bo.DataOverviewBo;
import me.supernova.dashboard.model.dto.*;
import me.supernova.dashboard.model.entity.EventLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 游戏事件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Mapper
public interface EventLogMapper extends BaseMapper<EventLog> {


    List<UserRetentionDTO> calculateUserRetention(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("eventName") String eventName
    );

    /**
     * 根据事件条件统计不同用户数量
     * 按照指定的事件条件筛选并统计符合条件的不同用户数
     *
     * @param timeConfig 时间配置
     * @param qryBo 基础查询参数
     * @param paramElementDto 事件参数条件
     * @return 符合条件的不同用户数量
     */
    Integer countDistinctUsersByEventCriteria(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") BaseAnalysisBo qryBo,
            @Param("paramElementDto") ParamElementDto paramElementDto
    );

    /**
     * 统计总应用使用时间（使用对象参数）
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 总应用使用时间
     */
    BigDecimal countTotalAppTime(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") DataOverviewBo qryBo
    );

    /**
     * 统计应用内购收入（使用对象参数）
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 应用内购收入
     */
    BigDecimal sumIapRevenue(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") DataOverviewBo qryBo
    );

    /**
     * 统计广告收入（使用对象参数）
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 广告收入
     */
    BigDecimal sumAdRevenue(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") DataOverviewBo qryBo
    );

    /**
     * 统计新付费用户数（使用对象参数）
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 新付费用户数
     */
    int countNewPayingUsers(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") DataOverviewBo qryBo
    );

    /**
     * 统计付费用户数（使用对象参数）
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 付费用户数
     */
    int countPayingUsers(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") DataOverviewBo qryBo
    );

    /**
     * 获取支付指标（使用对象参数）
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 支付指标
     */
    PaymentMetricsDTO getPaymentMetrics(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") DataOverviewBo qryBo
    );

    /**
     * 获取广告展示指标（使用对象参数）
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 广告展示指标
     */
    AdShowMetricsDTO getAdShowMetrics(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") DataOverviewBo qryBo
    );

    /**
     * 获取用户指标（使用对象参数）
     *
     * @param timeConfig 时间配置
     * @param qryBo      查询参数
     * @return 用户指标
     */
    UserMetricsDTO getUserMetrics(
            @Param("timeConfig") TimeConfig timeConfig,
            @Param("qryBo") DataOverviewBo qryBo
    );
}
