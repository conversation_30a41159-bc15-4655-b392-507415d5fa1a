package me.supernova.dashboard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.supernova.dashboard.model.dto.UserRetentionMetricsDTO;
import me.supernova.dashboard.model.entity.UserRetention;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户留存记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
public interface UserRetentionMapper extends BaseMapper<UserRetention> {

    List<UserRetentionMetricsDTO> batchGetUserRetentionMetrics(@Param("startTime") LocalDateTime startTime,
                                                               @Param("endTime") LocalDateTime endTime,
                                                               @Param("recordType") String recordType,
                                                               @Param("timezone") Integer timezone,
                                                               @Param("ipCountryCode") List<String> ipCountryCode,
                                                               @Param("platform") String platform,
                                                               @Param("gameName") String gameName,
                                                               @Param("store") String store,
                                                               @Param("firstVersion") String firstVersion,
                                                               @Param("lastVersion") String lastVersion);
}
