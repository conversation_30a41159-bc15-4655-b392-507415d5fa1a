package me.supernova.dashboard;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@MapperScan("me.supernova.dashboard.mapper")
@SpringBootApplication
@EnableScheduling
public class NovaDashboardApplication {

    public static void main(String[] args) {
        SpringApplication.run(NovaDashboardApplication.class, args);
    }
}