package me.supernova.dashboard.controller.game;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.domain.R;
import me.supernova.dashboard.model.vo.EventLogVo;
import me.supernova.dashboard.service.EventLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 游戏事件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Tag(name = "事件上报", description = "事件上报相关接口")
@RequiredArgsConstructor
@RestController
@RequestMapping("/event")
public class EventLogController {

    private final EventLogService eventLogService;

    @ActionLog(ignore = true)
    @SaIgnore
    @Operation(summary = "上报事件", description = "上报游戏事件，包括通用事件、广告事件、购买事件等")
    @PostMapping("/report")
    public R<Void> reportEvent(
            @Parameter(description = "事件上报请求参数", required = true) @Validated @RequestBody EventLogVo vo) {
        eventLogService.saveEvent(vo);
        return R.ok();
    }
}
