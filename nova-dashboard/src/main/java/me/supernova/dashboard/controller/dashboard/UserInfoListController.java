package me.supernova.dashboard.controller.dashboard;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.core.domain.R;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.dashboard.model.bo.DeviceIdentifierQueryBO;
import me.supernova.dashboard.model.bo.UpdateUserNoAdsBO;
import me.supernova.dashboard.model.bo.UserInfoQueryBO;
import me.supernova.dashboard.model.entity.UserInfo;
import me.supernova.dashboard.model.vo.UserInfoListVO;
import me.supernova.dashboard.service.UserInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 用户信息列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Tag(name = "管理平台/用户信息列表", description = "用户信息列表相关接口")
@RequiredArgsConstructor
@RestController
@RequestMapping("/userInfo/list")
public class UserInfoListController {

    private final UserInfoService userInfoService;

    /**
     * 分页查询用户列表
     *
     * @param query     查询条件
     * @param pageQuery 分页参数
     * @return 用户列表
     */
    @Operation(
            summary = "分页查询用户列表",
            description = "根据条件分页查询用户列表，支持多条件组合查询"
    )
    @Parameters({
            @Parameter(name = "query", description = "用户查询条件", required = true),
            @Parameter(name = "pageQuery", description = "分页参数", required = true)
    })
    @SaCheckPermission("game:user:query")
    @GetMapping("/page")
    public TableDataInfo<UserInfoListVO> selectPageUserList(@Validated UserInfoQueryBO query, @Validated PageQuery pageQuery) {
        Page<UserInfo> page = userInfoService.selectPageUserList(query, pageQuery);
        return TableDataInfo.build(page, UserInfoListVO.class);
    }

    /**
     * 根据设备标识符查询用户信息
     *
     * @param query 设备标识符查询条件
     * @return 用户信息列表
     */
    @Operation(
            summary = "根据设备标识符查询用户信息",
            description = "支持根据idfv、idfa、gpsadid、adjustadid、uuid等设备标识符进行精确查询或模糊搜索"
    )
    @Parameters({
            @Parameter(name = "query", description = "设备标识符查询条件", required = true)
    })
    @SaCheckPermission("game:user:query")
    @PostMapping("/queryByDeviceIdentifiers")
    public R<List<UserInfoListVO>> queryUsersByDeviceIdentifiers(@Validated @RequestBody DeviceIdentifierQueryBO query) {
        return R.ok(userInfoService.queryUsersByDeviceIdentifiers(query));
    }

    /**
     * 修改用户去广告状态
     *
     * @param request 修改请求参数
     * @return 修改结果
     */
    @Operation(
            summary = "修改用户去广告状态",
            description = "根据用户ID修改用户的去广告状态"
    )
    @Parameters({
            @Parameter(name = "request", description = "修改用户去广告状态请求参数", required = true)
    })
    @SaCheckPermission("game:user:edit")
    @PostMapping("/updateNoAdsStatus")
    public R<Void> updateUserNoAdsStatus(@Validated @RequestBody UpdateUserNoAdsBO request) {
        boolean success = userInfoService.updateUserNoAdsStatus(request.getUserId(), request.getNoAds());
        if (success) {
            return R.ok("用户去广告状态修改成功");
        } else {
            return R.fail("用户去广告状态修改失败");
        }
    }

}