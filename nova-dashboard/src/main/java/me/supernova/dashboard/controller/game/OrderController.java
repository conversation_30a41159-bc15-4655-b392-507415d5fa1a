package me.supernova.dashboard.controller.game;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.constant.MdcConstants;
import me.supernova.common.core.domain.R;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.dashboard.model.vo.ClientInfoVo;
import me.supernova.dashboard.model.vo.DiamondRechargeDto;
import me.supernova.dashboard.model.vo.OrderVerifyResponse;
import me.supernova.dashboard.service.OrdersService;
import org.slf4j.MDC;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "订单管理", description = "订单相关接口，包括创建订单、订单校验和查询订单等功能")
@RequiredArgsConstructor
@RestController
@RequestMapping("/order")
public class OrderController {

    private final OrdersService ordersService;

    /**
     * Redis 开关的 key
     */
    private static final String ORDER_VERIFY_SWITCH_KEY = "order:verify:switch";

    @ActionLog(ignore = true)
    @SaIgnore
    @Operation(summary = "订单校验", description = "校验订单支付状态，验证订单是否支付成功")
    @PostMapping(value = "/verify")
    public R<OrderVerifyResponse> recharge(
            @Parameter(description = "订单校验请求参数", required = true)
            @RequestBody DiamondRechargeDto dto) {

        //兼容性处理
        if (dto.getClientInfo() == null) {
            dto.setClientInfo(BeanUtil.copyProperties(dto, ClientInfoVo.class));
        }

        // 检查 Redis 开关
        String switchValue = RedisUtils.getCacheObject(ORDER_VERIFY_SWITCH_KEY);

        boolean verifyResult;
        // 开关为 true，直接返回 true
        // 开关为 false，直接返回 false
        if (switchValue == null || "disable".equals(switchValue)) {
            // 开关未设置或为 disable，正常执行
            verifyResult = ordersService.verifyOrder(dto);
        } else verifyResult = "true".equals(switchValue);

        MDC.remove(MdcConstants.ORDER_ID);

        return R.ok(new OrderVerifyResponse(verifyResult));
    }

}
