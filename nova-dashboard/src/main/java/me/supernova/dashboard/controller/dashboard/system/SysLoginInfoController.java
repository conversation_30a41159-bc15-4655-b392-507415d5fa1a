package me.supernova.dashboard.controller.dashboard.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import me.supernova.common.core.domain.R;
import me.supernova.common.excel.utils.ExcelUtil;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.common.web.core.BaseController;
import me.supernova.dashboard.model.bo.SysLoginInfoBo;
import me.supernova.dashboard.model.vo.SysLoginInfoVo;
import me.supernova.dashboard.service.SysLoginInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统访问记录
 *
 * <AUTHOR> Li
 */
@Tag(name = "管理平台/系统管理/登录日志")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/monitor/loginInfo")
public class SysLoginInfoController extends BaseController {

    private final SysLoginInfoService loginInfoService;

    /**
     * 获取系统访问记录列表
     */
    @Operation(summary = "获取系统访问记录列表")
    @SaCheckPermission("monitor:loginInfo:list")
    @GetMapping("/list")
    public TableDataInfo<SysLoginInfoVo> list(SysLoginInfoBo loginInfoBo, PageQuery pageQuery) {
        return loginInfoService.selectPageloginInfoList(loginInfoBo, pageQuery);
    }

    /**
     * 导出系统访问记录列表
     */
    @Operation(summary = "导出系统访问记录列表")
    @SaCheckPermission("monitor:loginInfo:export")
    @PostMapping("/export")
    public void export(SysLoginInfoBo loginInfoBo, HttpServletResponse response) {
        List<SysLoginInfoVo> list = loginInfoService.selectLoginInfoList(loginInfoBo);
        ExcelUtil.exportExcel(list, "登录日志", SysLoginInfoVo.class, response);
    }

    /**
     * 批量删除登录日志
     *
     * @param infoIds 日志ids
     */
    @Operation(summary = "批量删除登录日志")
    @SaCheckPermission("monitor:loginInfo:remove")
    @DeleteMapping("/{infoIds}")
    public R<Void> remove(@PathVariable Long[] infoIds) {
        return toAjax(loginInfoService.deleteLoginInfoByIds(infoIds));
    }

    /**
     * 清理系统访问记录
     */
    @Operation(summary = "清理系统访问记录")
    @SaCheckPermission("monitor:loginInfo:remove")
    @DeleteMapping("/clean")
    public R<Void> clean() {
        loginInfoService.cleanLoginInfo();
        return R.ok();
    }
}
