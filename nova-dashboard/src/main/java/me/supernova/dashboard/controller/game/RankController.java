package me.supernova.dashboard.controller.game;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.domain.R;
import me.supernova.dashboard.model.dto.RankDto;
import me.supernova.dashboard.model.vo.RankResponse;
import me.supernova.dashboard.service.RankService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "排行榜", description = "排行榜相关接口，包括获取排行榜数据等功能")
@RequiredArgsConstructor
@RestController
@RequestMapping("/rank")
public class RankController {

    private final RankService rankService;

    @ActionLog(ignore = true)
    @SaIgnore
    @Operation(summary = "获取榜单")
    @PostMapping(value = "/get")
    public R<RankResponse> get(@Validated @RequestBody RankDto dto) {
        return R.ok(rankService.getRank(dto));
    }
}
