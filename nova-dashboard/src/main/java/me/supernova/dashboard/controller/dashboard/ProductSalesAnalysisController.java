package me.supernova.dashboard.controller.dashboard;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.dashboard.model.bo.ProductSalesAnalysisBo;
import me.supernova.dashboard.model.vo.ProductSalesAnalysisVo;
import me.supernova.dashboard.service.ProductSalesAnalysisService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品销售数据分析控制器
 */
@Tag(name = "管理平台/商品销售分析", description = "商品销售数据分析相关接口")
@RestController
@RequestMapping("/productSalesAnalysis")
@RequiredArgsConstructor
public class ProductSalesAnalysisController {

    private final ProductSalesAnalysisService productSalesAnalysisService;

    @Operation(summary = "查询商品销售数据分析")
    @GetMapping("/query")
    public TableDataInfo<ProductSalesAnalysisVo> queryProductSalesAnalysis(ProductSalesAnalysisBo request) {
        List<ProductSalesAnalysisVo> result = productSalesAnalysisService.queryProductSalesAnalysis(request);
        return TableDataInfo.build(result);
    }
}
