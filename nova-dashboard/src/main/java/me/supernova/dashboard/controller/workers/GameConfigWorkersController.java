package me.supernova.dashboard.controller.workers;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.domain.R;
import me.supernova.dashboard.model.vo.BaseVo;
import me.supernova.dashboard.model.vo.ExcelGameConfig;
import me.supernova.dashboard.model.vo.GameConfigV2;
import me.supernova.dashboard.service.LevelConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "配置管理", description = "游戏配置相关接口，包括获取游戏配置等功能")
@RequiredArgsConstructor
@RestController
@RequestMapping("/workers")
public class GameConfigWorkersController {

    private final LevelConfigService levelConfigService;

    @ActionLog(ignore = true)
    @SaIgnore
    @Operation(
            summary = "获取游戏配置V2",
            description = "获取游戏相关配置信息，包括广告播放间隔、首次不播放广告次数等配置"
    )
    @PostMapping("/config/game/v2")
    public R<GameConfigV2> getGameConfigV2(@Validated @RequestBody BaseVo vo) {
        return R.ok(levelConfigService.getConfig(vo));
    }


    @ActionLog(ignore = true)
    @SaIgnore
    @Operation(
            summary = "Worker端获取游戏配置XLSX",
            description = "Worker端根据clientInfo中的版本信息从Redis获取对应的XLSX配置数据"
    )
    @PostMapping("/config/xlsx/worker")
    public R<ExcelGameConfig> getGameConfigXlsxForWorker(@Validated @RequestBody BaseVo vo) {
        ExcelGameConfig xlsxContent = levelConfigService.getGameConfigXlsxForWorker(vo);

        return R.ok(xlsxContent);
    }
}