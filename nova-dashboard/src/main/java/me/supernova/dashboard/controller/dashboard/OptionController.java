package me.supernova.dashboard.controller.dashboard;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import me.supernova.common.core.domain.R;
import me.supernova.dashboard.service.EventParamOptionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static me.supernova.dashboard.job.EventParamScanJob.EVENT_PARAM_TREE_PREFIX;

@Tag(name = "管理平台/选项管理")
@Validated
@RestController
@RequestMapping("/option")
@RequiredArgsConstructor
public class OptionController {

    private final EventParamOptionService eventParamOptionService;

    /**
     * 获取事件选项
     *
     * @param param 参数字符串（不能为空）
     * @return 事件选项列表
     */
    @Operation(summary = "获取事件选项")
    @GetMapping("/event")
    public R<List<String>> getEventOptions(@RequestParam @NotBlank(message = "参数不能为空") String param) {
        List<String> options = eventParamOptionService.getOptions(EVENT_PARAM_TREE_PREFIX + param);
        return R.ok(options);
    }

}
