package me.supernova.dashboard.controller.dashboard.system;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.domain.R;
import me.supernova.dashboard.model.bo.LarkLoginBo;
import me.supernova.dashboard.model.bo.LoginBo;
import me.supernova.dashboard.model.vo.LoginVo;
import me.supernova.dashboard.service.impl.SysUserLoginService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理平台/系统管理/认证管理")
@RestController
@RequiredArgsConstructor
public class SysAuthController {

    private final SysUserLoginService sysUserLoginService;

    @SaIgnore
    @Operation(summary = "飞书登录", description = "飞书用户登录")
    @GetMapping("/auth/test")
    public R<LoginVo> test() {
        return R.ok();
    }

    @SaIgnore
    @ActionLog(ignore = true)
    @Operation(summary = "登录", description = "用户登录")
    @PostMapping("/auth/login")
    public R<LoginVo> login(@Validated @RequestBody LoginBo req) {
        return R.ok(sysUserLoginService.login(req));
    }

    @Operation(summary = "注销登录")
    @PostMapping("/auth/logout")
    public R<Void> logout() {
        StpUtil.logout();
        return R.ok();
    }

    @SaIgnore
    @ActionLog(ignore = true)
    @Operation(summary = "飞书登录", description = "飞书用户登录")
    @PostMapping("/auth/login/lark")
    public R<LoginVo> larkLogin(@Validated @RequestBody LarkLoginBo req) {
        return R.ok(sysUserLoginService.larkLogin(req));
    }

}