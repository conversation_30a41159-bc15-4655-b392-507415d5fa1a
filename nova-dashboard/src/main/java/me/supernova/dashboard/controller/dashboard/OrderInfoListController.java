package me.supernova.dashboard.controller.dashboard;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.dashboard.model.bo.OrderInfoQueryBO;
import me.supernova.dashboard.model.entity.Orders;
import me.supernova.dashboard.model.vo.OrderInfoListVo;
import me.supernova.dashboard.service.OrdersService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 订单信息列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Tag(name = "管理平台/订单信息列表", description = "订单信息列表相关接口")
@RequiredArgsConstructor
@RestController
@RequestMapping("/orderInfo/list")
public class OrderInfoListController {

    private final OrdersService ordersService;

    /**
     * 分页查询订单列表
     *
     * @param query     查询条件
     * @param pageQuery 分页参数
     * @return 订单列表
     */
    @Operation(
            summary = "分页查询订单列表",
            description = "根据条件分页查询订单列表，支持多条件组合查询"
    )
    @Parameters({
            @Parameter(name = "query", description = "订单查询条件", required = true),
            @Parameter(name = "pageQuery", description = "分页参数", required = true)
    })
    @SaCheckPermission("game:order:query")
    @GetMapping("/page")
    public TableDataInfo<OrderInfoListVo> selectPageOrderList(@Validated OrderInfoQueryBO query, @Validated PageQuery pageQuery) {
        Page<Orders> page = ordersService.selectPageOrderList(query, pageQuery);
        return TableDataInfo.build(page, OrderInfoListVo.class);
    }
} 