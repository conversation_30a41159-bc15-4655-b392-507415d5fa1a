package me.supernova.dashboard.controller.dashboard;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.domain.R;
import me.supernova.dashboard.model.bo.FunnelAnalysisBO;
import me.supernova.dashboard.model.dto.UserEventSequenceDTO;
import me.supernova.dashboard.model.entity.Funnels;
import me.supernova.dashboard.service.FunnelsService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "管理平台/事件漏斗", description = "事件漏斗相关接口")
@RestController
@RequestMapping("/funnels")
@RequiredArgsConstructor
public class FunnelsController {

    private final FunnelsService funnelsService;

    @GetMapping("/list")
    @Operation(summary = "列出所有漏斗")
    public R<List<Funnels>> list() {
        return R.ok(funnelsService.list(Wrappers.lambdaQuery(Funnels.class).orderByDesc(Funnels::getCreateTime)));
    }

    @PostMapping("/save")
    @Operation(summary = "创建/编辑 漏斗")
    public R<Void> create(@RequestBody Funnels analysisBO) {
        funnelsService.saveOrUpdate(analysisBO);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除漏斗")
    public R<Void> delete(@RequestParam Long id) {
        funnelsService.removeById(id);
        return R.ok();
    }

    @PostMapping("/query")
    @Operation(summary = "执行漏斗查询")
    public R<List<UserEventSequenceDTO>> query(@RequestBody FunnelAnalysisBO analysisBO) {
        return R.ok(funnelsService.analyzeFunnelEvents(analysisBO));
    }

}