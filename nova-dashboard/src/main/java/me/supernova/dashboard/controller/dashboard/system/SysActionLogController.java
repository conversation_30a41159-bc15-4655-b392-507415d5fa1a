package me.supernova.dashboard.controller.dashboard.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.act.log.core.enums.Include;
import me.supernova.common.core.domain.R;
import me.supernova.common.excel.utils.ExcelUtil;
import me.supernova.common.mybatis.core.page.PageQuery;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.common.web.core.BaseController;
import me.supernova.dashboard.model.bo.SysActionLogBo;
import me.supernova.dashboard.model.vo.SysActionLogVo;
import me.supernova.dashboard.service.SysActionLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统日志 前端控制器
 *
 * <AUTHOR>
 */
@Tag(name = "管理平台/系统管理/系统日志管理")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/log")
public class SysActionLogController extends BaseController {

    private final SysActionLogService actionLogService;

    /**
     * 获取系统日志列表
     */
    @ActionLog(excludes = {Include.RESPONSE_BODY})
    @Operation(summary = "获取系统日志列表")
    @SaCheckPermission("system:log:query")
    @GetMapping("/list")
    public TableDataInfo<SysActionLogVo> list(SysActionLogBo bo, PageQuery pageQuery) {
        return actionLogService.selectPageActionLogList(bo, pageQuery);
    }

    /**
     * 获取系统日志详细信息
     *
     * @param id 日志ID
     */
    @ActionLog(excludes = {Include.RESPONSE_BODY})
    @Operation(summary = "获取系统日志详细信息")
    @SaCheckPermission("system:log:query")
    @GetMapping("/{id}")
    public R<SysActionLogVo> getInfo(@PathVariable Long id) {
        return R.ok(actionLogService.selectActionLogById(id));
    }

    /**
     * 导出操作日志
     */
    @Operation(summary = "导出操作日志")
    @SaCheckPermission("system:log:export")
    @PostMapping("/export/operation")
    public void exportOperation(SysActionLogBo bo, HttpServletResponse response) {
        List<SysActionLogVo> list = actionLogService.selectActionLogList(bo);
        ExcelUtil.exportExcel(list, "操作日志", SysActionLogVo.class, response);
    }
}
