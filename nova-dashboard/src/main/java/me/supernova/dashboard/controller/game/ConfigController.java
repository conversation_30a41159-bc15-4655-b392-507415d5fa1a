package me.supernova.dashboard.controller.game;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.domain.R;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.dashboard.constant.GameConstants;
import me.supernova.dashboard.model.vo.GameConfig;
import me.supernova.dashboard.model.vo.LevelOrder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Tag(name = "配置管理", description = "游戏配置相关接口，包括获取游戏配置等功能")
@RequiredArgsConstructor
@RestController
@RequestMapping("/config")
public class ConfigController {

    @ActionLog(ignore = true)
    @SaIgnore
    @Operation(
            summary = "获取游戏配置",
            description = "获取游戏相关配置信息，包括广告播放间隔、首次不播放广告次数等配置"
    )
    @PostMapping("/game")
    public R<GameConfig> getGameConfig() {
        GameConfig config = RedisUtils.getCacheObject(GameConstants.GAME_CONFIG_KEY);
        if (config == null) {
            config = new GameConfig();
        }

        // 检查并设置默认值
        if (config.getPlayIntersInterval() == null) {
            config.setPlayIntersInterval(120L);
        }

        if (config.getFirstNoIntersCount() == null) {
            config.setFirstNoIntersCount(6);
        }

        if (config.getPlayIntersIntervalLevel() == null) {
            config.setPlayIntersIntervalLevel(6);
        }

        if (config.getLevelOrders() == null) {
            List<LevelOrder> levelOrders = new ArrayList<>();
            for (int i = 1; i <= 40; i++) {
                levelOrders.add(new LevelOrder(i, "第" + i + "关", i));
            }
            config.setLevelOrders(levelOrders);
        }

        RedisUtils.setCacheObject(GameConstants.GAME_CONFIG_KEY, config);
        return R.ok(config);
    }
} 