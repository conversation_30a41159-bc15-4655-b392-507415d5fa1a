package me.supernova.dashboard.controller.dashboard;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.dashboard.model.bo.UserRetentionBo;
import me.supernova.dashboard.model.vo.UserRetentionVo;
import me.supernova.dashboard.service.UserRetentionService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "管理平台/用户留存分析", description = "用户留存分析相关接口")
@RestController
@RequestMapping("/userRetention")
@RequiredArgsConstructor
public class UserRetentionController {

    private final UserRetentionService userRetentionService;

    @Operation(summary = "查询用户留存数据")
    @GetMapping("/query")
    public TableDataInfo<UserRetentionVo> queryUserRetention(UserRetentionBo request) {
        List<UserRetentionVo> userRetentionVos = userRetentionService.queryUserRetention(request);
        return TableDataInfo.build(userRetentionVos);
    }

} 