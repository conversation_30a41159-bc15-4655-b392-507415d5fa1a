package me.supernova.dashboard.controller.dashboard.test;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.domain.R;
import me.supernova.common.core.utils.LocalIpUtil;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Tag(name = "测试API")
@RequiredArgsConstructor
@RestController
@RequestMapping("/test")
public class TestController {

    @Operation(summary = "GET接口")
    @SaIgnore
    @ActionLog(ignore = true)
    @GetMapping("/get")
    public R<Map<String, Object>> get(@RequestParam String param) {
        RuntimeException runtimeException = new RuntimeException("测试");
        log.error("测试", runtimeException);
        Map<String, Object> map = new HashMap<>();
        map.put("param", param);
        map.put("version", "1");
        map.put("localIp", LocalIpUtil.getMainIp());
        map.put("networkIps", LocalIpUtil.getNetworkIps());
        return R.ok(map);
    }

    @Operation(summary = "POST接口")
    @SaIgnore
    @ActionLog(ignore = true)
    @PostMapping("/post")
    public R<Map<Object, Object>> post(@RequestBody Map<String, String> param) {
        Map<Object, Object> map = new HashMap<>();
        map.put("param", param);
        map.put("version", "1");
        map.put("localIp", LocalIpUtil.getMainIp());
        map.put("networkIps", LocalIpUtil.getNetworkIps());
        return R.ok(map);
    }
}
