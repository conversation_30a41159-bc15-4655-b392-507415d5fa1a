package me.supernova.dashboard.controller.dashboard;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.mybatis.core.page.TableDataInfo;
import me.supernova.dashboard.model.bo.DataOverviewBo;
import me.supernova.dashboard.model.vo.DataOverviewVo;
import me.supernova.dashboard.service.DataOverviewService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Tag(name = "管理平台/数据概览", description = "数据概览相关接口")
@RestController
@RequestMapping("/dataOverview")
@RequiredArgsConstructor
@Slf4j
public class DataOverviewController {

    private final DataOverviewService dataOverviewService;

    @Operation(summary = "查询数据概览")
    @GetMapping("/query")
    public TableDataInfo<DataOverviewVo> queryDataOverview(DataOverviewBo request) {
        List<DataOverviewVo> dataOverviewVos = dataOverviewService.queryDataOverview(request);
        return TableDataInfo.build(dataOverviewVos);
    }

    @ActionLog(ignore = true)
    @Operation(summary = "查询数据概览（流式）")
    @GetMapping(value = "/query/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter queryDataOverviewStream(DataOverviewBo request) {
        SseEmitter emitter = new SseEmitter(120000L); // 120秒超时

        // 设置完成回调
        emitter.onCompletion(() -> log.info("数据概览流式查询连接已关闭"));

        // 设置错误回调
        emitter.onError((throwable) -> log.error("数据概览流式查询连接发生错误", throwable));

        // 异步处理数据流
        CompletableFuture.runAsync(() -> {
            try {
                dataOverviewService.queryDataOverviewStream(request, emitter);
            } catch (Exception e) {
                log.error("流式查询数据概览失败", e);
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }
}