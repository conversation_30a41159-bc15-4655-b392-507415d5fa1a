package me.supernova.dashboard.controller.dashboard;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.domain.R;
import me.supernova.dashboard.job.EventParamScanJob;
import me.supernova.dashboard.job.RobotScoreRecordJob;
import me.supernova.dashboard.job.UserRetentionJob;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理平台/定时任务")
@Validated
@RequestMapping("/job")
@RestController
@RequiredArgsConstructor
public class JobTriggerController {

    private final UserRetentionJob userRetentionJob;
    private final EventParamScanJob eventParamScanJob;
    private final RobotScoreRecordJob robotScoreRecordJob;

    @ActionLog(ignore = true)
    @Operation(summary = "手动触发定时任务")
    @GetMapping("/trigger")
    public R<Void> triggerJob(@RequestParam String jobName) {
        switch (jobName) {
            case "UserRetentionJob":
                userRetentionJob.execute();
                break;
            case "EventParamScanJob":
                eventParamScanJob.execute();
                break;
            case "RobotScoreRecordJob":
                robotScoreRecordJob.execute();
                break;
            default:
                return R.fail("未知的任务类型：" + jobName);
        }
        return R.ok();
    }
}
