package me.supernova.dashboard.controller.game.callback;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.codec.Base64;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.domain.R;
import me.supernova.common.json.utils.JsonUtils;
import me.supernova.dashboard.model.bo.DeveloperNotification;
import me.supernova.dashboard.model.bo.VoidedPurchaseNotification;
import me.supernova.dashboard.model.dto.GooglePayNotificationMessage;
import me.supernova.dashboard.service.GooglePayNotificationService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 谷歌支付通知控制器
 */
@Slf4j
@Hidden
@RestController
@RequiredArgsConstructor
@RequestMapping("/google/pay")
public class GooglePayNoticeController {

    private final GooglePayNotificationService googlePayNotificationService;

    /**
     * 谷歌支付通知
     *
     * @param googlePayNotificationMessage 谷歌支付通知消息
     * @return 处理结果
     */
    @ActionLog(ignore = true)
    @SaIgnore
    @Operation(summary = "谷歌支付通知", description = "接收谷歌支付通知并打印消息")
    @PostMapping("/notification")
    public R<Object> notice(
            @Parameter(description = "谷歌支付通知消息", required = true)
            @Validated
            @RequestBody GooglePayNotificationMessage googlePayNotificationMessage) {
        log.info("[google pay Notification]:[{}]", googlePayNotificationMessage);

        if (Objects.isNull(googlePayNotificationMessage)) {
            return R.ok();
        }

        GooglePayNotificationMessage.MessageDTO message = googlePayNotificationMessage.getMessage();
        if (Objects.isNull(message)) {
            return R.ok();
        }
        
        // 直接打印消息数据
        log.info("[google pay Notification data]:[{}]", message.getData());

        String data = new String(Base64.decode(message.getData()));

        DeveloperNotification developerNotification = JsonUtils.parseObject(data, DeveloperNotification.class);
        log.info("google_pay,收到谷歌通知,developerNotification:{}", developerNotification);
        if (Objects.isNull(developerNotification)) {
            log.info("google_pay,收到谷歌通知为null，不处理");
            return R.ok();
        }

        //处理退款商品相关消息
        VoidedPurchaseNotification voidedPurchaseNotification = developerNotification.getVoidedPurchaseNotification();
        if (Objects.nonNull(voidedPurchaseNotification)) {
            try {
                googlePayNotificationService.voidedNotification(developerNotification);
            } catch (Exception e) {
                log.error("voided_google_pay,谷歌通知报错", e);
            }
        }

        return R.ok();
    }
} 