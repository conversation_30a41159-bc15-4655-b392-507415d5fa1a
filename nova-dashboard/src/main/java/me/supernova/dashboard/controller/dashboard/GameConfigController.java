package me.supernova.dashboard.controller.dashboard;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.domain.R;
import me.supernova.common.redis.utils.RedisUtils;
import me.supernova.dashboard.constant.GameConstants;
import me.supernova.dashboard.model.vo.*;
import me.supernova.dashboard.service.LevelConfigService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@Tag(name = "管理平台/游戏配置管理", description = "游戏配置管理相关接口")
@RequiredArgsConstructor
@RestController
@RequestMapping("/dashboard/game/config")
@Slf4j
public class GameConfigController {

    private final LevelConfigService levelConfigService;

    /**
     * 获取关卡配置
     *
     * @param abGroup AB测试组，如果为null则获取默认组
     * @return 关卡配置
     */
    @SaCheckPermission("game:config:list")
    @Operation(summary = "获取关卡配置V2", description = "获取指定AB测试组的关卡配置")
    @GetMapping("V2")
    public R<AdminGameConfigV2> getConfig(
            @Parameter(description = "AB测试组，如果为空则获取默认组") @RequestParam(required = false) String abGroup) {
        return R.ok(levelConfigService.getAdminConfig(abGroup));
    }

    /**
     * 更新关卡配置
     *
     * @param config 关卡配置
     * @return 是否更新成功
     */
    @SaCheckPermission("game:config:edit")
    @Operation(summary = "更新关卡配置V2", description = "更新指定AB测试组的关卡配置")
    @PutMapping("V2")
    public R<Void> updateConfig(@RequestBody AdminGameConfigV2 config) {
        boolean result = levelConfigService.updateAdminConfig(config);
        return result ? R.ok() : R.fail("更新关卡配置失败");
    }


    @SaCheckPermission("game:config:list")
    @Operation(summary = "获取游戏配置")
    @GetMapping
    public R<GameConfig> getGameConfig() {
        GameConfig config = RedisUtils.getCacheObject(GameConstants.GAME_CONFIG_KEY);
        if (config == null) {
            config = new GameConfig();
        }

        // 检查并设置默认值
        if (config.getPlayIntersInterval() == null) {
            config.setPlayIntersInterval(120L);
        }

        if (config.getFirstNoIntersCount() == null) {
            config.setFirstNoIntersCount(6);
        }

        if (config.getPlayIntersIntervalLevel() == null) {
            config.setPlayIntersIntervalLevel(6);
        }

        if (config.getLevelOrders() == null) {
            List<LevelOrder> levelOrders = new ArrayList<>();
            for (int i = 1; i <= 40; i++) {
                levelOrders.add(new LevelOrder(i, "第" + i + "关", i));
            }
            config.setLevelOrders(levelOrders);
        }

        RedisUtils.setCacheObject(GameConstants.GAME_CONFIG_KEY, config);
        return R.ok(config);
    }

    @SaCheckPermission("game:config:edit")
    @Operation(summary = "更新游戏配置")
    @PutMapping
    public R<Void> updateGameConfig(@RequestBody GameConfig config) {
        RedisUtils.setCacheObject(GameConstants.GAME_CONFIG_KEY, config);
        return R.ok();
    }


    /**
     * 上传游戏配置XLSX文件到Redis
     *
     * @param file    XLSX文件
     * @param version 版本号
     * @return 上传结果
     */
    @SaCheckPermission("game:config:edit")
    @Operation(summary = "上传游戏配置XLSX文件", description = "控制台端上传XLSX格式的配置文件到Redis，使用版本号作为key的一部分")
    @PostMapping("/upload-xlsx")
    public R<Void> uploadGameConfigXlsx(
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "版本号") @RequestParam("version") String version) {

        String errorMsg = levelConfigService.uploadGameConfigXlsx(file, version);
        if (StrUtil.isNotBlank(errorMsg)) {
            return R.fail(errorMsg);
        }
        return R.ok("上传成功");
    }

    /**
     * 获取游戏配置XLSX数据
     *
     * @param version 版本号
     * @return 配置数据JSON字符串
     */
    @SaCheckPermission("game:config:list")
    @Operation(summary = "获取游戏配置XLSX数据", description = "控制台端根据版本号从Redis获取对应的配置数据JSON字符串")
    @GetMapping("/get-xlsx-data")
    public R<ExcelGameConfig> getGameConfigXlsxData(
            @Parameter(description = "版本号") @RequestParam("version") String version) {

        return R.ok(levelConfigService.getGameConfigXlsxData(version));
    }

    /**
     * 获取所有游戏配置版本列表
     *
     * @return 版本信息列表
     */
    @SaCheckPermission("game:config:list")
    @Operation(summary = "获取所有版本号列表", description = "返回所有已上传的游戏配置版本号列表，按上传时间倒序排列")
    @GetMapping("/versions")
    public R<List<VersionInfo>> getAllVersions() {
        List<VersionInfo> versions = levelConfigService.getAllVersions();
        return R.ok(versions);
    }

    /**
     * 删除指定版本的游戏配置
     *
     * @param version 版本号
     * @return 删除结果
     */
    @SaCheckPermission("game:config:delete")
    @Operation(summary = "删除指定版本", description = "删除指定版本号的CSV文件和相关记录")
    @DeleteMapping("/version")
    public R<Void> deleteGameConfigVersion(
            @Parameter(description = "版本号") @RequestParam String version
    ) {

        String errorMsg = levelConfigService.deleteGameConfigVersion(version);
        if (StrUtil.isNotBlank(errorMsg)) {
            return R.fail(errorMsg);
        }
        return R.ok("删除成功");
    }
}