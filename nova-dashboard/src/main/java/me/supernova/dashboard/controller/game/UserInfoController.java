package me.supernova.dashboard.controller.game;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.core.domain.R;
import me.supernova.dashboard.constant.AbTestGroupConstant;
import me.supernova.dashboard.model.entity.UserInfo;
import me.supernova.dashboard.model.vo.ClientInfoVo;
import me.supernova.dashboard.model.vo.RegisterLoginVo;
import me.supernova.dashboard.model.vo.UserRegisterResponse;
import me.supernova.dashboard.service.UserInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 设备信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Slf4j
@Tag(name = "设备注册或登录", description = "设备注册和登录相关接口")
@RequiredArgsConstructor
@RestController
@RequestMapping("/userInfo")
public class UserInfoController {

    private final UserInfoService userInfoService;

    /**
     * 设备注册/登录
     *
     * @return 用户信息
     */
    @ActionLog(ignore = true)
    @SaIgnore
    @Operation(summary = "设备注册或登录", description = "根据设备标识符自动注册或登录设备")
    @PostMapping("/register")
    public R<UserRegisterResponse> registerOrLogin(@RequestBody @Validated RegisterLoginVo vo) {
        // 兼容旧版本
        if (vo.getClientInfo() == null) {
            ClientInfoVo clientInfoVO = BeanUtil.copyProperties(vo, ClientInfoVo.class);
            vo.setClientInfo(clientInfoVO);
        }

        UserInfo userInfoOpt = userInfoService.updateOrCreateDeviceInfo(vo.getClientInfo());
        return R.ok(new UserRegisterResponse(
                userInfoOpt.getUserId(),
                userInfoOpt.getNoAds(),
                new UserRegisterResponse.AbtestGroupVo(userInfoOpt.getAbTestGroup().getStr(AbTestGroupConstant.LEVEL_SETTING))
        ));
    }
}
