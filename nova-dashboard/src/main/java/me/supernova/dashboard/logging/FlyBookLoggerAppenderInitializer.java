package me.supernova.dashboard.logging;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class FlyBookLoggerAppenderInitializer implements ApplicationListener<ApplicationReadyEvent> {

    @Value("${feishu.webhook:}")
    private String alertUrl;

    /**
     * Handle an application event.
     *
     * @param event the event to respond to
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        // 添加飞书日志Appender
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        FlyBookAppender flyBookAppender = new FlyBookAppender();
        flyBookAppender.setName("FLY_BOOK");
        flyBookAppender.setAlertUrl(alertUrl);
        flyBookAppender.setContext(loggerContext);
        flyBookAppender.start();

        Logger logger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME);
        logger.addAppender(flyBookAppender);
    }
}