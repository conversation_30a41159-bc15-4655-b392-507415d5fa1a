package me.supernova.dashboard.logging;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import cn.hutool.http.HttpRequest;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.constant.MdcConstants;
import me.supernova.common.core.utils.ServletUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Setter
@Component
public class FlyBookAppender extends AppenderBase<ILoggingEvent> {

    private String alertUrl;

    // 使用原子类存储上次发送时间
    private final AtomicLong lastSendTime = new AtomicLong(0);
    // 限流时间间隔，4秒
    private static final long INTERVAL = 4000;

    @Override
    protected void append(ILoggingEvent event) {
        if (!Level.ERROR.equals(event.getLevel())) {
            return;
        }

        // 尝试获取发送权限
        if (!tryAcquire()) {
            return;
        }

        sendToFlyBook(processAndBuildMessage(event));
    }

    private boolean tryAcquire() {
        long now = System.currentTimeMillis();
        long prev = lastSendTime.get();
        return now - prev >= INTERVAL && lastSendTime.compareAndSet(prev, now);
    }

    private void sendToFlyBook(String jsonMessage) {
        CompletableFuture.runAsync(() -> {
            try {
                //超时时间3秒, 避免频繁发送请求
                HttpRequest.post(this.alertUrl)
                        .timeout(3000)
                        .body(jsonMessage, "application/json;charset=UTF-8")
                        .execute()
                        .close();
            } catch (Exception e) {
                log.error("发送飞书消息失败", e);
            }
        });
    }

    private String processAndBuildMessage(ILoggingEvent event) {
        String message = event.getFormattedMessage();
        String stackTrace = processStackTrace(event);
        String curl = event.getMDCPropertyMap().get(MdcConstants.CURL);
        String traceId = event.getMDCPropertyMap().get(MdcConstants.TRACE_ID);

        // 使用安全的方式获取客户端IP，避免在异步上下文中访问已回收的request对象
        String clientIP = "unknown";

        try {
            if (isInRequestContext()) {
                clientIP = ServletUtils.getClientIP();
            } else {
                log.debug("当前不在HTTP请求上下文中，无法获取客户端IP");
            }
        } catch (Exception e) {
            log.debug("获取客户端IP失败: {}", e.getMessage());
        }

        return buildMessage(message, stackTrace, curl, traceId, clientIP, event);
    }

    public static boolean isInRequestContext() {
        try {
            return RequestContextHolder.getRequestAttributes() != null;
        } catch (Exception e) {
            return false;
        }
    }

    private String processStackTrace(ILoggingEvent event) {
        if (event.getThrowableProxy() == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        // 添加异常类型和消息
        sb.append(event.getThrowableProxy().getClassName())
                .append(": ")
                .append(event.getThrowableProxy().getMessage())
                .append("\n");

        // 添加堆栈跟踪
        String stackTrace = Arrays.stream(event.getThrowableProxy().getStackTraceElementProxyArray())
                .map(Object::toString)
                .filter(s -> !s.startsWith("at") || s.startsWith("at me.supernova.dashboard"))
                .collect(Collectors.joining("\n"));

        sb.append(stackTrace);
        return sb.toString();
    }

    private String escapeJson(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    private String buildMessage(String message, String stackTrace, String curl, String traceId, String clientIP, ILoggingEvent event) {

        // language=json
        return """
                {
                    "msg_type": "interactive",
                    "card": {
                        "config": {
                            "wide_screen_mode": true,
                            "enable_forward": true,
                            "width_mode": "full"
                        },
                        "elements": [
                            {
                                "tag": "column_set",
                                "flex_mode": "none",
                                "background_style": "default",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "weight": 1,
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": "**追踪 ID**:",
                                                "text_align": "left"
                                            }
                                        ]
                                    },
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "weight": 5,
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": "%s",
                                                "text_align": "left"
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "tag": "column_set",
                                "flex_mode": "none",
                                "background_style": "default",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "weight": 1,
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": "**日志消息**:",
                                                "text_align": "left"
                                            }
                                        ]
                                    },
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "weight": 5,
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": "%s",
                                                "text_align": "left"
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "tag": "column_set",
                                "flex_mode": "none",
                                "background_style": "default",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "weight": 1,
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": "**错误信息**:",
                                                "text_align": "left"
                                            }
                                        ]
                                    },
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "weight": 5,
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": "%s",
                                                "text_align": "left"
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "tag": "column_set",
                                "flex_mode": "none",
                                "background_style": "default",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "weight": 1,
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": "**CURL**:",
                                                "text_align": "left"
                                            }
                                        ]
                                    },
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "weight": 5,
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": "```shell\\n%s\\n```",
                                                "text_align": "left"
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "tag": "hr"
                            },
                            {
                                "tag": "markdown",
                                "content": "```\\n%s\\n```",
                                "text_align": "left"
                            }
                        ],
                        "header": {
                            "title": {
                                "content": "日志报警",
                                "tag": "plain_text"
                            },
                            "template": "red"
                        }
                    }
                }
                """.formatted(
                escapeJson(traceId),
                escapeJson(message),
                escapeJson(event.getThrowableProxy() != null ? event.getThrowableProxy().getMessage() : ""),
                escapeJson(curl),
                escapeJson(stackTrace)
        );
    }
}