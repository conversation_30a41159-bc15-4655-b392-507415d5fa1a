FROM public.ecr.aws/amazoncorretto/amazoncorretto:21

LABEL maintainer="litian <<EMAIL>>" \
      version="1.0" \
      description="Pixelab Dashboard Application"

# 应用配置
ENV APP_ENV=dev TZ=Asia/Shanghai

# JVM配置
ENV JAVA_OPTS=" \
    -javaagent:/app/newrelic/newrelic.jar \
    -Dfile.encoding=utf-8 \
    -Xlog:gc:/app/logs/gc.log \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/app/logs/dump/dump.hprof \
    -XX:MaxRAMPercentage=75.0" \
    EXTRA_JVM_CONFIG="" \
    EXTRA_JAVA_CONFIG=""

WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/logs/dump

# 复制所需文件
COPY target/*.jar app.jar

COPY newrelic /app/newrelic

# 声明
VOLUME ["/app/logs"]

# Spring Boot应用端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=5s --timeout=3s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动命令
ENTRYPOINT ["sh", "-c", "java ${JAVA_OPTS} ${EXTRA_JVM_CONFIG} -jar app.jar --spring.profiles.active=${APP_ENV} ${EXTRA_JAVA_CONFIG}"]