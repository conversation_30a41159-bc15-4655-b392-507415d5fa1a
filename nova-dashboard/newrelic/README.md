# New Relic 使用指南

## 简介

New Relic 是一个强大的应用性能监控（APM）工具，可以帮助我们监控和优化应用程序的性能。本指南将介绍如何在 Nova-Framework
项目中集成和使用 New Relic。

## 安装步骤

### 1. 添加 New Relic Java Agent

1. 从 New Relic 官网下载最新的 Java agent
2. 将 newrelic.jar 放置在项目的 newrelic 目录下

### 2. 配置 newrelic.yml

在 newrelic 目录下创建 `newrelic.yml` 配置文件，包含以下基本配置：

```yaml
common: &default_settings
  license_key: '你的 New Relic license key'
  app_name: Nova-Framework
  distributed_tracing:
    enabled: true
  
  log_level: info
  
  browser_monitoring:
    auto_instrument: true
    
  transaction_tracer:
    enabled: true
    transaction_threshold: apdex_f
    record_sql: obfuscated
    stack_trace_threshold: 0.5

development:
  <<: *default_settings
  app_name: Nova-Framework (Development)

test:
  <<: *default_settings
  app_name: Nova-Framework (Test)

production:
  <<: *default_settings
  app_name: Nova-Framework (Production)
```

### 3. 启动配置

在启动应用时添加以下 JVM 参数：

```bash
-javaagent:/path/to/newrelic/newrelic.jar
```

## 主要功能

### 1. 性能监控

- 应用响应时间
- 吞吐量
- Apdex 评分
- 错误率

### 2. 事务跟踪

- HTTP 请求追踪
- 数据库查询性能
- 外部服务调用

### 3. 错误追踪

- 异常捕获
- 错误堆栈
- 错误上下文

## 最佳实践

1. 确保 license key 不要直接硬编码在配置文件中
2. 对于敏感信息使用环境变量
3. 根据环境（开发、测试、生产）使用不同的配置
4. 定期检查 New Relic 仪表板，关注性能指标
5. 设置适当的告警阈值

## 如何卸载

1. 删除 newrelic 文件
2. 删除 Dockerfile 内部的 newrelic 相关的配置

## 相关资源

- [New Relic Java Agent 文档](https://docs.newrelic.com/docs/apm/agents/java-agent)
- [New Relic API 文档](https://docs.newrelic.com/docs/apis/rest-api-v2)
