# Nova Common

Nova Common 是 Nova Framework 的通用模块集合，提供了一系列可复用的基础功能组件。

## 模块说明

### 1. nova-common-bom

依赖版本管理模块，统一管理框架内所有模块的依赖版本。

### 2. nova-common-core

核心功能模块，提供基础工具类、通用配置、异常处理等核心功能。

### 3. nova-common-web

Web 相关功能模块，提供 Web 开发所需的通用功能：
- 统一响应处理
- 统一异常处理
- 请求日志记录
- 跨域配置
- 参数验证

### 4. nova-common-json

JSON 处理模块，提供 JSON 序列化和反序列化的增强功能。

### 5. nova-common-redis

Redis 工具模块，提供：
- Redis 配置
- 分布式锁
- 缓存操作封装
- Redis 工具类

### 6. nova-common-excel

Excel 处理模块，基于 FastExcel 提供：
- Excel 导入导出
- 注解驱动
- 自定义处理器

### 7. nova-common-satoken

认证授权模块，基于 Sa-Token 实现：
- 用户认证
- 权限控制
- 会话管理
- 单点登录

### 8. nova-common-idempotent

接口幂等性控制模块：
- 注解驱动的幂等控制
- 多种幂等策略支持
- 灵活的配置选项

### 9. nova-common-encrypt

加密解密模块：
- 多种加密算法支持
- 数据脱敏
- 安全工具类

### 10. nova-common-oss

对象存储服务模块：
- AWS S3 协议支持
- 文件上传下载
- 存储桶管理
- 文件访问控制

### 11. nova-common-ip

IP 地址工具模块：
- IP 地址解析
- 地理位置查询
- IP 地址池管理

### 12. nova-common-action-log

操作日志模块：
- 用户操作记录
- 异步日志处理
- 日志查询分析

### 13. nova-common-mybatis

MyBatis 增强模块：
- 通用 CRUD 封装
- 分页查询
- 数据权限
- 多租户支持

### 14. nova-common-translation

多语言翻译模块：
- 注解驱动的字段翻译
- 多种翻译实现
- 翻译缓存
- 灵活的扩展机制

## 使用说明

1. 在项目中引入需要的模块：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-xxx</artifactId>
    <version>${nova.version}</version>
</dependency>
```

2. 配置相关属性：
```yaml
nova:
  # 模块相关配置
  module-name:
    # 具体配置项
```

3. 启用相关功能：
```java
@EnableNovaXxx
public class Application {
    // ...
}
```

## 开发指南

1. 模块开发规范
   - 遵循 Java 开发规范
   - 完善单元测试
   - 编写模块文档

2. 依赖管理
   - 统一使用 BOM 管理版本
   - 避免依赖冲突
   - 控制依赖范围

3. 扩展开发
   - 提供 SPI 扩展点
   - 遵循开闭原则
   - 保持向后兼容

## 注意事项

1. 版本兼容
   - 注意 Spring Boot 版本兼容性
   - 关注依赖更新
   - 遵循语义化版本

2. 性能优化
   - 合理使用缓存
   - 优化数据库访问
   - 控制日志输出

3. 安全建议
   - 及时更新安全补丁
   - 正确使用加密功能
   - 防止敏感信息泄露 