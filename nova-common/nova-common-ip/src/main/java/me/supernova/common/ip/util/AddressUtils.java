package me.supernova.common.ip.util;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.core.enums.CountryCode;

/**
 * 获取地址类
 *
 * <AUTHOR> Li
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AddressUtils {

    // 未知地址
    public static final String UNKNOWN = "未知";

    public static String getAddressByIP(String ip) {
        if (StrUtil.isBlank(ip)) {
            return UNKNOWN;
        }
        // 内网不查询
        ip = StrUtil.contains(ip, "0:0:0:0:0:0:0:1") ? "127.0.0.1" : HtmlUtil.cleanHtmlTag(ip);
        if (NetUtil.isInnerIP(ip)) {
            return "内网IP";
        }
        return RegionUtils.getCountry(ip);
    }

    public static String getCountryCode(String countryCode, String ip) {
        if (StrUtil.isNotBlank(countryCode)) {
            return countryCode;
        }

        return CountryCode.getCodeByName(AddressUtils.getAddressByIP(ip));
    }
}
