package me.supernova.common.core.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 国家码枚举
 *
 * <AUTHOR>
 * @since 1.1.0
 */
@Getter
public enum CountryCode {
    AF("AF", "阿富汗"),
    AX("AX", "奥兰群岛"),
    AL("AL", "阿尔巴尼亚"),
    DZ("DZ", "阿尔及利亚"),
    AS("AS", "美属萨摩亚"),
    AD("AD", "安道尔"),
    AO("AO", "安哥拉"),
    AI("AI", "安圭拉"),
    AQ("AQ", "南极洲"),
    AG("AG", "安提瓜和巴布达"),
    AR("AR", "阿根廷"),
    AM("AM", "亚美尼亚"),
    AW("AW", "阿鲁巴"),
    AU("AU", "澳大利亚"),
    AT("AT", "奥地利"),
    AZ("AZ", "阿塞拜疆"),
    BS("BS", "巴哈马"),
    BH("BH", "巴林"),
    BD("BD", "孟加拉国"),
    BB("BB", "巴巴多斯"),
    BY("BY", "白俄罗斯"),
    BE("BE", "比利时"),
    BZ("BZ", "伯利兹"),
    BJ("BJ", "贝宁"),
    BM("BM", "百慕大"),
    BT("BT", "不丹"),
    BO("BO", "玻利维亚"),
    BQ("BQ", "博奈尔"),
    BA("BA", "波黑"),
    BW("BW", "博茨瓦纳"),
    BV("BV", "布维岛"),
    BR("BR", "巴西"),
    IO("IO", "英属印度洋领地"),
    BN("BN", "文莱"),
    BG("BG", "保加利亚"),
    BF("BF", "布基纳法索"),
    BI("BI", "布隆迪"),
    KH("KH", "柬埔寨"),
    CM("CM", "喀麦隆"),
    CA("CA", "加拿大"),
    CV("CV", "佛得角"),
    KY("KY", "开曼群岛"),
    CF("CF", "中非共和国"),
    TD("TD", "乍得"),
    CL("CL", "智利"),
    CN("CN", "中国"),
    CX("CX", "圣诞岛"),
    CC("CC", "科科斯（基林）群岛"),
    CO("CO", "哥伦比亚"),
    KM("KM", "科摩罗"),
    CD("CD", "刚果（金）"),
    CG("CG", "刚果（布）"),
    CK("CK", "库克群岛"),
    CR("CR", "哥斯达黎加"),
    CI("CI", "科特迪瓦"),
    HR("HR", "克罗地亚"),
    CU("CU", "古巴"),
    CW("CW", "库拉索"),
    CY("CY", "塞浦路斯"),
    CZ("CZ", "捷克"),
    DK("DK", "丹麦"),
    DJ("DJ", "吉布提"),
    DM("DM", "多米尼克"),
    DO("DO", "多米尼加"),
    EC("EC", "厄瓜多尔"),
    EG("EG", "埃及"),
    SV("SV", "萨尔瓦多"),
    GQ("GQ", "赤道几内亚"),
    ER("ER", "厄立特里亚"),
    EE("EE", "爱沙尼亚"),
    ET("ET", "埃塞俄比亚"),
    FK("FK", "福克兰群岛（马尔维纳斯）"),
    FO("FO", "法罗群岛"),
    FJ("FJ", "斐济"),
    FI("FI", "芬兰"),
    FR("FR", "法国"),
    GF("GF", "法属圭亚那"),
    PF("PF", "法属波利尼西亚"),
    TF("TF", "法属南部领地"),
    GA("GA", "加蓬"),
    GM("GM", "冈比亚"),
    GE("GE", "格鲁吉亚"),
    DE("DE", "德国"),
    GH("GH", "加纳"),
    GI("GI", "直布罗陀"),
    GR("GR", "希腊"),
    GL("GL", "格陵兰"),
    GD("GD", "格林纳达"),
    GP("GP", "瓜德罗普"),
    GU("GU", "关岛"),
    GT("GT", "危地马拉"),
    GG("GG", "根西岛"),
    GW("GW", "几内亚比绍"),
    GN("GN", "几内亚"),
    GY("GY", "圭亚那"),
    HT("HT", "海地"),
    HM("HM", "赫德岛和麦克唐纳群岛"),
    VA("VA", "梵蒂冈"),
    HN("HN", "洪都拉斯"),
    HK("HK", "香港"),
    HU("HU", "匈牙利"),
    IS("IS", "冰岛"),
    IN("IN", "印度"),
    ID("ID", "印度尼西亚"),
    IR("IR", "伊朗"),
    IQ("IQ", "伊拉克"),
    IE("IE", "爱尔兰"),
    IM("IM", "马恩岛"),
    IL("IL", "以色列"),
    IT("IT", "意大利"),
    JM("JM", "牙买加"),
    JP("JP", "日本"),
    JE("JE", "泽西岛"),
    JO("JO", "约旦"),
    KZ("KZ", "哈萨克斯坦"),
    KE("KE", "肯尼亚"),
    KI("KI", "基里巴斯"),
    KP("KP", "朝鲜"),
    KR("KR", "韩国"),
    XK("XK", "科索沃"),
    KW("KW", "科威特"),
    KG("KG", "吉尔吉斯斯坦"),
    LA("LA", "老挝"),
    LV("LV", "拉脱维亚"),
    LB("LB", "黎巴嫩"),
    LS("LS", "莱索托"),
    LR("LR", "利比里亚"),
    LY("LY", "利比亚"),
    LI("LI", "列支敦士登"),
    LT("LT", "立陶宛"),
    LU("LU", "卢森堡"),
    MO("MO", "澳门"),
    MK("MK", "马其顿"),
    MG("MG", "马达加斯加"),
    MW("MW", "马拉维"),
    MY("MY", "马来西亚"),
    MV("MV", "马尔代夫"),
    ML("ML", "马里"),
    MT("MT", "马耳他"),
    MH("MH", "马绍尔群岛"),
    MQ("MQ", "马提尼克"),
    MR("MR", "毛里塔尼亚"),
    MU("MU", "毛里求斯"),
    YT("YT", "马约特"),
    MX("MX", "墨西哥"),
    FM("FM", "密克罗尼西亚联邦"),
    MD("MD", "摩尔多瓦"),
    MC("MC", "摩纳哥"),
    MN("MN", "蒙古"),
    ME("ME", "黑山"),
    MS("MS", "蒙特塞拉特"),
    MA("MA", "摩洛哥"),
    MZ("MZ", "莫桑比克"),
    MM("MM", "缅甸"),
    NA("NA", "纳米比亚"),
    NR("NR", "瑙鲁"),
    NP("NP", "尼泊尔"),
    AN("AN", "荷属安的列斯"),
    NL("NL", "荷兰"),
    NC("NC", "新喀里多尼亚"),
    NZ("NZ", "新西兰"),
    NI("NI", "尼加拉瓜"),
    NE("NE", "尼日尔"),
    NG("NG", "尼日利亚"),
    NU("NU", "纽埃"),
    NF("NF", "诺福克岛"),
    MP("MP", "北马里亚纳群岛"),
    NO("NO", "挪威"),
    OM("OM", "阿曼"),
    PK("PK", "巴基斯坦"),
    PW("PW", "帕劳"),
    PS("PS", "巴勒斯坦"),
    PA("PA", "巴拿马"),
    PG("PG", "巴布亚新几内亚"),
    PY("PY", "巴拉圭"),
    PE("PE", "秘鲁"),
    PH("PH", "菲律宾"),
    PN("PN", "皮特凯恩群岛"),
    PL("PL", "波兰"),
    PT("PT", "葡萄牙"),
    PR("PR", "波多黎各"),
    QA("QA", "卡塔尔"),
    RE("RE", "留尼汪"),
    RO("RO", "罗马尼亚"),
    RU("RU", "俄罗斯"),
    RW("RW", "卢旺达"),
    BL("BL", "圣巴泰勒米"),
    SH("SH", "圣赫勒拿"),
    KN("KN", "圣基茨和尼维斯"),
    LC("LC", "圣卢西亚"),
    MF("MF", "法属圣马丁"),
    PM("PM", "圣皮埃尔和密克隆"),
    VC("VC", "圣文森特和格林纳丁斯"),
    WS("WS", "萨摩亚"),
    SM("SM", "圣马力诺"),
    ST("ST", "圣多美和普林西比"),
    SA("SA", "沙特阿拉伯"),
    SN("SN", "塞内加尔"),
    RS("RS", "塞尔维亚"),
    SC("SC", "塞舌尔"),
    SL("SL", "塞拉利昂"),
    SG("SG", "新加坡"),
    SX("SX", "荷属圣马丁"),
    SK("SK", "斯洛伐克"),
    SI("SI", "斯洛文尼亚"),
    SB("SB", "所罗门群岛"),
    SO("SO", "索马里"),
    ZA("ZA", "南非"),
    GS("GS", "南乔治亚和南桑德韦奇群岛"),
    SS("SS", "南苏丹"),
    ES("ES", "西班牙"),
    LK("LK", "斯里兰卡"),
    SD("SD", "苏丹"),
    SR("SR", "苏里南"),
    SJ("SJ", "斯瓦尔巴和扬马延"),
    SZ("SZ", "斯威士兰"),
    SE("SE", "瑞典"),
    CH("CH", "瑞士"),
    SY("SY", "叙利亚"),
    TW("TW", "台湾"),
    TJ("TJ", "塔吉克斯坦"),
    TZ("TZ", "坦桑尼亚"),
    TH("TH", "泰国"),
    TL("TL", "东帝汶"),
    TG("TG", "多哥"),
    TK("TK", "托克劳"),
    TO("TO", "汤加"),
    TT("TT", "特立尼达和多巴哥"),
    TN("TN", "突尼斯"),
    TR("TR", "土耳其"),
    TM("TM", "土库曼斯坦"),
    TC("TC", "特克斯和凯科斯群岛"),
    TV("TV", "图瓦卢"),
    UG("UG", "乌干达"),
    UA("UA", "乌克兰"),
    AE("AE", "阿联酋"),
    GB("GB", "英国"),
    UM("UM", "美国本土外小岛屿"),
    US("US", "美国"),
    UY("UY", "乌拉圭"),
    UZ("UZ", "乌兹别克斯坦"),
    VU("VU", "瓦努阿图"),
    VE("VE", "委内瑞拉"),
    VN("VN", "越南"),
    VG("VG", "英属维尔京群岛"),
    VI("VI", "美属维尔京群岛"),
    WF("WF", "瓦利斯和富图纳"),
    EH("EH", "西撒哈拉"),
    YE("YE", "也门"),
    ZM("ZM", "赞比亚"),
    ZW("ZW", "津巴布韦");

    private static final Map<String, String> CODE_TO_NAME_MAP = new HashMap<>();
    private static final Map<String, String> NAME_TO_CODE_MAP = new HashMap<>();

    static {
        for (CountryCode countryCode : values()) {
            CODE_TO_NAME_MAP.put(countryCode.getCode().toLowerCase(), countryCode.getName());
            NAME_TO_CODE_MAP.put(countryCode.getName().toLowerCase(), countryCode.getCode());
        }
    }

    private final String code;
    private final String name;

    CountryCode(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        return CODE_TO_NAME_MAP.get(code.toLowerCase());
    }

    public static String getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        return NAME_TO_CODE_MAP.getOrDefault(name.toLowerCase(), name);
    }
}