package me.supernova.common.core.utils;

import cn.hutool.core.collection.CollUtil;
import org.springframework.util.AntPathMatcher;

import java.util.List;

public class MatchUtil {

    public static final AntPathMatcher ANT_PATH_MATCHER = new AntPathMatcher();

    public static boolean match(String uri, String pattern) {
        return ANT_PATH_MATCHER.match(pattern, uri);
    }

    public static boolean isAnyMatch(String uri, String[] patterns) {
        if (patterns == null) {
            return false;
        }
        for (String pattern : patterns) {
            if (match(uri, pattern)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isAnyMatch(String uri, List<String> patterns) {
        if (CollUtil.isEmpty(patterns)) {
            return false;
        }
        for (String pattern : patterns) {
            if (match(uri, pattern)) {
                return true;
            }
        }
        return false;
    }
}
