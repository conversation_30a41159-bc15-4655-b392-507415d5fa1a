package me.supernova.common.core.utils;

import cn.hutool.extra.spring.SpringUtil;

public class EnvUtil {

    /**
     * 是否为生产环境
     *
     * @return 是否生产环境
     */
    public static boolean isProd() {
        String activeProfile = SpringUtil.getActiveProfile();
        return "prod".equals(activeProfile);
    }

    /**
     * 是否为开发环境
     *
     * @return 是否开发环境
     */
    public static boolean isDev() {
        String activeProfile = SpringUtil.getActiveProfile();
        return "dev".equals(activeProfile);
    }

    /**
     * 是否为预发布环境
     *
     * @return 是否预发布环境
     */
    public static boolean isPre() {
        String activeProfile = SpringUtil.getActiveProfile();
        return "pre".equals(activeProfile);
    }

}
