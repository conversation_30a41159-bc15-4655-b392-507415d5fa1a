package me.supernova.common.core.filter;
import cn.hutool.core.io.IoUtil;
import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 可重复读取请求体的包装器
 * 支持文件流直接透传，非文件流可重复读取
 *
 * <AUTHOR>
 * @since 2.10.0
 */
public class RepeatReadRequestWrapper extends HttpServletRequestWrapper {

    private byte[] cachedBody;
    private final HttpServletRequest originalRequest;

    public RepeatReadRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        this.originalRequest = request;

        // 判断是否为文件上传请求
        if (!isMultipartContent(request)) {
            this.cachedBody = IoUtil.readBytes(request.getInputStream(), false);
        }
    }

    /**
     * 获取请求体内容（字符串形式）
     *
     * @return 请求体内容，如果是文件上传请求则返回null
     */
    public String getRequestContent() {
        if (isMultipartContent(originalRequest) || cachedBody == null) {
            return null;
        }
        return new String(cachedBody, StandardCharsets.UTF_8);
    }

    /**
     * 设置新的请求体内容
     *
     * @param content 新的请求体内容
     */
    public void setRequestContent(String content) {
        if (isMultipartContent(originalRequest)) {
            return;
        }
        if (content != null) {
            this.cachedBody = content.getBytes(StandardCharsets.UTF_8);
        }
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        // 如果是文件上传，直接返回原始输入流
        if (isMultipartContent(originalRequest)) {
            return originalRequest.getInputStream();
        }

        // 非文件上传，返回可重复读取的输入流
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(cachedBody);

        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return byteArrayInputStream.available() == 0;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
                // 非阻塞I/O，这里可以根据需要实现
            }

            @Override
            public int read() {
                return byteArrayInputStream.read();
            }
        };
    }

    @Override
    public BufferedReader getReader() throws IOException {
        // 如果是文件上传，直接返回原始Reader
        if (isMultipartContent(originalRequest)) {
            new BufferedReader(new InputStreamReader(originalRequest.getInputStream(), StandardCharsets.UTF_8));
        }
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    /**
     * 检查是否为文件上传请求
     *
     * @param request 请求对象
     * @return 是否为文件上传请求
     */
    public boolean isMultipartContent(HttpServletRequest request) {
        return request.getContentType() != null && request.getContentType().toLowerCase().startsWith("multipart/");
    }
}
