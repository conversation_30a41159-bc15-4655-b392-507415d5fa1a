package me.supernova.common.core.utils;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class LocalIpUtil {
    public static Map<String, String> getNetworkIps() {
        Map<String, String> networkIps = new HashMap<>();
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface iface = interfaces.nextElement();
                // 过滤非活动接口和回环接口
                if (iface.isLoopback() || !iface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> addresses = iface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    // 只获取IPv4地址
                    if (addr instanceof Inet4Address) {
                        networkIps.put(iface.getDisplayName(), addr.getHostAddress());
                    }
                }
            }
        } catch (SocketException e) {
            networkIps.put("error", "无法获取IP地址");
        }
        return networkIps;
    }

    /**
     * 获取主要的对外 IP 地址（同时支持主机和 Docker 环境）
     * @return 返回可供外部访问的 IP 地址，如果未找到则返回 null
     */
    public static String getMainIp() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface iface = interfaces.nextElement();
                if (iface.isLoopback() || !iface.isUp()) {
                    continue;
                }

                String name = iface.getDisplayName().toLowerCase();
                // Docker 容器内部，通常使用 eth0 作为主网卡
                if (name.equals("eth0")) {
                    Enumeration<InetAddress> addresses = iface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress addr = addresses.nextElement();
                        if (addr instanceof Inet4Address) {
                            return addr.getHostAddress();
                        }
                    }
                }

                // 如果不是 Docker 环境，使用原来的逻辑
                if (!name.contains("docker") && !name.contains("vmnet")) {
                    if (name.startsWith("en")) {
                        Enumeration<InetAddress> addresses = iface.getInetAddresses();
                        while (addresses.hasMoreElements()) {
                            InetAddress addr = addresses.nextElement();
                            if (addr instanceof Inet4Address) {
                                return addr.getHostAddress();
                            }
                        }
                    }
                }
            }

            // 如果上述都没找到，返回第一个非回环的 IPv4 地址
            interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface iface = interfaces.nextElement();
                if (iface.isLoopback() || !iface.isUp()) {
                    continue;
                }
                Enumeration<InetAddress> addresses = iface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    if (addr instanceof Inet4Address) {
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            return null;
        }
        return null;
    }
}
