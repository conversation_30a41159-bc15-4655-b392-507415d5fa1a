# Nova Common Core 模块

Nova Common Core 是 Nova Framework 的核心通用模块，提供了框架所需的基础工具类和配置。该模块作为其他模块的基础依赖，提供了大量可复用的功能组件。

## 主要功能

### 1. 配置类
- `ApplicationConfig`: 提供基础的应用程序配置，启用了AOP和异步支持
- `ThreadPoolConfig`: 线程池配置
- `ValidatorConfig`: 验证器配置

### 2. 工具类集合

#### 2.1 集合与数据处理
- `CollectionUtils`: 提供丰富的集合操作工具方法
- `StreamUtils`: Stream流式处理工具类
- `ObjectUtils`: 对象工具类，提供对象判空、获取等操作

#### 2.2 Web相关
- `ServletUtils`: Servlet工具类，处理HTTP请求参数
- `SpringUtils`: Spring容器工具类，提供Bean操作
- `ValidatorUtils`: 参数校验工具类

#### 2.3 系统与网络
- `LocalIpUtil`: 本地IP地址工具类
- `ThreadUtils`: 线程相关工具类
- `MatchUtil`: 路径匹配工具类，支持Ant风格路径匹配

#### 2.4 安全相关
- `SqlUtil`: SQL注入防护工具类
- `MimeTypeUtils`: 媒体类型工具类

#### 2.5 反射与类型转换
- `ReflectUtils`: 反射工具类，支持getter/setter方法调用

### 3. 常量定义
- `Constants`: 系统通用常量
- `UserConstants`: 用户相关常量

## 特性
1. 提供完整的基础工具类支持
2. 统一的异常处理机制
3. 标准化的参数验证
4. 线程池管理
5. Spring容器增强

## 使用方式

1. 在项目中引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-core</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 自动配置：
模块使用Spring Boot的自动配置机制，在`META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`中定义了自动装配类：
- ApplicationConfig
- AsyncConfig
- ThreadPoolConfig
- ValidatorConfig
- SpringUtils

## 注意事项
1. 该模块为基础核心模块，被其他模块广泛依赖，修改时需谨慎
2. 工具类都采用了单例模式，通过私有构造器防止实例化
3. 所有工具类都提供了静态方法，可直接调用
4. 配置类都使用了`@AutoConfiguration`注解，支持自动装配 