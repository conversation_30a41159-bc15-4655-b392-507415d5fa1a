# Nova Common JSON 模块

Nova Common JSON 是 Nova Framework 的 JSON 处理模块，基于 Jackson 和 Hutool-JSON 实现，提供了强大的 JSON 序列化和反序列化功能。

## 主要功能

### 1. <PERSON> 配置增强
- 自定义 Jackson 配置
  - 时间类型处理
  - 大数字处理
  - 时区处理
  - 序列化规则定制

### 2. JSON 工具类
- `JsonUtils`: 全功能 JSON 处理工具
  - 对象转 JSON 字符串
  - JSON 字符串转对象
  - 集合类型转换
  - 复杂类型支持
  - 异常处理

### 3. 大数字处理
- `BigNumberSerializer`: 大数字序列化处理器
  - 处理超出 JavaScript 安全整数范围的数字
  - 自动转换为字符串类型
  - 保证精度不丢失

### 4. 时间类型处理
- 支持 Java 8 时间类型
  - LocalDateTime
  - LocalDate
  - LocalTime
- 统一的时间格式化
- 时区处理

## 配置示例

### Jackson 全局配置
```yaml
spring:
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    # 时区设置
    time-zone: GMT+8
    # 序列化配置
    serialization:
      # 格式化输出
      indent_output: true
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    # 反序列化配置
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false
    # 设置空如何序列化
    defaultPropertyInclusion: NON_NULL
    # 允许出现特殊字符和转义符
    parser:
      allow_unquoted_control_chars: true
      allow_single_quotes: true
```

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-json</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. JSON 工具类使用示例：
```java
// 对象转 JSON 字符串
String jsonString = JsonUtils.toJsonString(object);

// JSON 字符串转对象
User user = JsonUtils.parseObject(jsonString, User.class);

// JSON 字符串转集合
List<User> users = JsonUtils.parseArray(jsonString, User.class);

// 复杂类型转换
Map<String, List<User>> map = JsonUtils.parseObject(jsonString, 
    new TypeReference<Map<String, List<User>>>() {});

// Dict 类型转换
Dict dict = JsonUtils.parseMap(jsonString);
```

3. 大数字处理：
```java
// 自动处理大数字
@JsonSerialize(using = BigNumberSerializer.class)
private Long id;
```

## 特性

1. 统一的序列化规则
- 标准的日期时间格式
- 统一的数字处理
- 一致的空值处理

2. 高性能
- 基于 Jackson 实现
- 支持对象复用
- 高效的序列化和反序列化

3. 类型安全
- 类型自动转换
- 精度保护
- 异常处理

## 注意事项

1. 大数字处理
- JavaScript 安全整数范围：[-9007199254740991, 9007199254740991]
- 超出范围的数字会自动转为字符串
- 注意前端处理方式

2. 日期时间处理
- 统一使用 ISO-8601 格式
- 注意时区设置
- 考虑夏令时影响

3. 性能优化
- 复用 ObjectMapper
- 避免频繁创建对象
- 合理使用类型转换

4. 安全性
- 注意反序列化安全
- 处理特殊字符
- 防止 JSON 注入

5. 兼容性
- 考虑不同版本的兼容
- 处理字段变更
- 注意向后兼容 