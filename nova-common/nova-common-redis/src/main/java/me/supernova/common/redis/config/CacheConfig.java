package me.supernova.common.redis.config;

import me.supernova.common.redis.manager.SpringCacheManager;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;

/**
 * 缓存配置
 *
 * <AUTHOR>
 */
@AutoConfiguration
@EnableCaching
public class CacheConfig {

    /**
     * 自定义缓存管理器 整合spring-cache
     */
    @Bean
    public CacheManager cacheManager() {
        return new SpringCacheManager();
    }

}
