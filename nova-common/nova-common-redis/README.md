# Nova Common Redis 模块

Nova Common Redis 是 Nova Framework 的 Redis 集成模块，基于 Redisson 实现，提供了强大的缓存功能和分布式特性支持。

## 主要功能

### 1. Redis 配置
- `RedisConfig`: 提供 Redis 的核心配置，支持单机和集群模式
- `CacheConfig`: Spring Cache 的配置，实现了基于 Redis 的缓存功能
- 支持 Redis Key 前缀配置，便于多环境或多应用隔离

### 2. 缓存管理
- `SpringCacheManager`: 自定义的缓存管理器，支持多参数配置
  - 支持 TTL（存活时间）
  - 支持 MaxIdleTime（最大空闲时间）
  - 支持 MaxSize（最大缓存数量）
  - 示例：`demo:cache#10m#5m#20` (10分钟过期，5分钟空闲过期，最多20个元素)

### 3. 工具类

#### 3.1 Redis 操作工具
- `RedisUtils`: 提供丰富的 Redis 操作方法
  - 基础对象操作（get/set/delete）
  - 列表操作（List）
  - 集合操作（Set）
  - 哈希操作（Hash）
  - 原子操作
  - 发布订阅
  - 限流功能

#### 3.2 缓存工具
- `CacheUtils`: Spring Cache 的操作封装
  - 获取缓存
  - 设置缓存
  - 删除缓存
  - 清空缓存

### 4. 性能优化
- 集成 Caffeine 作为一级缓存
- 支持异步批处理操作
- 支持 Redis 集群配置

### 5. 异常处理
- `RedisExceptionHandler`: 统一的 Redis 异常处理
- 分布式锁异常处理

## 配置示例

### 单机配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: your_password
      database: 0

redisson:
  # 线程池数量
  threads: 16
  # Netty线程池数量
  nettyThreads: 32
  # key前缀
  keyPrefix: "nova"
  # 单机服务配置
  singleServerConfig:
    # 客户端名称
    clientName: ${spring.application.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 64
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
```

### 集群配置
```yaml
spring:
  data:
    redis:
      cluster:
        nodes:
          - *************:6379
          - *************:6379
          - *************:6379
      password: your_password

redisson:
  threads: 16
  nettyThreads: 32
  keyPrefix: "nova"
  clusterServersConfig:
    clientName: ${spring.application.name}
    masterConnectionMinimumIdleSize: 32
    masterConnectionPoolSize: 64
    slaveConnectionMinimumIdleSize: 32
    slaveConnectionPoolSize: 64
    idleConnectionTimeout: 10000
    timeout: 3000
    subscriptionConnectionPoolSize: 50
    readMode: "SLAVE"
    subscriptionMode: "MASTER"
```

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-redis</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 自动配置：
模块使用 Spring Boot 的自动配置机制，在 `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` 中定义了自动装配类：
- CacheConfig
- RedisConfig

## 注意事项
1. 建议在生产环境使用集群模式以提高可用性
2. 合理配置连接池参数，避免连接数过多或过少
3. 使用 key 前缀避免多应用间的键冲突
4. 合理使用一级缓存（Caffeine）来减轻 Redis 压力
5. 注意处理分布式锁的异常情况 