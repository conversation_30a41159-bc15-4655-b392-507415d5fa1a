package me.supernova.common.satoken.config;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import me.supernova.common.satoken.config.properties.SecurityProperties;
import me.supernova.common.satoken.core.dao.RedisSaTokenDao;
import me.supernova.common.satoken.core.service.SaPermissionImpl;
import me.supernova.common.satoken.handler.SaTokenExceptionHandler;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * sa-token 配置
 *
 * <AUTHOR> Li
 */
@AutoConfiguration
@RequiredArgsConstructor
@EnableConfigurationProperties(SecurityProperties.class)
public class SaTokenAutoConfiguration implements WebMvcConfigurer {

    private final SecurityProperties securityProperties;

    private final SaTokenConfig saTokenConfig;

    /**
     * 把sa-token的拦截器注入到Spring
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(saInterceptor()).addPathPatterns("/**");
    }

    /**
     * 权限接口实现(使用bean注入方便用户替换)
     */
    @Bean
    public StpInterface stpInterface() {
        return new SaPermissionImpl();
    }

    /**
     * 自定义dao层存储
     */
    @Bean
    public SaTokenDao saTokenDao() {
        return new RedisSaTokenDao();
    }

    /**
     * 异常处理器
     */
    @Bean
    public SaTokenExceptionHandler saTokenExceptionHandler() {
        return new SaTokenExceptionHandler();
    }


    /**
     * SaToken 拦截器配置
     */
    public SaInterceptor saInterceptor() {
        return new SaInterceptor(handle ->
                SaRouter.match("/**")
                        .notMatch(securityProperties.getExcludes())
                        .notMatch("/v3/api-docs")
                        .notMatch("/auth/logout")
                        .check(r -> {
                            StpUtil.checkLogin();
                            long sessionTimeout = StpUtil.getSessionTimeout();
                            if (sessionTimeout <= 60 * 60 * 24) {
                                // 如果session过期时间小于24小时，自动续期
                                StpUtil.renewTimeout(saTokenConfig.getTimeout());
                            }
                        }));
    }
}
