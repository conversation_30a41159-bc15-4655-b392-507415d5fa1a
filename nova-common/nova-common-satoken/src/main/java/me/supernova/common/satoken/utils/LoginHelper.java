package me.supernova.common.satoken.utils;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.spring.SaTokenContextForSpringInJakartaServlet;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import me.supernova.common.core.constant.UserConstants;
import me.supernova.common.core.domain.model.LoginUser;
import me.supernova.common.core.enums.UserType;

import java.util.Optional;

/**
 * 登录鉴权助手
 * <p>
 * user_type 为 用户类型 同一个用户表 可以有多种用户类型 例如 pc,app
 * device 为 设备类型 同一个用户类型 可以有 多种设备类型 例如 web,ios
 * 可以组成 用户类型与设备类型多对多的 权限灵活控制
 * <p>
 * 多用户体系 针对 多种用户类型 但权限控制不一致
 * 可以组成 多用户类型表与多设备类型 分别控制权限
 * <p>
 * <p>
 * 注意:这个 loginHelper 是基于 sa-token 的
 * sa-token 通过 RequestContextHolder 获取 request,然后在 request 中找 token
 * 最后通过 token 在到 session(存储到了 redis 中) 中获取用户信息
 * 所以这个有一点限制,就是不能用 transmittable-thread-local 传递 request
 * 如果想要传递 request,可以自己实现SaTokenContext,然后存储 request 到 transmittable-thread-local中
 *
 * <AUTHOR> Li
 * @see SaTokenContextForSpringInJakartaServlet
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LoginHelper {

    public static final String LOGIN_USER_KEY = "loginUser";

    /**
     * 登录系统 基于 设备类型
     * 针对相同用户体系不同设备
     *
     * @param loginUser 登录用户信息
     */
    public static void login(LoginUser loginUser) {
        StpUtil.login(loginUser.getLoginId());
        StpUtil.getTokenSession().set(LOGIN_USER_KEY, loginUser);
    }

    /**
     * 获取用户(多级缓存)
     */
    public static Optional<LoginUser> getLoginUserOpt() {
        if (!StpUtil.isLogin()) {
            return Optional.empty();
        }
        SaSession session = StpUtil.getTokenSession();
        if (ObjectUtil.isNull(session)) {
            return Optional.empty();
        }
        return Optional.ofNullable((LoginUser) session.get(LOGIN_USER_KEY));
    }

    public static LoginUser getLoginUser() {
        return getLoginUserOpt().orElseThrow(() -> new NotLoginException(NotLoginException.DEFAULT_MESSAGE, "", ""));
    }

    /**
     * 获取用户基于token
     */
    public static Optional<LoginUser> getLoginUserOpt(String token) {
        SaSession session = StpUtil.getTokenSessionByToken(token);
        if (ObjectUtil.isNull(session)) {
            return Optional.empty();
        }
        return Optional.ofNullable((LoginUser) session.get(LOGIN_USER_KEY));
    }

    /**
     * 获取用户id
     */
    public static Long getUserId() {
        return getLoginUser().getUserId();
    }

    /**
     * 获取用户账户
     */
    public static String getUsername() {
        return getLoginUser().getUsername();
    }

    /**
     * 获取部门ID
     */
    public static Long getDeptId() {
        return getLoginUser().getDeptId();
    }

    /**
     * 获取部门名
     */
    public static String getDeptName() {
        return getLoginUser().getDeptName();
    }

    /**
     * 获取用户类型
     */
    public static UserType getUserType() {
        String loginType = StpUtil.getLoginIdAsString();
        return UserType.getUserType(loginType);
    }

    /**
     * 是否为超级管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isSuperAdmin(Long userId) {
        return UserConstants.SUPER_ADMIN_ID.equals(userId);
    }

    /**
     * 是否为超级管理员
     *
     * @return 结果
     */
    public static boolean isSuperAdmin() {
        return isSuperAdmin(getUserId());
    }
}
