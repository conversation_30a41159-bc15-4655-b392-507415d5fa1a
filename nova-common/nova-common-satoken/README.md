# Nova Common SaToken 模块

Nova Common SaToken 是 Nova Framework 的认证授权模块，基于 Sa-Token 框架实现，提供了完整的认证、授权和权限控制功能。

## 主要功能

### 1. 认证授权
- 基于 Sa-Token 实现的认证授权系统
- 支持多用户体系（如管理员、普通用户等）
- 支持多端登录（如 Web、App 等）
- 支持会话管理和自动续期

### 2. 权限控制
- `SaPermissionImpl`: 权限验证实现
  - 菜单权限控制
  - 角色权限控制
  - 支持不同用户类型的权限管理

### 3. 数据存储
- `RedisSaTokenDao`: 基于 Redis 的数据持久化
  - 使用 Caffeine + Redis 多级缓存
  - 优化并发查询效率
  - 支持数据自动过期

### 4. 工具类
- `LoginHelper`: 登录鉴权助手
  - 用户登录管理
  - 会话信息获取
  - 用户信息缓存
  - 超级管理员判断

### 5. 异常处理
- `SaTokenExceptionHandler`: 统一的异常处理
  - 处理权限码异常
  - 处理角色权限异常
  - 处理认证失败异常

## 配置示例

```yaml
# Sa-Token 配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token有效期 1天
  timeout: 86400
  # token临时有效期 (指定时间内无操作就过期) 1天
  activity-timeout: 86400
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: false
  # 是否从cookie中读取token
  is-read-cookie: false
  # 是否从head中读取token
  is-read-head: true

# 安全配置
security:
  # 排除路径
  excludes:
    - /auth/login
    - /auth/register
    - /*/v3/api-docs
    - /csrf
```

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-satoken</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 自动配置：
模块使用 Spring Boot 的自动配置机制，在 `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` 中定义了自动装配类：
- SaTokenAutoConfiguration

3. 基本使用示例：

```java
// 登录
LoginHelper.login(loginUser);

// 获取当前登录用户信息
LoginUser loginUser = LoginHelper.getLoginUser();

// 获取用户ID
Long userId = LoginHelper.getUserId();

// 判断是否为超级管理员
boolean isSuperAdmin = LoginHelper.isSuperAdmin();

// 注解式鉴权
@SaCheckLogin                // 登录校验
@SaCheckRole("admin")       // 角色校验
@SaCheckPermission("user:add") // 权限校验
```

## 特性

1. 多级缓存
- 使用 Caffeine 作为一级缓存
- Redis 作为二级缓存
- 优化高并发场景下的性能

2. 会话管理
- 支持会话自动续期
- 可配置会话并发登录
- 支持会话共享配置

3. 权限模型
- 支持多用户类型
- 支持多设备类型
- 灵活的权限控制策略

## 注意事项

1. 缓存配置
- Caffeine 缓存默认配置：
  - 写入后5秒过期
  - 初始容量100
  - 最大容量1000

2. 安全配置
- 合理配置排除路径
- 注意处理跨域请求
- 建议使用 HTTPS

3. 性能优化
- 合理使用多级缓存
- 避免频繁的权限检查
- 适当配置会话超时时间

4. 权限设计
- 根据业务需求设计权限体系
- 注意权限粒度控制
- 合理使用角色和权限组合 