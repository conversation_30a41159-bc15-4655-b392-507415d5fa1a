<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>me.supernova</groupId>
        <artifactId>nova-common</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>nova-common-satoken</artifactId>
    <name>nova-common-satoken</name>
    <description>nova-common-satoken</description>

    <properties>
        <java.version>21</java.version>
    </properties>


    <dependencies>

        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-redis</artifactId>
        </dependency>

        <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

    </dependencies>

</project>
