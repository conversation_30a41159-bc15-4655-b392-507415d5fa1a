<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-bom</artifactId>
    <version>${revision}</version>
    <name>nova-common-bom</name>
    <description>nova-common-bom</description>
    <packaging>pom</packaging>

    <properties>
        <java.version>21</java.version>
        <revision>0.0.1-SNAPSHOT</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 幂等 -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- OSS -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- satoken -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 请求加解密模块 -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- ip 地址模块 -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-ip</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 行为日志模块 -->
            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-action-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>me.supernova</groupId>
                <artifactId>nova-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
