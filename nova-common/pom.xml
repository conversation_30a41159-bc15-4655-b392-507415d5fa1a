<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>me.supernova</groupId>
        <artifactId>NovaFramework</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>nova-common</artifactId>
    <name>nova-common</name>
    <description>common 通用模块</description>
    <packaging>pom</packaging>
    <modules>
        <module>nova-common-bom</module>
        <module>nova-common-core</module>
        <module>nova-common-web</module>
        <module>nova-common-json</module>
        <module>nova-common-redis</module>
        <module>nova-common-excel</module>
        <module>nova-common-satoken</module>
        <module>nova-common-idempotent</module>
        <module>nova-common-encrypt</module>
        <module>nova-common-oss</module>
        <module>nova-common-ip</module>
        <module>nova-common-action-log</module>
        <module>nova-common-mybatis</module>
        <module>nova-common-translation</module>
    </modules>

</project>
