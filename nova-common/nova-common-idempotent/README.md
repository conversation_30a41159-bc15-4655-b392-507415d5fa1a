# Nova Common Idempotent 模块

Nova Common Idempotent 是 Nova Framework 的幂等性控制模块，基于 Redis 实现，提供了防止重复提交的功能。该模块参考了美团 GTIS 防重系统的设计思路。

## 主要功能

### 1. 防重复提交
- 基于注解的防重复提交控制
- 支持自定义时间间隔
- 支持自定义提示消息
- 支持国际化消息配置

### 2. 幂等性控制
- 基于 Redis 的分布式锁实现
- 支持多种时间单位配置
- 自动清理过期数据
- 异常情况自动释放锁

### 3. 请求识别
- 支持多种请求参数组合
- 自动过滤特殊类型参数
- MD5 签名保证唯一性
- 支持自定义请求头标识

### 4. 异常处理
- 统一的异常处理机制
- 支持自定义异常提示
- 异常情况自动清理数据
- 防止资源泄露

## 配置示例

### 1. 启用幂等性控制
```java
@Configuration
@EnableIdempotent
public class IdempotentConfig {
    // 配置项
}
```

### 2. 注解使用示例
```java
@PostMapping("/submit")
@RepeatSubmit(interval = 5, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
public R<String> submit(@RequestBody SubmitForm form) {
    // 业务处理
    return R.ok();
}
```

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-idempotent</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 使用 `@RepeatSubmit` 注解：
```java
@RestController
@RequestMapping("/api")
public class DemoController {
    
    // 默认 5 秒内不允许重复提交
    @RepeatSubmit
    @PostMapping("/create")
    public R<String> create(@RequestBody CreateRequest request) {
        return R.ok();
    }
    
    // 自定义间隔时间和提示消息
    @RepeatSubmit(interval = 10, timeUnit = TimeUnit.SECONDS, 
                 message = "{custom.repeat.submit.message}")
    @PostMapping("/update")
    public R<String> update(@RequestBody UpdateRequest request) {
        return R.ok();
    }
}
```

3. 配置国际化消息（可选）：
```properties
# messages.properties
custom.repeat.submit.message=系统正在处理您的请求，请勿重复提交
```

## 特性

1. 高性能
- 基于 Redis 实现
- 轻量级注解方式
- 最小化性能影响

2. 可靠性
- 分布式环境支持
- 异常自动处理
- 资源自动释放

3. 易用性
- 注解驱动设计
- 简单的配置方式
- 灵活的自定义选项

## 注意事项

1. Redis 配置
- 确保 Redis 服务可用
- 合理配置连接池
- 监控 Redis 性能

2. 时间间隔设置
- 根据业务场景设置合理的时间间隔
- 不建议设置过短的时间间隔（<1秒）
- 考虑网络延迟影响

3. 异常处理
- 注意捕获和处理异常
- 确保锁的正确释放
- 避免死锁情况

4. 性能考虑
- 合理使用注解
- 避免过多的重复提交检查
- 监控系统性能影响 