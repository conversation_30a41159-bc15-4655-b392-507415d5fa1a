<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>me.supernova</groupId>
        <artifactId>nova-common</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>nova-common-action-log</artifactId>
    <description>nova-common-action-log</description>

    <properties>
        <java.version>21</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-ip</artifactId>
        </dependency>

        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-json</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
    </dependencies>

</project>
