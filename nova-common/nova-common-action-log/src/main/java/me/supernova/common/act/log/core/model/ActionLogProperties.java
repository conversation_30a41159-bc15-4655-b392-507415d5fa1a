package me.supernova.common.act.log.core.model;

import lombok.Data;
import me.supernova.common.act.log.core.enums.Include;
import me.supernova.common.core.utils.MatchUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 日志配置属性
 *
 * <AUTHOR>
 * @since 1.1.0
 */
@Data
@ConfigurationProperties("action-log")
public class ActionLogProperties {

    /**
     * 是否启用日志
     */
    private boolean enabled = true;

    /**
     * 是否启用CURL命令生成
     */
    private boolean enableCurl = false;

    /**
     * 是否打印日志，开启后可打印访问日志（类似于 Nginx access log）
     * <p>
     * 不记录日志也支持开启打印访问日志
     * </p>
     */
    private Boolean isPrint = false;

    /**
     * 包含信息
     */
    private Set<Include> includes = Include.defaultIncludes();

    /**
     * 放行路由
     */
    private List<String> excludePatterns = new ArrayList<>();

    /**
     * 是否匹配放行路由
     *
     * @param uri 请求 URI
     * @return 是否匹配
     */
    public boolean isMatch(String uri) {
        return MatchUtil.isAnyMatch(uri, this.excludePatterns);
    }
}
