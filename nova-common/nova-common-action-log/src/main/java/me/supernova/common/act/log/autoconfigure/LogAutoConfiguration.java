package me.supernova.common.act.log.autoconfigure;

import lombok.extern.slf4j.Slf4j;
import me.supernova.common.act.log.core.ActionLogFilter;
import me.supernova.common.act.log.core.ActionLogHandler;
import me.supernova.common.act.log.core.annotation.ConditionalOnEnabledLog;
import me.supernova.common.act.log.core.dao.ActionLogDao;
import me.supernova.common.act.log.core.dao.impl.DefaultActionLogDaoImpl;
import me.supernova.common.act.log.core.model.ActionLogProperties;
import me.supernova.common.act.log.handler.InterceptorActionLogHandler;
import me.supernova.common.act.log.interceptor.ActionLogInterceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 日志自动配置
 *
 * <AUTHOR>
 * @since 1.1.0
 */
@Slf4j
@Configuration
@ConditionalOnEnabledLog
@EnableConfigurationProperties(ActionLogProperties.class)
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public class LogAutoConfiguration implements WebMvcConfigurer {

    private final ActionLogProperties actionLogProperties;

    public LogAutoConfiguration(ActionLogProperties actionLogProperties) {
        this.actionLogProperties = actionLogProperties;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ActionLogInterceptor(actionLogProperties, logHandler(), logDao()));
    }

    /**
     * 日志过滤器
     */
    @Bean
    @ConditionalOnMissingBean
    public ActionLogFilter logFilter() {
        return new ActionLogFilter(actionLogProperties);
    }

    /**
     * 日志处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public ActionLogHandler logHandler() {
        return new InterceptorActionLogHandler();
    }

    /**
     * 日志持久层接口
     */
    @Bean
    @ConditionalOnMissingBean
    public ActionLogDao logDao() {
        return new DefaultActionLogDaoImpl();
    }

}
