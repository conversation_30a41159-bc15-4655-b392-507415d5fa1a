package me.supernova.common.act.log.core;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.text.CharSequenceUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.act.log.core.enums.Include;
import me.supernova.common.act.log.core.http.servlet.RecordableServletHttpRequest;
import me.supernova.common.act.log.core.http.servlet.RecordableServletHttpResponse;
import me.supernova.common.act.log.core.model.LogRecord;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 日志处理器基类
 *
 * <AUTHOR>
 * @since 2.8.0
 */
public abstract class AbstractActionLogHandler implements ActionLogHandler {

    @Override
    public LogRecord.Started start(LocalDateTime startTime, HttpServletRequest request) {
        return LogRecord.start(startTime, new RecordableServletHttpRequest(request));
    }

    @Override
    public LogRecord finish(LogRecord.Started started,
                            LocalDateTime endTime,
                            HttpServletResponse response,
                            Set<Include> includes,
                            Method targetMethod,
                            Class<?> targetClass) {
        Set<Include> includeSet = this.getIncludes(includes, targetMethod, targetClass);
        LogRecord logRecord = this.finish(started, endTime, response, includeSet);
        // 记录日志描述
        if (includeSet.contains(Include.DESCRIPTION)) {
            this.logDescription(logRecord, targetMethod);
        }
        // 记录所属模块
        if (includeSet.contains(Include.MODULE)) {
            this.logModule(logRecord, targetMethod, targetClass);
        }
        return logRecord;
    }

    @Override
    public LogRecord finish(LogRecord.Started started,
                            LocalDateTime endTime,
                            HttpServletResponse response,
                            Set<Include> includes) {
        return started.finish(endTime, new RecordableServletHttpResponse(response, response.getStatus()), includes);
    }

    /**
     * 记录日志描述
     *
     * @param logRecord    日志记录
     * @param targetMethod 目标方法
     */
    @Override
    public void logDescription(LogRecord logRecord, Method targetMethod) {
        ActionLog methodActionLog = AnnotationUtil.getAnnotation(targetMethod, ActionLog.class);
        // 例如：@Log("新增部门") -> 新增部门
        if (null != methodActionLog && CharSequenceUtil.isNotBlank(methodActionLog.value())) {
            logRecord.setDescription(methodActionLog.value());
        }
    }

    /**
     * 记录所属模块
     *
     * @param logRecord    日志记录
     * @param targetMethod 目标方法
     * @param targetClass  目标类
     */
    @Override
    public void logModule(LogRecord logRecord, Method targetMethod, Class<?> targetClass) {
        ActionLog methodActionLog = AnnotationUtil.getAnnotation(targetMethod, ActionLog.class);
        // 例如：@Log(module = "部门管理") -> 部门管理
        // 方法级注解优先级高于类级注解
        if (null != methodActionLog && CharSequenceUtil.isNotBlank(methodActionLog.module())) {
            logRecord.setModule(methodActionLog.module());
            return;
        }
        ActionLog classActionLog = AnnotationUtil.getAnnotation(targetClass, ActionLog.class);
        if (null != classActionLog && CharSequenceUtil.isNotBlank(classActionLog.module())) {
            logRecord.setModule(classActionLog.module());
        }
    }

    @Override
    public Set<Include> getIncludes(Set<Include> includes, Method targetMethod, Class<?> targetClass) {
        ActionLog classActionLog = AnnotationUtil.getAnnotation(targetClass, ActionLog.class);
        Set<Include> includeSet = new HashSet<>(includes);
        if (null != classActionLog) {
            this.processInclude(includeSet, classActionLog);
        }
        // 方法级注解优先级高于类级注解
        ActionLog methodActionLog = AnnotationUtil.getAnnotation(targetMethod, ActionLog.class);
        if (null != methodActionLog) {
            this.processInclude(includeSet, methodActionLog);
        }
        return includeSet;
    }

    /**
     * 处理日志包含信息
     *
     * @param includes      日志包含信息
     * @param actionLogAnnotation Log 注解
     */
    private void processInclude(Set<Include> includes, ActionLog actionLogAnnotation) {
        Include[] includeArr = actionLogAnnotation.includes();
        if (includeArr.length > 0) {
            includes.addAll(Set.of(includeArr));
        }
        Include[] excludeArr = actionLogAnnotation.excludes();
        if (excludeArr.length > 0) {
            includes.removeAll(Set.of(excludeArr));
        }
    }
}
