package me.supernova.common.act.log.interceptor;

import com.alibaba.ttl.TransmittableThreadLocal;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.supernova.common.act.log.core.ActionLogHandler;
import me.supernova.common.act.log.core.annotation.ActionLog;
import me.supernova.common.act.log.core.dao.ActionLogDao;
import me.supernova.common.act.log.core.model.ActionLogProperties;
import me.supernova.common.act.log.core.model.LogRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 日志拦截器
 *
 * <AUTHOR>
 * @since 1.1.0
 */
public class ActionLogInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(ActionLogInterceptor.class);
    private final ActionLogProperties actionLogProperties;
    private final ActionLogHandler actionLogHandler;
    private final ActionLogDao actionLogDao;
    private final TransmittableThreadLocal<LocalDateTime> timeTtl = new TransmittableThreadLocal<>();
    private final TransmittableThreadLocal<LogRecord.Started> logTtl = new TransmittableThreadLocal<>();

    public ActionLogInterceptor(ActionLogProperties actionLogProperties, ActionLogHandler actionLogHandler, ActionLogDao actionLogDao) {
        this.actionLogProperties = actionLogProperties;
        this.actionLogHandler = actionLogHandler;
        this.actionLogDao = actionLogDao;
    }

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request,
                             @NonNull HttpServletResponse response,
                             @NonNull Object handler) {
        LocalDateTime startTime = LocalDateTime.now();
        if (Boolean.TRUE.equals(actionLogProperties.getIsPrint())) {
            log.info("[{}] {}", request.getMethod(), request.getRequestURI());
            timeTtl.set(startTime);
        }
        // 开始日志记录
        if (this.isRequestRecord(handler, request)) {
            LogRecord.Started startedLogRecord = actionLogHandler.start(startTime, request);
            logTtl.set(startedLogRecord);
        }
        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request,
                                @NonNull HttpServletResponse response,
                                @NonNull Object handler,
                                Exception e) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            if (Boolean.TRUE.equals(actionLogProperties.getIsPrint())) {
                Duration timeTaken = Duration.between(timeTtl.get(), endTime);
                log.info("[{}] {} {} {}ms", request.getMethod(), request.getRequestURI(), response
                        .getStatus(), timeTaken.toMillis());
            }
            LogRecord.Started startedLogRecord = logTtl.get();
            if (null == startedLogRecord) {
                return;
            }
            // 结束日志记录
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method targetMethod = handlerMethod.getMethod();
            Class<?> targetClass = handlerMethod.getBeanType();
            LogRecord logRecord = actionLogHandler.finish(startedLogRecord, endTime, response, actionLogProperties
                    .getIncludes(), targetMethod, targetClass);
            actionLogDao.add(logRecord);
        } catch (Exception ex) {
            log.error("Logging http log occurred an error: {}.", ex.getMessage(), ex);
            throw ex;
        } finally {
            timeTtl.remove();
            logTtl.remove();
        }
    }

    /**
     * 是否要记录日志
     *
     * @param handler 处理器
     * @return true：需要记录；false：不需要记录
     */
    private boolean isRequestRecord(Object handler, HttpServletRequest request) {
        if (!(handler instanceof HandlerMethod handlerMethod)) {
            return false;
        }
        // 如果接口匹配排除列表，不记录日志
        if (actionLogProperties.isMatch(request.getRequestURI())) {
            return false;
        }
        // 不记录 sse 接口日志
        if (request.getRequestURI().contains("/sse")) {
            return false;
        }
        // 如果接口被隐藏，不记录日志
        Operation methodOperation = handlerMethod.getMethodAnnotation(Operation.class);
        if (null != methodOperation && methodOperation.hidden()) {
            return false;
        }
        Hidden methodHidden = handlerMethod.getMethodAnnotation(Hidden.class);
        if (null != methodHidden) {
            return false;
        }
        Class<?> handlerBeanType = handlerMethod.getBeanType();
        if (null != handlerBeanType.getDeclaredAnnotation(Hidden.class)) {
            return false;
        }
        // 如果接口方法或类上有 @Log 注解，且要求忽略该接口，则不记录日志
        ActionLog methodActionLog = handlerMethod.getMethodAnnotation(ActionLog.class);
        if (null != methodActionLog && methodActionLog.ignore()) {
            return false;
        }
        ActionLog classActionLog = handlerBeanType.getDeclaredAnnotation(ActionLog.class);
        return null == classActionLog || !classActionLog.ignore();
    }
}
