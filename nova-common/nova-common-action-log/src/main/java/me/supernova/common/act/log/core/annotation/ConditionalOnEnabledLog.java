package me.supernova.common.act.log.core.annotation;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.lang.annotation.*;

/**
 * 是否启用日志记录注解
 *
 * <AUTHOR>
 * @since 1.1.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@ConditionalOnProperty(prefix = "action-log", name = "enabled", havingValue = "true")
public @interface ConditionalOnEnabledLog {
}