package me.supernova.common.act.log.core.http.servlet;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletResponse;
import me.supernova.common.act.log.core.http.RecordableHttpResponse;
import me.supernova.common.core.filter.RepeatReadResponseWrapper;
import me.supernova.common.core.utils.ServletUtils;
import me.supernova.common.json.utils.JsonUtils;

import java.util.Map;

/**
 * 可记录的 HTTP 响应信息适配器
 *
 * <AUTHOR> Boot Actuator）
 * <AUTHOR>
 */
public final class RecordableServletHttpResponse implements RecordableHttpResponse {

    private final HttpServletResponse response;

    private final int status;

    public RecordableServletHttpResponse(HttpServletResponse response, int status) {
        this.response = response;
        this.status = status;
    }

    @Override
    public int getStatus() {
        return this.status;
    }

    @Override
    public Map<String, String> getHeaders() {
        return ServletUtils.getRespHeader();
    }

    @Override
    public String getBody() {
        if (response instanceof RepeatReadResponseWrapper wrapper) {
            return wrapper.getResponseContent();
        }
        return "";
    }

    @Override
    public Map<String, Object> getParam() {
        String body = this.getBody();
        if (CharSequenceUtil.isBlank(body) || !JSONUtil.isTypeJSON(body)) {
            return null;
        }

        return JsonUtils.parseMap(body);
    }
}
