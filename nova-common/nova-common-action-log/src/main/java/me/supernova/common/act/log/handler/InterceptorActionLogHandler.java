package me.supernova.common.act.log.handler;


import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.text.CharSequenceUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import me.supernova.common.act.log.core.AbstractActionLogHandler;
import me.supernova.common.act.log.core.model.LogRecord;

import java.lang.reflect.Method;

/**
 * 日志处理器-拦截器版实现
 *
 * <AUTHOR>
 * @since 2.8.0
 */
public class InterceptorActionLogHandler extends AbstractActionLogHandler {

    @Override
    public void logDescription(LogRecord logRecord, Method targetMethod) {
        super.logDescription(logRecord, targetMethod);
        if (CharSequenceUtil.isNotBlank(logRecord.getDescription())) {
            return;
        }
        // 例如：@Operation(summary="新增部门") -> 新增部门
        Operation methodOperation = AnnotationUtil.getAnnotation(targetMethod, Operation.class);
        if (null != methodOperation) {
            logRecord.setDescription(CharSequenceUtil.blankToDefault(methodOperation.summary(), "请在该接口方法上指定日志描述"));
        }
    }

    @Override
    public void logModule(LogRecord logRecord, Method targetMethod, Class<?> targetClass) {
        super.logModule(logRecord, targetMethod, targetClass);
        if (CharSequenceUtil.isNotBlank(logRecord.getModule())) {
            return;
        }
        // 例如：@Tag(name = "部门管理") -> 部门管理
        Tag classTag = AnnotationUtil.getAnnotation(targetClass, Tag.class);
        if (null != classTag) {
            String name = classTag.name();
            logRecord.setModule(CharSequenceUtil.blankToDefault(name, "请在该接口类上指定所属模块"));
        }
    }
}
