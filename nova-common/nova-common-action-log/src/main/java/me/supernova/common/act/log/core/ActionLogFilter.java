package me.supernova.common.act.log.core;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.act.log.core.model.ActionLogProperties;
import me.supernova.common.core.constant.MdcConstants;
import me.supernova.common.core.filter.RepeatReadRequestWrapper;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;

/**
 * 日志过滤器
 *
 * <AUTHOR>
 * @since 1.1.0
 */
@Slf4j
public class ActionLogFilter extends OncePerRequestFilter implements Ordered {

    private static final String CONTENT_LENGTH_HEADER = "content-length";
    private static final String X_API_ENCRYPT_HEADER = "X-Api-Encrypt";
    private static final String DECRYPTED_VALUE = "decrypted";
    private static final String ACTUATOR_HEALTH_PATH = "/actuator/health";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final ActionLogProperties actionLogProperties;

    public ActionLogFilter(ActionLogProperties actionLogProperties) {
        this.actionLogProperties = actionLogProperties;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE - 100;
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
                                    @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain) throws ServletException, IOException {
        if (!this.isFilter(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 生成CURL命令（如果启用）
        if (request instanceof RepeatReadRequestWrapper wrapper) {
            if (actionLogProperties.isEnableCurl() && shouldGenerateCurlCommand(wrapper)) {
                String curlCommand = generateCurlCommand(wrapper, wrapper.getRequestContent());
                MDC.put(MdcConstants.CURL, curlCommand);
                log.info("生成CURL命令: {}", curlCommand);
            }
        }

        // 执行过滤器链
        filterChain.doFilter(request, response);
    }

    /**
     * 判断是否应该生成CURL命令
     */
    private boolean shouldGenerateCurlCommand(HttpServletRequest request) {
        // 不处理健康检查接口
        return !ACTUATOR_HEALTH_PATH.equals(request.getRequestURI());
    }

    /**
     * 生成CURL命令
     */
    private String generateCurlCommand(HttpServletRequest request, String requestBody) {
        StringBuilder curl = new StringBuilder("curl --location --request ");
        curl.append(request.getMethod()).append(" '").append(request.getRequestURL());

        appendQueryParameters(request, curl);
        appendHeaders(request, curl);
        appendRequestBody(requestBody, curl);

        return curl.toString();
    }

    /**
     * 添加查询参数到CURL命令
     */
    private void appendQueryParameters(HttpServletRequest request, StringBuilder curl) {
        String queryString = request.getQueryString();
        if (queryString != null && !queryString.isEmpty()) {
            curl.append("?").append(queryString);
        }
        curl.append("'");
    }

    /**
     * 添加请求头到CURL命令
     */
    private void appendHeaders(HttpServletRequest request, StringBuilder curl) {
        Collections.list(request.getHeaderNames()).stream()
                .filter(headerName -> !headerName.equalsIgnoreCase(CONTENT_LENGTH_HEADER))
                .forEach(headerName -> {
                    String headerValue = request.getHeader(headerName);
                    if (headerValue != null && !headerValue.isEmpty()) {
                        // 对 X-Api-Encrypt 头进行特殊处理
                        if (headerName.equalsIgnoreCase(X_API_ENCRYPT_HEADER)) {
                            headerValue = DECRYPTED_VALUE;
                        }
                        curl.append(" --header '").append(headerName).append(": ").append(headerValue).append("'");
                    }
                });
    }

    /**
     * 添加请求体到CURL命令
     */
    private void appendRequestBody(String contentString, StringBuilder curl) {
        if (StrUtil.isNotBlank(contentString)) {

            // 移除BOM字符（U+FEFF）
            if (contentString.startsWith("\uFEFF")) {
                contentString = contentString.substring(1);
            }

            // 检查是否为 JSON 格式，如果是则压缩成一行
            if (JSONUtil.isTypeJSON(contentString)) {
                contentString = compressJson(contentString);
            }

            curl.append(" --data-raw '").append(contentString.replace("'", "\\'")).append("'");
        }
    }

    /**
     * 压缩 JSON 字符串，去掉所有换行和多余空格
     */
    private String compressJson(String json) {
        try {
            Object jsonObj = OBJECT_MAPPER.readValue(json, Object.class);
            return OBJECT_MAPPER.writeValueAsString(jsonObj);
        } catch (Exception e) {
            // 如果解析失败，返回原始字符串
            log.warn("Failed to compress JSON: {}", e.getMessage());
            return json;
        }
    }

    /**
     * 是否过滤请求
     */
    private boolean isFilter(HttpServletRequest request) {
        if (!isRequestValid(request)) {
            return false;
        }
        // 不拦截 /error
        ServerProperties serverProperties = SpringUtil.getBean(ServerProperties.class);
        return !request.getRequestURI().equals(serverProperties.getError().getPath());
    }

    /**
     * 请求是否有效
     */
    private boolean isRequestValid(HttpServletRequest request) {
        try {
            new URI(request.getRequestURL().toString());
            return true;
        } catch (URISyntaxException e) {
            return false;
        }
    }
}
