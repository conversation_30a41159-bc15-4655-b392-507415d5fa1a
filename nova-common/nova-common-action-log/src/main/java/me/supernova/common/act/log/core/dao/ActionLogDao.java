package me.supernova.common.act.log.core.dao;


import me.supernova.common.act.log.core.model.LogRecord;

import java.util.Collections;
import java.util.List;

/**
 * 日志持久层接口
 *
 * <AUTHOR>
 * @since 1.1.0
 */
public interface ActionLogDao {

    /**
     * 查询日志列表
     *
     * @return 日志列表
     */
    default List<LogRecord> list() {
        return Collections.emptyList();
    }

    /**
     * 记录日志
     *
     * @param logRecord 日志信息
     */
    void add(LogRecord logRecord);
}
