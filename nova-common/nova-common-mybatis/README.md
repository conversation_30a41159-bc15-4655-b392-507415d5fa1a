# Nova Common MyBatis 模块

Nova Common MyBatis 是 Nova Framework 的数据持久层模块，基于 MyBatis-Plus 实现，提供了强大的数据库操作功能和扩展特性。

## 主要功能

### 1. MyBatis-Plus 增强配置
- 自动配置 MyBatis-Plus 核心功能
- 分页插件支持
- 防全表更新与删除插件
- 支持虚拟线程优化

### 2. 通用 Mapper 增强
- `BaseMapperPlus`: 增强的基础 Mapper 接口
  - 支持实体与 VO 对象的自动转换
  - 批量操作支持
  - 丰富的查询方法
  - 分页查询优化

### 3. 自动填充功能
- `InjectionMetaObjectHandler`: 字段自动注入处理器
  - 创建时间自动填充
  - 更新时间自动填充
  - 创建人信息自动填充
  - 更新人信息自动填充
  - 部门信息自动填充

### 4. 分页功能
- `PageQuery`: 统一的分页查询对象
  - 支持动态排序
  - 支持多字段排序
  - 防止 SQL 注入
  - 参数自动校验

### 5. 数据类型处理
- `JSONTypePgHandler`: PostgreSQL JSON 类型处理器
  - 支持 JSON 数据类型的读写
  - 自动类型转换
  - 异常处理

### 6. 基础实体类
- `BaseEntity`: 实体基类
  - 统一的审计字段
  - 自动填充支持
  - 序列化支持

## 配置示例

### MyBatis-Plus 配置
```yaml
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: me.supernova.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  # 全局配置
  global-config:
    # 是否控制台打印 mybatis-plus 的 LOGO
    banner: false
    db-config:
      # 主键类型
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: delFlag
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
  # 扩展配置
  extension:
    # 分页插件
    pagination:
      enabled: true
      db-type: postgresql
      overflow: false
      max-limit: -1
    # 防止全表更新与删除插件
    block-attack-plugin-enabled: true
```

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-mybatis</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 创建实体类：
```java
@Data
@TableName("sys_user")
public class SysUser extends BaseEntity {
    private String username;
    private String password;
    // 其他字段...
}
```

3. 创建 Mapper：
```java
public interface SysUserMapper extends BaseMapperPlus<SysUser, SysUserVo> {
    // 自定义方法...
}
```

4. 分页查询示例：
```java
PageQuery pageQuery = new PageQuery();
pageQuery.setPageNum(1);
pageQuery.setPageSize(10);
pageQuery.setOrderByColumn("create_time");
pageQuery.setIsAsc("desc");

Page<SysUser> page = pageQuery.build();
Page<SysUser> result = baseMapper.selectPage(page, wrapper);
```

## 特性

1. 代码简化
- 通用 CRUD 操作
- 自动填充机制
- 分页查询简化

2. 性能优化
- 分页插件优化
- 批量操作支持
- 虚拟线程支持

3. 安全性
- 防止全表更新删除
- SQL 注入防护
- 字段自动转义

## 注意事项

1. 数据安全
- 启用防全表更新与删除插件
- 合理使用逻辑删除
- 注意字段类型匹配

2. 性能优化
- 合理使用批量操作
- 避免大事务
- 注意分页查询性能

3. 代码规范
- 统一使用基础实体类
- 规范命名约定
- 合理使用注解

4. 类型转换
- 注意 JSON 类型处理
- 处理特殊字符
- 注意日期时间类型 