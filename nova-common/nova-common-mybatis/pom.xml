<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>me.supernova</groupId>
        <artifactId>nova-common</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>nova-common-mybatis</artifactId>
    <description>nova-common-mybatis</description>

    <properties>
        <java.version>21</java.version>
    </properties>

    <dependencies>

        <!--  用作字段填充的  -->
        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-satoken</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
        </dependency>

        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-json</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
