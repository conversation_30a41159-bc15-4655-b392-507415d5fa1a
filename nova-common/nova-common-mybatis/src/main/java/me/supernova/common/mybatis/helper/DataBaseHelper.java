package me.supernova.common.mybatis.helper;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 数据库助手
 *
 * <AUTHOR> Li
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DataBaseHelper {

    public static String findInSet(Object var1, String var2) {
        // (select strpos(',0,100,101,' , ',100,')) <> 0
        return "(select strpos(','||%s||',' , ',%s,')) <> 0".formatted(var2, var1);
    }


}
