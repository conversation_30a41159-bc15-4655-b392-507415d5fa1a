package me.supernova.common.mybatis.core.page;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import me.supernova.common.core.domain.R;

import java.io.Serializable;
import java.util.List;

/**
 * 表格分页数据对象
 *
 * <AUTHOR> Li
 */
@EqualsAndHashCode(callSuper = false)
@Data
@NoArgsConstructor
public class TableDataInfo<T> extends R<Page<T>> implements Serializable {

    public static <T> TableDataInfo<T> build(Page<T> page) {
        TableDataInfo<T> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setData(page);
        rspData.setMessage("查询成功");
        return rspData;
    }

    /**
     * 根据数据列表构建表格分页数据对象
     */
    public static <T> TableDataInfo<T> build(List<T> list) {
        TableDataInfo<T> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.HTTP_OK);
        Page<T> page = Page.of(1, list.size(), list.size());
        rspData.setData(page.setRecords(list));
        rspData.setMessage("查询成功");
        return rspData;
    }

    /**
     * 构建分页数据对象并自动转换类型
     *
     * @param sourcePage  源分页对象
     * @param targetClass 目标类型
     * @return 转换后的分页数据对象
     */
    public static <S, T> TableDataInfo<T> build(Page<S> sourcePage, Class<T> targetClass) {
        TableDataInfo<T> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.HTTP_OK);

        // 转换记录
        List<T> targetList = BeanUtil.copyToList(sourcePage.getRecords(), targetClass);

        // 构建新的分页对象
        Page<T> targetPage = new Page<>(sourcePage.getCurrent(), sourcePage.getSize(), sourcePage.getTotal());
        targetPage.setRecords(targetList);

        rspData.setData(targetPage);
        rspData.setMessage("查询成功");
        return rspData;
    }
}
