package me.supernova.common.mybatis.config;

import com.baomidou.mybatisplus.annotation.DbType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * MyBatis Plus 扩展配置属性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ConfigurationProperties("mybatis-plus.extension")
public class MyBatisPlusExtensionProperties {

    /**
     * 分页插件配置
     */
    private PaginationProperties pagination;

    /**
     * 启用防全表更新与删除插件
     */
    private boolean blockAttackPluginEnabled = true;

    /**
     * 分页插件配置属性
     */
    @Data
    public static class PaginationProperties {

        /**
         * 是否启用分页插件
         */
        private boolean enabled = true;

        /**
         * 数据库类型
         */
        private DbType dbType;

        /**
         * 是否溢出处理
         */
        private boolean overflow = false;

        /**
         * 单页分页条数限制（默认：-1 表示无限制）
         */
        private Long maxLimit = -1L;

    }

}
