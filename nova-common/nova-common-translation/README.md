# Nova Common Translation 模块

Nova Common Translation 是 Nova Framework 的通用翻译模块，提供了基于注解的字段翻译功能。该模块主要用于处理实体类字段的动态翻译，支持多种翻译实现方式。

## 主要功能

### 1. 注解驱动翻译
- `@Translation` 注解支持
  - 支持字段级别翻译
  - 支持方法级别翻译
  - 灵活的映射字段配置
  - 支持额外参数传递

### 2. 翻译实现
- 自定义翻译接口
  - 实现 `TranslationInterface` 接口
  - 支持多种翻译类型
  - 支持自定义翻译逻辑
- 翻译结果缓存
  - 并发安全的缓存机制
  - 自动管理翻译实现映射

### 3. 序列化处理
- 自定义序列化器
  - 支持 null 值处理
  - 自动类型转换
  - 灵活的序列化配置
- Bean 序列化修改
  - 统一的序列化处理
  - 自定义序列化规则

### 4. 扩展功能
- 映射字段支持
  - 支持关联字段翻译
  - 自动字段值获取
- 条件参数支持
  - 支持额外条件配置
  - 灵活的参数传递

## 配置示例

### 1. 创建翻译实现类
```java
@Component("userStatus")
public class UserStatusTranslation implements TranslationInterface<String> {
    
    @Override
    public String translation(Object key, String other) {
        if (key instanceof Integer status) {
            return switch (status) {
                case 0 -> "禁用";
                case 1 -> "正常";
                default -> "未知";
            };
        }
        return "未知";
    }
}
```

### 2. 使用翻译注解
```java
@Data
public class UserVO {
    
    private Long userId;
    
    private String username;
    
    // 直接翻译当前字段
    @Translation(type = "userStatus")
    private Integer status;
    
    // 翻译关联字段
    @Translation(type = "dept", mapper = "deptId")
    private Long deptId;
    
    // 带额外参数的翻译
    @Translation(type = "dict", other = "sys_user_sex")
    private String sex;
}
```

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-translation</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 实现翻译接口：
```java
@Component("dictType")
public class DictTypeTranslation implements TranslationInterface<String> {
    
    @Autowired
    private DictService dictService;
    
    @Override
    public String translation(Object key, String other) {
        if (key == null || other == null) {
            return null;
        }
        return dictService.getDictLabel(other, key.toString());
    }
}
```

3. 在实体类中使用：
```java
public class SysUser {
    
    @Translation(type = "dictType", other = "sys_normal_disable")
    private String status;
    
    @Translation(type = "userType")
    private Integer userType;
    
    // 使用映射字段
    private Long deptId;
    @Translation(type = "dept", mapper = "deptId")
    private String deptName;
}
```

## 特性

1. 易扩展
- 支持自定义翻译实现
- 灵活的配置方式
- 模块化设计

2. 高性能
- 缓存翻译实现
- 最小化性能影响
- 并发安全

3. 易用性
- 注解驱动
- 直观的配置
- 丰富的示例

## 注意事项

1. 性能考虑
- 合理使用翻译缓存
- 避免过多的翻译操作
- 注意翻译实现的性能

2. 翻译实现
- 实现类需要注册为 Spring Bean
- 注意处理 null 值情况
- 考虑异常处理

3. 映射使用
- 正确配置映射字段
- 确保映射字段存在
- 注意字段类型匹配

4. 序列化处理
- 注意序列化性能
- 处理特殊类型转换
- 考虑 null 值处理