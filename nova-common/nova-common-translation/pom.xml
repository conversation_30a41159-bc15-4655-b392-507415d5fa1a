<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>me.supernova</groupId>
        <artifactId>nova-common</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>nova-common-translation</artifactId>
    <name>nova-common-translation</name>
    <description>nova-common-translation</description>

    <properties>
        <java.version>21</java.version>
    </properties>


    <dependencies>

        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>me.supernova</groupId>
            <artifactId>nova-common-json</artifactId>
        </dependency>
    </dependencies>

</project>
