package me.supernova.common.translation.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.translation.core.TranslationInterface;
import me.supernova.common.translation.core.handler.TranslationBeanSerializerModifier;
import me.supernova.common.translation.core.handler.TranslationHandler;
import org.springframework.boot.autoconfigure.AutoConfiguration;

import java.util.Map;

/**
 * 翻译模块配置类
 * 用法:
 * 实现一个 TranslationInterface 接口, 实现 translation 方法, 返回翻译后的值
 * 用@component 注解,name ,与@Translation 的 type 属性值对应,这样就能找到对应的转换了
 * 注意: 这个是在返回值比较少的情况下可以用,如果返回值很多,建议直接连表
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
public class TranslationConfig {

    private final Map<String, TranslationInterface<?>> translationInterfaceMap;
    private final ObjectMapper objectMapper;


    @PostConstruct
    public void init() {
        if (translationInterfaceMap != null) {
            TranslationHandler.TRANSLATION_MAPPER.putAll(translationInterfaceMap);
        }

        // 设置 Bean 序列化修改器
        objectMapper.setSerializerFactory(
                objectMapper.getSerializerFactory()
                        .withSerializerModifier(new TranslationBeanSerializerModifier()));
    }

}
