# Nova Common Encrypt 模块

Nova Common Encrypt 是 Nova Framework 的加密模块，提供了多种加密算法的实现，支持数据的加密和解密操作。该模块基于 Hutool Crypto 实现，提供了灵活的加密方案。

## 主要功能

### 1. 加密算法支持
- Base64 编码/解码
- AES 对称加密
  - 支持 16/24/32 位密钥
  - 支持 Base64/Hex 编码
- RSA 非对称加密
  - 公钥加密/私钥解密
  - 支持密钥对生成
  - 支持 Base64/Hex 编码
- 摘要算法
  - MD5 加密
  - SHA256 加密
  - SM3 加密

### 2. API 加密
- 请求解密
  - 支持请求体解密
  - 自动解密处理
- 响应加密
  - 支持响应体加密
  - 注解驱动加密
- 灵活的配置
  - 支持开关控制
  - 自定义头部标识
  - 可配置密钥对

### 3. 加密上下文
- 算法类型配置
- 密钥管理
- 编码方式选择
- 上下文参数传递

### 4. 扩展功能
- 自定义加密器
- 编码类型扩展
- 异常处理机制
- 性能优化

## 配置示例

### 1. API 加密配置
```yaml
api-decrypt:
  # 加密功能开关
  enabled: true
  # 头部标识
  header-flag: Encrypt
  # 响应加密公钥
  public-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC...
  # 请求解密私钥
  private-key: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwgg...
```

### 2. 注解使用示例
```java
@RestController
@RequestMapping("/api")
public class DemoController {
    
    // 强制响应加密
    @ApiEncrypt(response = true)
    @GetMapping("/data")
    public R<String> getData() {
        return R.ok("sensitive data");
    }
}
```

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-encrypt</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 加密工具类使用：
```java
// Base64 加密/解密
String encoded = EncryptUtils.encryptByBase64("原文");
String decoded = EncryptUtils.decryptByBase64(encoded);

// AES 加密/解密
String aesKey = "1234567890123456"; // 16位密钥
String encrypted = EncryptUtils.encryptByAes("原文", aesKey);
String decrypted = EncryptUtils.decryptByAes(encrypted, aesKey);

// RSA 加密/解密
Map<String, String> keyPair = EncryptUtils.generateRsaKey();
String publicKey = keyPair.get(EncryptUtils.PUBLIC_KEY);
String privateKey = keyPair.get(EncryptUtils.PRIVATE_KEY);
String rsaEncrypted = EncryptUtils.encryptByRsa("原文", publicKey);
String rsaDecrypted = EncryptUtils.decryptByRsa(rsaEncrypted, privateKey);

// 摘要算法
String md5 = EncryptUtils.encryptByMd5("原文");
String sha256 = EncryptUtils.encryptBySha256("原文");
String sm3 = EncryptUtils.encryptBySm3("原文");
```

3. 自定义加密器：
```java
public class CustomEncryptor extends AbstractEncryptor {
    
    public CustomEncryptor(EncryptContext context) {
        super(context);
    }
    
    @Override
    public AlgorithmType algorithm() {
        return AlgorithmType.CUSTOM;
    }
    
    @Override
    public String encrypt(String value, EncodeType encodeType) {
        // 实现加密逻辑
        return value;
    }
    
    @Override
    public String decrypt(String value) {
        // 实现解密逻辑
        return value;
    }
}
```

## 特性

1. 安全性
- 多种加密算法支持
- 密钥管理机制
- 加密强度保证

2. 易用性
- 简单的 API 设计
- 注解驱动加密
- 统一的工具类

3. 扩展性
- 自定义加密器
- 灵活的配置
- 编码方式扩展

## 注意事项

1. 密钥管理
- 安全存储密钥
- 定期更换密钥
- 避免密钥泄露

2. 性能考虑
- 选择合适的算法
- 避免过度加密
- 注意加密开销

3. 安全建议
- 使用强密码
- 及时更新算法
- 注意数据安全

4. 兼容性
- 加密算法版本
- 密钥长度限制
- 编码格式统一 