package me.supernova.common.encrypt.enumd;

import lombok.AllArgsConstructor;
import lombok.Getter;
import me.supernova.common.encrypt.core.encryptor.AbstractEncryptor;
import me.supernova.common.encrypt.core.encryptor.AesEncryptor;

/**
 * 算法名称
 *
 * <AUTHOR>
 * @version 4.6.0
 */
@Getter
@AllArgsConstructor
public enum AlgorithmType {

    /**
     * 默认走yml配置
     */
    DEFAULT(null),

    /**
     * aes
     */
    AES(AesEncryptor.class);

    private final Class<? extends AbstractEncryptor> clazz;
}
