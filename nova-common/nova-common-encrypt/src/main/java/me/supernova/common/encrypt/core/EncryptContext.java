package me.supernova.common.encrypt.core;

import lombok.Data;
import me.supernova.common.encrypt.enumd.AlgorithmType;
import me.supernova.common.encrypt.enumd.EncodeType;

/**
 * 加密上下文 用于encryptor传递必要的参数。
 *
 * <AUTHOR>
 * @version 4.6.0
 */
@Data
public class EncryptContext {

    /**
     * 默认算法
     */
    private AlgorithmType algorithm;

    /**
     * 安全秘钥
     */
    private String password;

    /**
     * 编码方式，base64/hex
     */
    private EncodeType encode;
}
