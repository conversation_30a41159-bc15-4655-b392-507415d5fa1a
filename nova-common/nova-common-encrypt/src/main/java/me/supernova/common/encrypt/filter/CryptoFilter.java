package me.supernova.common.encrypt.filter;

import cn.hutool.core.util.StrUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.supernova.common.core.constant.ApiDecryptConstant;
import me.supernova.common.core.filter.RepeatReadRequestWrapper;
import me.supernova.common.core.filter.RepeatReadResponseWrapper;
import me.supernova.common.encrypt.properties.ApiDecryptProperties;
import me.supernova.common.encrypt.utils.EncryptUtils;
import org.springframework.core.Ordered;
import org.springframework.http.HttpMethod;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;


/**
 * Crypto 过滤器
 *
 * <AUTHOR>
 */
public class CryptoFilter extends OncePerRequestFilter implements Ordered {

    private final ApiDecryptProperties properties;

    public CryptoFilter(ApiDecryptProperties properties) {
        this.properties = properties;
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 10;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {

        // 是否存在加密标头
        String headerValue = request.getHeader(ApiDecryptConstant.HEADER_FLAG);
        if (StrUtil.isBlank(headerValue) || "decrypted".equals(headerValue) || !HttpMethod.POST.matches(request.getMethod())) {
            chain.doFilter(request, response);
            return;
        }


        if (request instanceof RepeatReadRequestWrapper reqWrapper) {
            String requestContent = reqWrapper.getRequestContent();
            // 解密 body 采用 AES 加密
            reqWrapper.setRequestContent(EncryptUtils.decryptByAes(requestContent, properties.getPassword()));
        }

        chain.doFilter(request, response);


        if (response instanceof RepeatReadResponseWrapper respWrapper) {
            // 获取原始响应内容
            String responseContent = respWrapper.getResponseContent();

            if (responseContent != null && !respWrapper.isStreamingResponse()) {
                // 设置响应头
                respWrapper.addHeader("Access-Control-Expose-Headers", "*");
                respWrapper.setHeader("Access-Control-Allow-Origin", "*");
                respWrapper.setHeader("Access-Control-Allow-Methods", "*");
                respWrapper.setHeader(ApiDecryptConstant.HEADER_FLAG, "1");
                respWrapper.setCharacterEncoding(StandardCharsets.UTF_8.toString());

                // 对内容进行加密
                String encryptedBody = EncryptUtils.encryptByAes(responseContent, properties.getPassword());
                respWrapper.setResponseContent(encryptedBody);
            }
        }
    }


}
