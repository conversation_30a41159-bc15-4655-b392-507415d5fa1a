# Nova Common OSS 模块

Nova Common OSS 是 Nova Framework 的对象存储服务模块，基于 AWS S3 SDK 实现，支持多种云存储服务，包括阿里云 OSS、腾讯云 COS、七牛云、华为云 OBS 等兼容 S3 协议的对象存储服务。

## 主要功能

### 1. 多云存储支持
- 支持主流云存储服务
  - 阿里云 OSS
  - 腾讯云 COS
  - 七牛云
  - 华为云 OBS
  - MinIO
- 统一的 API 接口
- 灵活的配置切换

### 2. 文件操作
- 文件上传
  - 支持字节数组上传
  - 支持输入流上传
  - 支持文件对象上传
  - 自动生成唯一文件名
- 文件下载
  - 支持下载到本地文件
  - 支持下载到输出流
  - 支持获取文件输入流
- 文件删除
  - 支持删除单个文件
  - 异常处理机制

### 3. 访问控制
- 访问策略管理
  - 私有访问
  - 公共读写
  - 自定义权限
- URL 签名
  - 支持私有文件访问
  - 可配置签名有效期
- HTTPS 支持
  - 可配置是否使用 HTTPS
  - 自动协议适配

### 4. 高级特性
- 高性能传输
  - 基于 AWS CRT 的客户端
  - S3 传输管理器支持
  - 异步操作支持
- 缓存管理
  - 客户端缓存
  - 配置缓存
- 自定义域名
  - 支持 CDN 加速
  - 灵活的域名配置

## 配置示例

### OSS 配置
```yaml
oss:
  # 存储服务商
  cloud-service: aliyun
  # 访问站点
  endpoint: oss-cn-beijing.aliyuncs.com
  # 自定义域名
  domain: cdn.example.com
  # 存储空间名
  bucket-name: my-bucket
  # 访问密钥
  access-key: your-access-key
  # 密钥密文
  secret-key: your-secret-key
  # 存储区域
  region: cn-beijing
  # 是否启用 HTTPS
  is-https: Y
  # 访问策略（0=私有 1=公共读写 2=自定义）
  access-policy: 0
```

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-oss</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 文件上传示例：
```java
// 获取 OSS 客户端实例
OssClient client = OssFactory.instance();

// 上传字节数组
byte[] data = Files.readAllBytes(file.toPath());
UploadResult result1 = client.uploadSuffix(data, ".jpg", "image/jpeg");

// 上传输入流
InputStream inputStream = new FileInputStream(file);
UploadResult result2 = client.uploadSuffix(inputStream, ".pdf", fileLength, "application/pdf");

// 上传文件对象
File file = new File("example.txt");
UploadResult result3 = client.uploadSuffix(file, ".txt");
```

3. 文件下载示例：
```java
// 下载到本地文件
Path localFile = client.fileDownload("example/test.jpg");

// 下载到输出流
OutputStream outputStream = new FileOutputStream("local.jpg");
long size = client.download("example/test.jpg", outputStream);

// 获取文件输入流
InputStream inputStream = client.getObjectContent("example/test.jpg");
```

4. 访问控制示例：
```java
// 获取私有文件访问 URL（有效期 3600 秒）
String url = client.getPrivateUrl("example/private.jpg", 3600);

// 删除文件
client.delete("example/test.jpg");
```

## 特性

1. 高性能
- 基于 AWS CRT 的高性能客户端
- 异步传输支持
- 自动重试机制

2. 可靠性
- 异常处理机制
- 传输校验（ETag）
- 自动重连

3. 安全性
- 访问控制
- HTTPS 支持
- URL 签名机制

## 注意事项

1. 配置安全
- 妥善保管访问密钥
- 合理设置访问权限
- 定期更新密钥

2. 性能优化
- 合理使用客户端缓存
- 控制并发请求数
- 选择合适的传输方式

3. 成本控制
- 注意存储空间使用
- 合理设置文件过期策略
- 监控流量使用

4. 兼容性
- 注意不同云服务商的差异
- 测试特殊场景
- 保持版本兼容 