# Nova Common Excel 模块

Nova Common Excel 是 Nova Framework 的 Excel 处理模块，基于 FastExcel 实现，提供了强大的 Excel 导入导出功能。

## 主要功能

### 1. Excel 导入
- 同步导入（适用于小数据量）
- 异步导入（支持大数据量）
- 数据校验
  - 支持 Jakarta Validation 注解校验
  - 自定义校验监听器
  - 错误信息收集和处理
- 类型转换
  - 支持多种数据类型自动转换
  - 枚举类型转换
  - 自定义转换器

### 2. Excel 导出
- 普通导出
  - 支持注解配置导出
  - 自动列宽调整
  - 大数值自动转换
- 模板导出
  - 单表多数据导出
  - 多表多数据导出
  - 多 sheet 导出
- 下拉框支持
  - 简单下拉选择
  - 级联下拉选择
  - 动态数据源

### 3. 注解支持
- `@ExcelFormat`: 格式化注解
  - 支持自定义转换表达式
  - 支持分隔符配置
- `@ExcelEnumFormat`: 枚举格式化注解
  - 支持枚举类型自动转换
  - 支持自定义枚举字段映射

### 4. 数据处理
- 大数值处理
  - 防止精度丢失
  - 自动字符串转换
- 异常处理
  - 统一异常处理
  - 详细错误信息
- 数据校验
  - 数据有效性验证
  - 自定义校验规则

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-excel</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 导入示例：
```java
// 同步导入（小数据量）
List<User> userList = ExcelUtil.importExcel(inputStream, User.class);

// 异步导入（大数据量）
ExcelResult<User> result = ExcelUtil.importExcel(inputStream, User.class, true);

// 自定义监听器导入
ExcelListener<User> listener = new CustomExcelListener<>();
ExcelResult<User> result = ExcelUtil.importExcel(inputStream, User.class, listener);
```

3. 导出示例：
```java
// 简单导出
ExcelUtil.exportExcel(list, "用户数据", User.class, response);

// 带下拉框的导出
List<DropDownOptions> options = new ArrayList<>();
options.add(new DropDownOptions(0, Arrays.asList("选项1", "选项2")));
ExcelUtil.exportExcel(list, "用户数据", User.class, response, options);

// 模板导出
Map<String, Object> data = new HashMap<>();
data.put("users", userList);
data.put("dept", deptList);
ExcelUtil.exportTemplateMultiList(data, "templates/user_template.xlsx", response);
```

4. 实体类注解示例：
```java
@Data
public class User {
    // 枚举转换
    @ExcelEnumFormat(enumClass = UserType.class, textField = "desc")
    private UserType userType;
    
    // 格式化转换
    @ExcelFormat(readConverterExp = "0=男,1=女")
    private String gender;
    
    // 大数值处理
    @JsonSerialize(using = BigNumberSerializer.class)
    private Long id;
}
```

## 特性

1. 高性能
- 基于 FastExcel 实现
- 支持大数据量处理
- 异步处理机制

2. 易扩展
- 自定义监听器
- 自定义转换器
- 自定义校验规则

3. 功能丰富
- 完整的导入导出功能
- 丰富的数据处理能力
- 强大的下拉框支持

## 注意事项

1. 导入注意事项
- 大数据量建议使用异步导入
- 注意内存占用
- 合理配置校验规则

2. 导出注意事项
- 下拉框数量过多可能影响性能
- 模板文件路径配置
- 数据量限制

3. 性能优化
- 合理使用同步/异步模式
- 避免过多数据转换
- 控制下拉框数据量

4. 数据安全
- 注意敏感数据处理
- 文件大小限制
- 并发处理控制 