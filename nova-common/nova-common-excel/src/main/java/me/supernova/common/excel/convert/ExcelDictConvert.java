package me.supernova.common.excel.convert;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.enums.CellDataTypeEnum;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.ReadCellData;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;
import me.supernova.common.excel.annotation.ExcelFormat;
import me.supernova.common.excel.utils.ExcelUtil;

import java.lang.reflect.Field;

/**
 * 字典格式化转换处理
 *
 * <AUTHOR> Li
 */
@Slf4j
public class ExcelDictConvert implements Converter<Object> {

    @Override
    public Class<Object> supportJavaTypeKey() {
        return Object.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Object convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        ExcelFormat anno = getAnnotation(contentProperty.getField());
        String label = cellData.getStringValue();
        String value = ExcelUtil.reverseByExp(label, anno.readConverterExp(), anno.separator());

        return Convert.convert(contentProperty.getField().getType(), value);
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object object, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (ObjectUtil.isNull(object)) {
            return new WriteCellData<>("");
        }
        ExcelFormat anno = getAnnotation(contentProperty.getField());

        String value = Convert.toStr(object);
        String label = ExcelUtil.convertByExp(value, anno.readConverterExp(), anno.separator());
        return new WriteCellData<>(label);
    }

    private ExcelFormat getAnnotation(Field field) {
        return AnnotationUtil.getAnnotation(field, ExcelFormat.class);
    }
}
