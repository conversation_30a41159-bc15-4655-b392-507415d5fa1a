package me.supernova.common.excel.annotation;


import cn.hutool.core.util.StrUtil;

import java.lang.annotation.*;

/**
 * 格式化,去掉了字典功能
 *
 * <AUTHOR> Li
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface ExcelFormat {

    /**
     * 读取内容转表达式 (如: 0=男,1=女,2=未知)
     */
    String readConverterExp() default "";

    /**
     * 分隔符，读取字符串组内容
     */
    String separator() default StrUtil.COMMA;

}
