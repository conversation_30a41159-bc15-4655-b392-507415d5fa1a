# Nova Common Web 模块

Nova Common Web 是 Nova Framework 的 Web 通用模块，提供了 Web 应用所需的基础配置和功能组件。该模块基于 Spring Boot Web 构建，使用 Undertow 作为 Web 容器。

## 主要功能

### 1. Web 服务器配置
- 使用 Undertow 作为高性能 Web 容器
- 支持 WebSocket
- 支持虚拟线程优化
- 提供基础的性能监控（Actuator）

### 2. 安全防护
- XSS 防护
  - 支持自定义排除路径
  - 支持 GET、POST 请求过滤
  - JSON 数据自动清洗
- CORS 跨域配置
  - 支持自定义源地址
  - 支持自定义请求头
  - 支持自定义请求方法

### 3. 请求追踪
- TraceId 过滤器
  - 自动生成请求追踪 ID
  - 集成 MDC 日志追踪
  - 响应头自动包含追踪 ID

### 4. 数据转换
- 时间类型转换器
  - `StringTimeToLocalDateTimeConvert`: 字符串转 LocalDateTime
  - `TimestampToLocalDateTimeConvert`: 时间戳转 LocalDateTime
  - 支持多种时间格式
  - 自动时区处理

### 5. 异常处理
- 全局异常处理器
  - 统一的异常响应格式
  - 详细的异常日志记录
  - 支持多种异常类型：
    - 请求方法不支持
    - 业务异常
    - 参数验证异常
    - 路径变量缺失
    - 参数类型不匹配
    - 404 异常
    - 运行时异常等

### 6. 基础控制器
- `BaseController`: 提供通用的控制器方法
  - 统一的响应处理
  - 页面重定向支持
  - 操作结果转换

## 配置示例

### XSS 防护配置
```yaml
xss:
  # 开启 XSS 防护
  enabled: true
  # 排除路径
  excludes:
    - /system/notice/*
    - /api/v1/*
```

### Undertow 服务器配置
```yaml
server:
  undertow:
    # HTTP post 内容的最大大小
    max-http-post-size: -1
    # 以下的配置会影响 buffer，这些 buffer 会用于服务器连接的 IO 操作
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置 IO 线程数
      io: 8
      # 阻塞任务线程池
      worker: 256
```

## 使用方式

1. 引入依赖：
```xml
<dependency>
    <groupId>me.supernova</groupId>
    <artifactId>nova-common-web</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 自动配置：
模块使用 Spring Boot 的自动配置机制，在 `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` 中定义了自动装配类：
- UndertowConfig
- XssConfig
- ResourcesConfig
- WebConfig

3. 继承基础控制器：
```java
@RestController
@RequestMapping("/api/v1")
public class YourController extends BaseController {
    
    @PostMapping("/example")
    public R<Void> example() {
        // 业务处理
        return toAjax(true);
    }
}
```

## 特性

1. 高性能
- 使用 Undertow 替代 Tomcat
- 支持虚拟线程优化
- 异步请求处理

2. 安全性
- XSS 防护
- 请求追踪
- 异常处理

3. 可扩展性
- 灵活的配置选项
- 易于集成的基础组件
- 统一的异常处理机制

## 注意事项

1. XSS 防护
- 合理配置排除路径
- 注意性能影响
- 确保重要接口的防护

2. 异常处理
- 使用全局异常处理器
- 合理记录异常日志
- 注意敏感信息保护

3. 性能优化
- 合理配置线程池
- 注意内存使用
- 监控服务器状态

4. 时间处理
- 注意时区设置
- 统一时间格式
- 处理特殊时间格式 