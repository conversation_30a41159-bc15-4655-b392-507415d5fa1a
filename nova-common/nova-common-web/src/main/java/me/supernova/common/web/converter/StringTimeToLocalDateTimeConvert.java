package me.supernova.common.web.converter;

import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> wfj
 * @version : 1.0
 * @date : 2022/2/18 1:17 下午
 * @description : LocalDateTime 转换器,时间字符串 转 LocalDateTime,用于转换RequestParam和PathVariable参数
 */
@Slf4j
public class StringTimeToLocalDateTimeConvert implements Converter<String, LocalDateTime> {

    private final DateTimeFormatter dateTimeFormatter;
    private final ZoneId zoneId;

    private final TimestampToLocalDateTimeConvert timestampToLocalDateTimeConvert;

    public StringTimeToLocalDateTimeConvert(DateTimeFormatter dateTimeFormatter, ZoneId zoneId) {
        this.dateTimeFormatter = dateTimeFormatter;
        this.zoneId = zoneId;
        this.timestampToLocalDateTimeConvert = new TimestampToLocalDateTimeConvert(zoneId);
    }

    @Override
    public LocalDateTime convert(String source) {
        if (!StringUtils.hasText(source)) {
            return null;
        }

        // 时间戳
        if (NumberUtil.isLong(source)) {
            return timestampToLocalDateTimeConvert.convert(Long.parseLong(source));
        }

        switch (source.length()) {
            case 10:
                source = source + " 00:00:00";
                break;
            case 13:
                source = source + ":00:00";
                break;
            case 16:
                source = source + ":00";
                break;
            default:
                break;
        }

        try {
            LocalDateTime localDateTime = LocalDateTime.parse(source, dateTimeFormatter);
            ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
            return zonedDateTime.toLocalDateTime();
        } catch (Exception e) {
            log.error("LocalDateTimeConvert.Exception source:{} | ", source, e);
        }
        return null;
    }

    @Override
    public <U> Converter<String, U> andThen(Converter<? super LocalDateTime, ? extends U> after) {
        return Converter.super.andThen(after);
    }
}