package me.supernova.common.web.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.supernova.common.core.utils.MatchUtil;
import me.supernova.common.web.config.properties.XssProperties;
import org.springframework.core.Ordered;
import org.springframework.http.HttpMethod;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * 防止XSS攻击的过滤器
 *
 * <AUTHOR>
 */
public class XssFilter extends OncePerRequestFilter implements Ordered {

    private final XssProperties properties;

    public XssFilter(XssProperties properties) {
        this.properties = properties;
    }


    @Override
    protected void doFilterInternal(HttpServletRequest req, HttpServletResponse resp, FilterChain chain) throws ServletException, IOException {
        if (handleExcludeURL(req, resp)) {
            chain.doFilter(req, resp);
            return;
        }

        chain.doFilter(new XssHttpServletRequestWrapper(req), resp);
    }

    private boolean handleExcludeURL(HttpServletRequest request, HttpServletResponse response) {
        String url = request.getServletPath();
        String method = request.getMethod();
        // GET DELETE 不过滤
        if (method == null || HttpMethod.GET.matches(method) || HttpMethod.DELETE.matches(method)) {
            return true;
        }
        return MatchUtil.isAnyMatch(url, properties.getExcludeUrls());
    }

    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE - 110;
    }
}
