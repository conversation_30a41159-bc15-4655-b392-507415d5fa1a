package me.supernova.common.web.config;

import lombok.extern.slf4j.Slf4j;
import me.supernova.common.web.config.properties.XssProperties;
import me.supernova.common.web.converter.StringTimeToLocalDateTimeConvert;
import me.supernova.common.web.converter.TimestampToLocalDateTimeConvert;
import me.supernova.common.web.filter.RepeatReadFilter;
import me.supernova.common.web.filter.TraceIdFilter;
import me.supernova.common.web.filter.XssFilter;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import java.time.ZoneOffset;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMATTER;

@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(XssProperties.class)
public class WebConfig {

    @Bean
    @ConditionalOnMissingBean
    public StringTimeToLocalDateTimeConvert getStringTimeToLocalDateTimeConvert() {
        return new StringTimeToLocalDateTimeConvert(NORM_DATETIME_FORMATTER, ZoneOffset.systemDefault());
    }

    @Bean
    @ConditionalOnMissingBean
    public TimestampToLocalDateTimeConvert getTimestampToLocalDateTimeConvert() {
        return new TimestampToLocalDateTimeConvert(ZoneOffset.systemDefault());
    }

    @Bean
    public RepeatReadFilter repeatReadFilter() {
        return new RepeatReadFilter();
    }

    @Bean
    @ConditionalOnProperty(value = "xss.enabled", havingValue = "true")
    public XssFilter xssFilter(XssProperties xssProperties) {
        return new XssFilter(xssProperties);
    }

    @Bean
    public TraceIdFilter traceIdFilter() {
        return new TraceIdFilter();
    }
}