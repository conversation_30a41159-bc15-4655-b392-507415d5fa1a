package me.supernova.common.web.filter;

import cn.hutool.extra.spring.SpringUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.supernova.common.core.filter.RepeatReadRequestWrapper;
import me.supernova.common.core.filter.RepeatReadResponseWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * 日志过滤器
 */
public class RepeatReadFilter extends OncePerRequestFilter implements Ordered {

    private static final Logger logger = LoggerFactory.getLogger(RepeatReadFilter.class);

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
                                    @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain) throws ServletException, IOException {
        // 检查是否需要过滤该请求
        if (!shouldProcess(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 处理请求
        RepeatReadRequestWrapper processedRequest = new RepeatReadRequestWrapper(request);

        // 处理响应
        RepeatReadResponseWrapper processedResponse = new RepeatReadResponseWrapper(response);

        // 执行过滤链
        filterChain.doFilter(processedRequest, processedResponse);

        processedResponse.copyBodyToResponse();
    }


    /**
     * 判断是否应该过滤该请求
     *
     * @param request 请求对象
     * @return 是否过滤请求
     */
    private boolean shouldProcess(HttpServletRequest request) {
        // 检查请求是否有效
        if (!isRequestValid(request)) {
            return false;
        }

        // 不拦截 /error 路径
        ServerProperties serverProperties = SpringUtil.getBean(ServerProperties.class);

        if (request.getRequestURI().equals(serverProperties.getError().getPath())) {
            return false;
        }

        if (request.getRequestURI().startsWith("/actuator")) {
            return false;
        }

        // 不拦截SSE流式请求
        String acceptHeader = request.getHeader("Accept");
        if (acceptHeader != null && acceptHeader.contains("text/event-stream")) {
            return false;
        }

        // 不拦截包含stream的路径
        if (request.getRequestURI().contains("/stream")) {
            return false;
        }

        return true;
    }

    /**
     * 请求是否有效
     *
     * @param request 请求对象
     * @return true：是；false：否
     */
    private boolean isRequestValid(HttpServletRequest request) {
        try {
            new URI(request.getRequestURL().toString());
            return true;
        } catch (URISyntaxException e) {
            logger.warn("无效的请求URL: {}", request.getRequestURL());
            return false;
        }
    }

}
