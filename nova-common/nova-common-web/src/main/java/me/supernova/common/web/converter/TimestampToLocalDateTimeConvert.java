package me.supernova.common.web.converter;

import org.springframework.core.convert.converter.Converter;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR> wfj
 * @version : 1.0
 * @date : 2022/2/18 1:05 下午
 * @description : LocalDateTime 转换器,时间戳 转 LocalDateTime,用于转换RequestParam和PathVariable参数
 */
public class TimestampToLocalDateTimeConvert implements Converter<Long, LocalDateTime> {

    private final ZoneId zoneId;
    /**
     * 毫秒
     */
    private static final long MILLISECOND_THRESHOLD = 1000000000L; // 1 billion, for seconds to milliseconds threshold

    public TimestampToLocalDateTimeConvert(ZoneId zoneId) {
        this.zoneId = zoneId;
    }

    @Override
    public LocalDateTime convert(Long source) {
        // 将时间戳转为本地时间
        if (source < 1) {
            return null;
        }

        // 判断是秒还是毫秒
        if (source < MILLISECOND_THRESHOLD) {
            return LocalDateTime.ofInstant(Instant.ofEpochSecond(source), zoneId);
        } else {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(source), zoneId);
        }
    }

    @Override
    public <U> Converter<Long, U> andThen(Converter<? super LocalDateTime, ? extends U> after) {
        return Converter.super.andThen(after);
    }
}