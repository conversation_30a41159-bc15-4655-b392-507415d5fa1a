package me.supernova.common.web.filter;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.supernova.common.core.constant.MdcConstants;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

public class TraceIdFilter extends OncePerRequestFilter implements Ordered {
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String traceId = IdUtil.fastSimpleUUID();
        // 将 traceId 放入 MDC 中
        MDC.put(MdcConstants.TRACE_ID, traceId);

        response.setHeader(MdcConstants.TRACE_ID, traceId);

        String uuid = request.getHeader(MdcConstants.UUID);
        if (StrUtil.isNotBlank(uuid)) {
            MDC.put(MdcConstants.UUID, uuid);
        }

        try {

            filterChain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }

    @Override
    public int getOrder() {
        return HIGHEST_PRECEDENCE + 1;
    }
}
