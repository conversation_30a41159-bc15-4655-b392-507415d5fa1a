version: '3.8'

services:
  dashboard:
    image: 872515268479.dkr.ecr.us-east-1.amazonaws.com/pixelab-hk/pixelab-dasboard:test
    environment:
      - APP_ENV=test
      - EXTRA_JVM_CONFIG=-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:19031
    network_mode: host
    restart: unless-stopped
    logging:
      driver: json-file
      options:
        max-size: 100m
        max-file: 1
    volumes:
      - ${PIXELAB_HOME}/logs:/app/logs
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080/actuator/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s