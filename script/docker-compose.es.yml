services:
  es01:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.22
    container_name: es01
    environment:
      - node.name=es01
      - cluster.name=es-cluster
      # 关键改动：指定完整的主机和端口列表
      - discovery.seed_hosts=127.0.0.1:9300,127.0.0.1:9301,127.0.0.1:9302
      - cluster.initial_master_nodes=es01,es02,es03
      - bootstrap.memory_lock=true
      - xpack.security.enabled=false
      - xpack.security.http.ssl.enabled=false
      - ELASTIC_PASSWORD=YourSecurePassword123
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      # 指定 es01 的 HTTP 和 Transport 端口
      - http.port=9201
      - transport.port=9300
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - es01-data:/usr/share/elasticsearch/data
    # 在 host 模式下，ports 映射是多余的，应移除
    network_mode: host
    healthcheck:
      # 修正：添加认证，放宽状态检查
      test: ["CMD-SHELL", "curl -s -u elastic:YourSecurePassword123 http://localhost:9201/_cluster/health | grep -q '\"status\":\"\\(green\\|yellow\\)\"'"]
      interval: 30s
      timeout: 10s
      retries: 5

  es02:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.22
    container_name: es02
    environment:
      - node.name=es02
      - cluster.name=es-cluster
      # 关键改动：和 es01 保持一致
      - discovery.seed_hosts=127.0.0.1:9300,127.0.0.1:9301,127.0.0.1:9302
      - cluster.initial_master_nodes=es01,es02,es03
      - bootstrap.memory_lock=true
      - xpack.security.enabled=false
      - xpack.security.http.ssl.enabled=false
      - ELASTIC_PASSWORD=YourSecurePassword123
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      # 指定 es02 的 HTTP 和 Transport 端口，必须与 es01 不同
      - http.port=9202
      - transport.port=9301
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - es02-data:/usr/share/elasticsearch/data
    network_mode: host
    healthcheck:
      # 修正：使用 es02 的端口进行检查
      test: ["CMD-SHELL", "curl -s -u elastic:YourSecurePassword123 http://localhost:9202/_cluster/health | grep -q '\"status\":\"\\(green\\|yellow\\)\"'"]
      interval: 30s
      timeout: 10s
      retries: 5

  es03:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.22
    container_name: es03
    environment:
      - node.name=es03
      - cluster.name=es-cluster
      # 关键改动：和 es01、es02 保持一致
      - discovery.seed_hosts=127.0.0.1:9300,127.0.0.1:9301,127.0.0.1:9302
      - cluster.initial_master_nodes=es01,es02,es03
      - bootstrap.memory_lock=true
      - xpack.security.enabled=false
      - xpack.security.http.ssl.enabled=false
      - ELASTIC_PASSWORD=YourSecurePassword123
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      # 指定 es03 的 HTTP 和 Transport 端口，必须与 es01、es02 不同
      - http.port=9203
      - transport.port=9302
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - es03-data:/usr/share/elasticsearch/data
    network_mode: host
    healthcheck:
      # 修正：使用 es03 的端口进行检查
      test: ["CMD-SHELL", "curl -s -u elastic:YourSecurePassword123 http://localhost:9203/_cluster/health | grep -q '\"status\":\"\\(green\\|yellow\\)\"'"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  es01-data:
    driver: local
  es02-data:
    driver: local
  es03-data:
    driver: local