#!/bin/bash

# 设置环境变量
export PIXELAB_HOME="${PIXELAB_HOME:-$HOME/pixelab}"
export DOCKER_IMAGE="872515268479.dkr.ecr.us-east-1.amazonaws.com/pixelab-hk/pixelab-dasboard:test"
export LOG_FILE="${PIXELAB_HOME}/deploy.log"
export STACK_NAME="pixelab"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
error_exit() {
    log "错误: $1"
    exit 1
}

# 创建必要的目录
create_directories() {
    log "创建必要的目录..."
    mkdir -p "${PIXELAB_HOME}/logs" || error_exit "无法创建日志目录"
    log "目录创建成功"
}

# 初始化 Swarm
init_swarm() {
    log "初始化 Docker Swarm..."
    if ! docker info | grep -q "Swarm: active"; then
        docker swarm init || error_exit "Swarm 初始化失败"
        log "Swarm 初始化成功"
    else
        log "Swarm 已经初始化"
    fi
}

# 清理网络
cleanup_networks() {
    log "清理未使用的网络..."
    docker network prune -f || error_exit "网络清理失败"
    log "网络清理完成"
}

# 健康检查
health_check() {
    local port=$1
    log "执行健康检查 (端口: $port)..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$port/health" > /dev/null; then
            log "服务健康检查通过 (端口: $port)"
            return 0
        fi
        log "等待服务就绪... 尝试 $attempt/$max_attempts (端口: $port)"
        sleep 5
        attempt=$((attempt + 1))
    done
    
    return 1
}

# 部署服务
deploy() {
    log "开始部署服务..."
    
    # 拉取最新镜像
    log "拉取最新镜像..."
    docker pull "$DOCKER_IMAGE" || error_exit "镜像拉取失败"
    
    # 清理旧网络
    cleanup_networks
    
    # 部署服务栈
    log "部署服务栈..."
    docker stack deploy -c docker-compose.service.yml $STACK_NAME || error_exit "服务栈部署失败"
    
    # 等待服务健康检查
    if ! health_check 8080; then
        error_exit "服务健康检查失败"
    fi
    
    log "部署完成"
}

# 停止所有服务
stop() {
    log "停止所有服务..."
    docker stack rm $STACK_NAME || error_exit "服务栈移除失败"
    cleanup_networks
    log "所有服务已停止"
}

# 更新服务（零停机更新）
update() {
    log "开始零停机更新服务..."
    
    # 拉取新镜像
    log "拉取最新镜像..."
    docker pull "$DOCKER_IMAGE" || error_exit "镜像拉取失败"
    
    # 更新服务
    log "更新服务..."
    docker service update --image $DOCKER_IMAGE ${STACK_NAME}_dashboard || error_exit "服务更新失败"
    
    # 等待服务健康检查
    if ! health_check 8080; then
        log "服务健康检查失败，开始回滚..."
        rollback
        error_exit "服务更新失败"
    fi
    
    log "更新完成"
}

# 回滚服务
rollback() {
    log "开始回滚服务..."
    
    # 检查是否有备份版本
    if ! docker images | grep -q "${DOCKER_IMAGE}_backup"; then
        error_exit "没有找到备份版本，无法回滚"
    fi
    
    # 恢复备份版本
    log "恢复备份版本..."
    docker tag "${DOCKER_IMAGE}_backup" "$DOCKER_IMAGE" || error_exit "恢复备份版本失败"
    
    # 回滚服务
    log "回滚服务..."
    docker service update --image $DOCKER_IMAGE ${STACK_NAME}_dashboard || error_exit "服务回滚失败"
    
    # 等待服务健康检查
    if ! health_check 8080; then
        error_exit "服务回滚后健康检查失败"
    fi
    
    log "回滚完成"
}

# 查看服务状态
status() {
    log "查看服务状态..."
    echo "服务栈状态："
    docker stack ps $STACK_NAME
    
    echo -e "\n服务详情："
    docker service ls | grep $STACK_NAME
    
    echo -e "\n容器资源使用情况："
    docker stats --no-stream
}

# 清理功能
cleanup() {
    log "清理未使用的 Docker 资源..."
    docker system prune -f || error_exit "Docker 清理失败"
    cleanup_networks
    log "清理完成"
}

# 主菜单
case "$1" in
    "deploy")
        create_directories
        init_swarm
        deploy
        ;;
    "stop")
        stop
        ;;
    "update")
        update
        ;;
    "rollback")
        rollback
        ;;
    "status")
        status
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "用法: $0 {deploy|stop|update|rollback|status|cleanup}"
        exit 1
        ;;
esac 