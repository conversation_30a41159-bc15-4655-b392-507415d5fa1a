version: '3.8'

services:
  postgres:
    image: postgres:17
    container_name: pixelab-postgres
    environment:
      POSTGRES_USER: pixelab
      POSTGRES_PASSWORD: lkasjdfjknwunf283748
      POSTGRES_DB: pixelab
      TZ: Asia/Shanghai
      PGTZ: Asia/Shanghai
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - postgres_init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - pixelab-network

  redis:
    image: redis:7
    container_name: pixelab-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - pixelab-network

volumes:
  postgres_data:
  postgres_init:
  redis_data:

networks:
  pixelab-network:
    driver: bridge