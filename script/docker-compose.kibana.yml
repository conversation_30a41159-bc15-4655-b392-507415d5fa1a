services:
  kibana:
    image: docker.elastic.co/kibana/kibana:7.17.22
    container_name: kibana
    environment:
      # Use the specific HTTP port of one of your Elasticsearch nodes
      # Since you are using host network mode for Elasticsearch,
      # <PERSON><PERSON> should be able to reach it via localhost and the specified port.
      - ELASTICSEARCH_HOSTS=http://localhost:9201
      # 由于 Elasticsearch 禁用了安全功能，这里不需要用户名和密码
      # - ELASTICSEARCH_USERNAME=elastic
      # - ELASTICSEARCH_PASSWORD=YourSecurePassword123
    ports:
      - "5601:5601" # Expose Kibana on port 5601
    network_mode: host
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:5601/api/status | grep -q '\"state\":\"green\"'"]
      interval: 10s
      timeout: 5s
      retries: 5
