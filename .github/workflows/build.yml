# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-maven

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Pixelab Dashboard CI/CD

on:
  push:
    branches: [ "master" ]

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read

    steps:
      - uses: actions/checkout@v4

      - name: 设置 JDK 环境
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: 'temurin'
          cache: maven

      - name: Maven 构建
        run: mvn -B package --file pom.xml -DskipTests

      - name: 获取当前时间
        id: datetime
        run: echo "datetime=$(TZ='Asia/Shanghai' date +'%Y%m%d-%H%M%S')" >> $GITHUB_OUTPUT

      - name: 配置 AWS 凭证
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_IAM_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: 登录到 Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: 处理仓库名称
        id: repo-name
        run: echo "repo_name=$(echo ${{ github.repository }} | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT

      - name: 构建并推送 Docker 镜像
        id: build-image
        uses: docker/build-push-action@v3
        with:
          context: ./nova-dashboard
          push: true
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ steps.repo-name.outputs.repo_name }}:latest
            ${{ steps.login-ecr.outputs.registry }}/${{ steps.repo-name.outputs.repo_name }}:${{ github.ref_name }}-${{ steps.datetime.outputs.datetime }}
          file: ./nova-dashboard/Dockerfile

      - name: 发送飞书通知 - 成功
        if: success()
        uses: Rollingegg/feishu-robot-action@v1
        with:
          uuid: ${{ secrets.FEISHU_ROBOT_UUID }}
          json: |
            {"msg_type":"interactive","card":{"config":{"wide_screen_mode":true,"enable_forward":true},"header":{"template":"green","title":{"content":"🚀 Nova Dashboard 构建成功","tag":"plain_text"}},"elements":[{"tag":"div","text":{"tag":"lark_md","content":"** 📦 构建信息**\n\n**分支**: ${{ github.ref_name }}\n**提交**: ${{ github.sha }}\n**提交信息**: ${{ github.event.head_commit.message }}\n**触发者**: ${{ github.actor }}"}},{"tag":"div","fields":[{"is_short":true,"text":{"tag":"lark_md","content":"**🕒 构建时间**\n${{ steps.datetime.outputs.datetime }}"}},{"is_short":true,"text":{"tag":"lark_md","content":"**📌 构建状态**\n✅ 成功"}}]},{"tag":"div","text":{"tag":"lark_md","content":"**📋 镜像信息**\n${{ steps.login-ecr.outputs.registry }}/${{ steps.repo-name.outputs.repo_name }}:${{ github.ref_name }}-${{ steps.datetime.outputs.datetime }}"}},{"tag":"action","actions":[{"tag":"button","text":{"tag":"plain_text","content":"查看构建详情"},"type":"primary","multi_url":{"url":"${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}","pc_url":"${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}","android_url":"${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}","ios_url":"${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"}}]},{"tag":"note","elements":[{"tag":"plain_text","content":"此消息由 GitHub Actions 自动发送"}]}]}}

      - name: 发送飞书通知 - 失败
        if: failure()
        uses: Rollingegg/feishu-robot-action@v1
        with:
          uuid: ${{ secrets.FEISHU_ROBOT_UUID }}
          json: |
            {"msg_type":"interactive","card":{"config":{"wide_screen_mode":true,"enable_forward":true},"header":{"template":"red","title":{"content":"❌ Nova Dashboard 构建失败","tag":"plain_text"}},"elements":[{"tag":"div","text":{"tag":"lark_md","content":"** 📦 构建信息**\n\n**分支**: ${{ github.ref_name }}\n**提交**: ${{ github.sha }}\n**提交信息**: ${{ github.event.head_commit.message }}\n**触发者**: ${{ github.actor }}"}},{"tag":"div","fields":[{"is_short":true,"text":{"tag":"lark_md","content":"**🕒 构建时间**\n${{ steps.datetime.outputs.datetime }}"}},{"is_short":true,"text":{"tag":"lark_md","content":"**📌 构建状态**\n❌ 失败"}}]},{"tag":"div","text":{"tag":"lark_md","content":"**❌ 失败原因**\n构建或部署过程中出现错误"}},{"tag":"action","actions":[{"tag":"button","text":{"tag":"plain_text","content":"查看失败详情"},"type":"danger","multi_url":{"url":"${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}","pc_url":"${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}","android_url":"${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}","ios_url":"${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"}}]},{"tag":"note","elements":[{"tag":"plain_text","content":"此消息由 GitHub Actions 自动发送"}]}]}}